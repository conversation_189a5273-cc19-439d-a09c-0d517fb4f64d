﻿using System;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;

public class UpdateOrganizationOperatorGeneralCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string OperatorId { get; }
    public string FirstName { get; set; } = "";
    public string LastName { get; set; } = "";
    public string Email { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public int YearsOfExperience { get; set; } = 0;
    public int YearsOfRemoteExperience { get; set; } = 0;
    public WeekDaysIds WeekDays { get; set; } = WeekDaysIds.Monday | WeekDaysIds.Tuesday | WeekDaysIds.Wednesday | WeekDaysIds.Thursday | WeekDaysIds.Friday;
    public DateTime RegularStartTime { get; set; } = DateTime.Today.AddHours(9);
    public DateTime RegularEndTime { get; set; } = DateTime.Today.AddHours(17);
    public string Biography { get; set; } = "";

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOrganizationOperatorGeneralCommand(string operatorId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.OperatorId = operatorId;
    }
    #endregion
}