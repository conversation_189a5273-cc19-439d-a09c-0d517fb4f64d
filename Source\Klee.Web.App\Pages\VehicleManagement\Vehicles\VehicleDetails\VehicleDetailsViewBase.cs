﻿using DevExpress.Charts.Native;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails.Data;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleUpdateGeneral;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails
{
    public partial class VehicleDetailsViewBase : LayoutBodyDetailsViewBase<VehicleDetailsViewModel, UserSessionData>
    {
        #region PROPERTIES
        [Parameter]
        public string VehicleIdEncoded { get; set; }
        public string VehicleId => Base64UrlEncoder.Decode(this.VehicleIdEncoded);

        protected string PageHeaderTitle { get; private set; } = "Vehicle :                                      ";

        // Render Support
        private bool AreTabContentItemGeneralChartsRendered { get; set; }
        private bool AreTabContentItemYearOverviewChartsRendered { get; set; }

        // Tab Content Items
        protected RTabContentItem TabContentItemGeneral { get; set; }
        protected RTabContentItem TabContentItemYearOverview { get; set; }

        // Dialog Views
        protected VehicleUpdateGeneralDialogView VehicleUpdateGeneralDialogView { get; set; }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new VehicleDetailsViewModel(this);

            //
            await base.OnInitializedAsync();

            // Set PageHeaderTitle
            this.PageHeaderTitle = this.GetPageHeaderTitle();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();

                // Load Vehicle
                if (await this.ViewModel.LoadVehicleAsync(this.VehicleId))
                {
                    // Set PageHeaderTitle
                    this.PageHeaderTitle = this.GetPageHeaderTitle();
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override Task OnAfterRenderFirstAsync()
        {
            // Set EventHandlers
            this.TabContentItemGeneral.ContentRendered += this.OnTabContentItemGeneral_ContentRendered;
            this.TabContentItemYearOverview.ContentRendered += this.OnTabContentItemYearOverview_ContentRendered;

            return base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
        {
            await base.OnMainLayoutBodyActiveTabChangedAsync(e);

            // Handle Tab Change
            switch (e.ActiveTabId)
            {
                case VehicleDetailsViewTabIds.General:
                    break;

                case VehicleDetailsViewTabIds.YearOverview:
                    break;
            }
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS - SUPPORT
        private string GetPageHeaderTitle()
        {
            return $"Vehicle : {this.VehicleId} ({this.ViewModel.VehicleName})";
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickDelete()
        {
            // Init
            string vehicleId = this.ViewModel?.VehicleId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Vehicle?",
                                                                        $"Delete vehicle with id '{vehicleId}'?") == RDialogResult.Ok)
                {
                    // Delete Vehicle
                    await this.ViewModel!.DeleteVehicleAsync(vehicleId);

                    // Close View
                    await this.CloseViewAsync();

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Vehicle with id '{vehicleId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting vehicle with id '{vehicleId}'", vehicleId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClick_ShowSysEntityInfoDetailsView()
        {
            // Init
            string vehicleId = this.ViewModel?.VehicleId ?? "";

            try
            {
                // Show EntityInfoDetails
                //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.Vehicle);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to vehicle entity view of vehicle '{vehicleId}'", vehicleId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenVehicleUpdateGeneralDialogView()
        {
            try
            {
                await this.VehicleUpdateGeneralDialogView.LoadVehicleAsync(this.VehicleId);
                await this.VehicleUpdateGeneralDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.VehicleUpdateGeneralDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - VEHICLE UPDATE GENERAL DIALOG VIEW
        protected void OnVehicleUpdateGeneralDialogView_Opened()
        {
        }

        protected async Task OnVehicleUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as VehicleUpdateGeneralDialogViewModel;
                    
                    // Load Update
                    await this.ViewModel.LoadVehicleAsync(this.VehicleId, forceLoad:true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnTabNavItemGeneral_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        protected Task OnTabNavItemRealtime_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        protected async Task OnTabNavItemYearOverview_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {

            }
        }

        protected async void OnTabContentItemGeneral_ContentRendered(object sender, EventArgs e)
        {
            if (this.MainLayoutBody.Tabs.ActiveTabId == VehicleDetailsViewTabIds.General &&
                this.AreTabContentItemGeneralChartsRendered == false)
            {
                // Set Indicator
                this.AreTabContentItemGeneralChartsRendered = true;

                // Update charts
                //await this.VehicleSummary01WidgetView.UpdateJavaScriptChartsAsync();
            }
        }

        protected async void OnTabContentItemYearOverview_ContentRendered(object sender, EventArgs e)
        {
            if (this.MainLayoutBody.Tabs.ActiveTabId == VehicleDetailsViewTabIds.YearOverview &&
                this.AreTabContentItemYearOverviewChartsRendered == false)
            {
                // Set Indicator
                this.AreTabContentItemYearOverviewChartsRendered = true;

                // Update charts
                //await this.VehicleSummary02WidgetView.UpdateDevExpressCharts();
                //await this.VehicleSummary02WidgetView.UpdateJavaScriptChartsAsync();
            }
        }
        #endregion
    }
}