
@using AntDesign

<Collapse Class="bg-transparent border-0">
    <Panel Key="@Voyage.Id.ToString()" Class="mb-2 border border-gray-200 rounded-lg bg-white hover:border-teal-700">
        <HeaderTemplate>
            <div class="flex flex-col md:flex-row md:items-center justify-between w-full gap-3">
                <!-- Primary voyage information -->
                <div class="flex flex-col gap-2">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-ship h-4 w-4 text-teal-600"></i>
                        <span class="font-semibold text-gray-900 text-base">@Voyage.VesselName</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetStatusBadgeClass(Voyage.Status)">
                            @GetStatusIcon(Voyage.Status)
                            @GetStatusText(Voyage.Status)
                        </span>
                    </div>

                    <!-- Route and timing information -->
                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-gray-600 ml-7">
                        <div class="flex items-center gap-1">
                            <i class="fas fa-calendar h-3 w-3 text-teal-500"></i>
                            <span>@FormatDateRange(Voyage.DepartureDate, Voyage.ArrivalDate)</span>
                        </div>
                    </div>
                </div>

                <!-- Expand indicator -->
                <div class="flex items-center text-gray-400 md:ml-4">
                    <span class="text-xs text-gray-500 mr-2">View Details</span>
                </div>
            </div>
        </HeaderTemplate>
        <ChildContent>
            <VoyageDetailsContent Voyage="@Voyage" />
        </ChildContent>
    </Panel>
</Collapse>



@code {

    [Parameter]
    public Dashboard.VoyageItem Voyage { get; set; } = new();

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "completed" => "bg-green-100 text-green-800",
            "upcoming" => "bg-blue-100 text-blue-800",
            "active" => "bg-teal-100 text-teal-800",
            _ => "bg-amber-100 text-amber-800"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "completed" => "Completed",
            "upcoming" => "Upcoming",
            "active" => "In Progress",
            _ => "Issues Reported"
        };
    }

    private RenderFragment GetStatusIcon(string status)
    {
        return status switch
        {
            "completed" => @<i class="fas fa-check-circle h-3 w-3 mr-1"></i>,
            "upcoming" => @<i class="fas fa-clock h-3 w-3 mr-1"></i>,
            "active" => @<i class="fas fa-play-circle h-3 w-3 mr-1"></i>,
            _ => @<i class="fas fa-exclamation-triangle h-3 w-3 mr-1"></i>
        };
    }

    private string FormatDateRange(DateTime departure, DateTime arrival)
    {
        var depDate = departure.ToString("dd MMM");
        var depTime = departure.ToString("HH:mm");
        var arrDate = arrival.ToString("dd MMM");
        var arrTime = arrival.ToString("HH:mm");

        // If same day, show: "15 Mar 09:00 - 17:00"
        if (departure.Date == arrival.Date)
        {
            return $"{depDate} {depTime} - {arrTime}";
        }

        // If different days, show: "15 Mar 09:00 - 16 Mar 17:00"
        return $"{depDate} {depTime} - {arrDate} {arrTime}";
    }
}
