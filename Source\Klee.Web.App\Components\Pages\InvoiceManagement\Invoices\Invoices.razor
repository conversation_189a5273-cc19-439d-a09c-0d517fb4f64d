@page "/invoices"

@using Klee.Domain.Entities.InvoiceManagement.Invoices.Data
@using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data
@layout OrganizationViewLayout

@inherits InvoicesViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold text-teal-700">Stats</h1>
</div>

@if (ViewModel.IsLoading == true)
{
    <div class="text-center py-12">
        <Spin Size="SpinSize.Large" />
        <p class="mt-4 text-gray-600">Loading invoices...</p>
    </div>
}
else
{
    <!-- Dashboard Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Voyages Posted This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-ship text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Voyages Posted</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.Statistics.VoyagesPostedThisMonth</p>
                        <p class="text-xs text-gray-500">This month</p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Total Hours This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Hours</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.Statistics.TotalHoursThisMonth.ToString("F1")</p>
                        <p class="text-xs text-gray-500">This month</p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Money Earned This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-euro-sign text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Earned This Month</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.FormatCurrency(ViewModel.Statistics.MoneyEarnedThisMonth)</p>
                        <p class="text-xs text-gray-500">Incoming invoices</p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Total Money Earned -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Earned</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.FormatCurrency(ViewModel.Statistics.TotalMoneyEarned)</p>
                        <p class="text-xs text-gray-500">All time</p>
                    </div>
                </div>
            </div>
        </Card> 
    </div> 

    <!-- Invoice Table -->
    <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-teal-700">Invoices</h1>
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="overflow-x-auto">
            <Table TItem="InvoiceListItem"
                   DataSource="@ViewModel.Invoices"
                   Class="@TailwindStyleStrings.Table.Container"
                   ScrollX="1040">
                
                <PropertyColumn Property="c => c.VesselName" Title="Vessel" Width="200" Sortable Filterable>
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-ship h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span class="font-medium">@context.VesselName</span>
                        </div>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.Duration" Title="Duration" Sortable Width="120">
                    <Template>
                        <span class="text-sm">@ViewModel.FormatDuration(context.Duration)</span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.OperatorFullName" Title="Operator" Sortable Filterable Width="180">
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-user h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span>@context.OperatorFullName</span>
                        </div>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.OperatorOrganizationName" Title="Operator Organization" Filterable Width="200" />

                <PropertyColumn Property="c => c.Status" Title="Status" Sortable Width="100">
                    <Template>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetStatusBadgeClass(context.Status)">
                            @context.Status.ToString()
                        </span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.TotalAmountInEuros" Title="Amount" Sortable Width="120">
                    <Template>
                        <span class="font-medium">@ViewModel.FormatCurrency(context.TotalAmountInEuros)</span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.CreatedDate" Title="Direction" Width="120">
                    <Template>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetDirectionBadgeClass(context)">
                            @ViewModel.GetInvoiceDirection(context)
                        </span>
                    </Template>
                </PropertyColumn>

                <ActionColumn Title="Actions" Width="140">
                    <Template>
                        @if (context.Status == VoyageInvoiceStatus.Open)
                        {
                            @if (context.IsIncoming(ViewModel.CurrentUserOrganizationId))
                            {
                                <!-- Only operator organization can mark as paid (incoming invoices) -->
                                <Button Type="@ButtonType.Default"
                                        Class="@TailwindStyleStrings.Button.Outline"
                                        OnClick="() => ShowMarkAsPaidConfirmation(context.VoyageInvoiceId, context.VesselName)"
                                        Loading="@ViewModel.IsUpdatingStatus">
                                    <i class="fas fa-check mr-1"></i>
                                    Mark as Paid
                                </Button>
                            }
                            else
                            {
                                <!-- Booking organization cannot mark as paid (outgoing invoices) -->
                                <span class="text-xs text-gray-600">
                                    <i class="fas fa-clock mr-1"></i>
                                    Awaiting Payment
                                </span>
                            }
                        }
                        else
                        {
                            <span class="text-xs text-gray-500">
                                <i class="fas fa-check-circle mr-1"></i>
                                Paid @context.PaymentDate?.ToString("MMM dd")
                            </span>
                        }
                    </Template>
                </ActionColumn>
            </Table>
        </div>
    </Card> 
}

<!-- Mark as Paid Confirmation Modal -->
 <Modal Title="Confirm Payment"
       @bind-Visible="ShowMarkAsPaidModal"
       OnOk="ConfirmMarkAsPaid"
       OnCancel="CancelMarkAsPaid"
       OkText=@("Mark as Paid")
       CancelText=@("Cancel")>
    <p>@SelectedInvoiceDescription</p>
    <p class="text-sm text-gray-600 mt-2">This action cannot be undone.</p>
</Modal> 

@code {

    public static string GetUri()
    {
        return "/invoices";
    }

}  