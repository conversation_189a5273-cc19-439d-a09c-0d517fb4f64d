@layout OrganizationViewLayout
@using AntDesign
@using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data
@using Klee.Domain.Entities.VoyageManagement.Voyages.Data
@using Klee.Web.App.Components.Pages.InvoiceManagement.Invoices

@page "/my-assets/voyages"

@inherits VoyagesViewBase

<div class="container py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">My Voyages</h1>

        <div class="flex items-center gap-3">
            <Button Type="@ButtonType.Default"
                    Class="@TailwindStyleStrings.Button.Secondary"
                    Icon="@IconType.Outline.Dollar"
                    OnClick="@(() => this.NavigationManager.NavigateTo(Invoices.GetUri()))">
                View Invoices
            </Button>
            <Button Type="@ButtonType.Default"
                    Class="@TailwindStyleStrings.Button.Secondary"
                    Icon="@IconType.Outline.Reload"
                    OnClick="@OnClickRefresh">
                Refresh
            </Button>
        </div>
    </div>

    @if (!ViewModel.Voyages.Any())
    {
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="text-center py-12">
                <i class="fas fa-ship text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Voyages Found</h3>
                <p class="text-gray-500">No voyages have been found for your organization.</p>
            </div>
        </Card>
    }
    else
    {
        <Card Class="@TailwindStyleStrings.Card.Container">
            <Table TItem="VoyageManagementListItem"
                       DataSource="@ViewModel.Voyages"
                       Class="@TailwindStyleStrings.Table.Container"
                       ExpandIconColumnIndex="0"
                       RowExpandable="@(record => true)">
                    <ColumnDefinitions>
                        <PropertyColumn Property="@(v => v.DateRangeDisplay)" Title="Date & Time" Sortable Width="180">
                            <Template>
                                <div class="flex flex-col">
                                    <span class="font-medium text-gray-900">@context.DateRangeDisplay</span>
                                    <span class="text-xs text-gray-500">Duration: @context.DurationDisplay</span>
                                </div>
                            </Template>
                        </PropertyColumn>
                        <PropertyColumn Property="@(v => v.VesselDisplayName)" Title="Vessel" Sortable Filterable Width="220">
                            <Template>
                                <div class="flex items-center gap-2">
                                    <i class="@($"fas fa-ship h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                    <div class="flex flex-col">
                                        <span class="font-medium">@context.VehicleName</span>
                                        <span class="text-xs text-gray-500">@context.VehicleOrganizationName</span>
                                    </div>
                                </div>
                            </Template>
                        </PropertyColumn>
                        <PropertyColumn Property="@(v => v.OperatorDisplayNameWithOrg)" Title="Captain" Sortable Filterable Width="220">
                            <Template>
                                @if (!string.IsNullOrEmpty(context.OperatorId))
                                {
                                    <div class="flex items-center gap-2">
                                        <i class="@($"fas fa-user h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <div class="flex flex-col">
                                            <span class="font-medium">@context.OperatorDisplayName</span>
                                            <span class="text-xs text-gray-500">@context.OperatorOrganizationName</span>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <span class="text-gray-500 italic">No captain assigned</span>
                                }
                            </Template>
                        </PropertyColumn>
                        <PropertyColumn Property="@(v => v.VoyageType)" Title="Type" Sortable Width="100">
                            <Template>
                                @if (context.VoyageType == VoyageTypeIds.Outgoing)
                                {
                                    <Tag Color="TagColor.Blue" Class="flex items-center gap-1">
                                        <i class="fas fa-arrow-right text-xs"></i>
                                        Outgoing
                                    </Tag>
                                }
                                else
                                {
                                    <Tag Color="TagColor.Green" Class="flex items-center gap-1">
                                        <i class="fas fa-arrow-left text-xs"></i>
                                        Incoming
                                    </Tag>
                                }
                            </Template>
                        </PropertyColumn>
                    </ColumnDefinitions>
                    <ExpandTemplate Context="voyage">
                        <div class="px-8 py-6 bg-gray-50">
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                <!-- Voyage Details -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Voyage Details</h4>
                                    <div class="bg-white rounded-lg p-4 shadow-sm">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-1">Description</p>
                                                @if (!string.IsNullOrWhiteSpace(voyage.Data.Description))
                                                {
                                                    <p class="text-sm text-gray-900">@voyage.Data.Description</p>
                                                }
                                                else
                                                {
                                                    <p class="text-sm text-gray-500 italic">No description provided</p>
                                                }
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Duration</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.DurationDisplay</p>
                                            </div>
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-2">Required Qualifications</p>
                                                @if (voyage.Data.RequiredQualifications?.Any() == true)
                                                {
                                                    <div class="flex flex-wrap gap-1">
                                                        @foreach (var qualification in voyage.Data.RequiredQualifications)
                                                        {
                                                            <span class="text-xs font-medium text-teal-800 px-2 py-1 bg-teal-100 rounded">
                                                                @qualification.ToString()
                                                            </span>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <p class="text-sm text-gray-500 italic">No specific qualifications required</p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Vessel Information -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Vessel Information</h4>
                                    <div class="bg-white rounded-lg p-4 shadow-sm">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Name</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleName</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">ENI</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleENI</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Type</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleTypeDisplayName</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Hourly Rate</p>
                                                <p class="text-sm text-gray-900">€@voyage.Data.VehicleHourlyRateInEuros.ToString("N2")</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Length</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleLength.ToString("F1")m</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Beam</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleBeam.ToString("F1")m</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Captain Details -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Captain Details</h4>
                                    <div class="bg-white rounded-lg p-4 shadow-sm">
                                        @if (!string.IsNullOrEmpty(voyage.Data.OperatorId))
                                        {
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <p class="text-sm font-medium text-teal-700 mb-1">Name</p>
                                                    <p class="text-sm text-gray-900">@voyage.Data.OperatorDisplayName</p>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-medium text-teal-700 mb-1">Email</p>
                                                    <p class="text-sm text-gray-900">@voyage.Data.OperatorEmail</p>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-medium text-teal-700 mb-1">Experience</p>
                                                    <p class="text-sm text-gray-900">@voyage.Data.OperatorYearsOfExperience years</p>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-medium text-teal-700 mb-1">Hourly Rate</p>
                                                    <p class="text-sm text-gray-900">€@voyage.Data.OperatorHourlyRateInEuros.ToString("N2")</p>
                                                </div>
                                                @if (!string.IsNullOrWhiteSpace(voyage.Data.OperatorBiography))
                                                {
                                                    <div class="md:col-span-2">
                                                        <p class="text-sm font-medium text-teal-700 mb-1">Biography</p>
                                                        <p class="text-sm text-gray-900 leading-relaxed">@voyage.Data.OperatorBiography</p>
                                                    </div>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="text-center py-4">
                                                <i class="fas fa-user-slash text-2xl text-gray-400 mb-2"></i>
                                                <p class="text-sm text-gray-500 italic">No captain assigned to this voyage</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ExpandTemplate>
                </Table>
        </Card>
    }
</div>

@code {
    #region METHODS - STATIC
    public static string GetUri()
    {
        return "/my-assets/voyages";
    }
    #endregion
}
