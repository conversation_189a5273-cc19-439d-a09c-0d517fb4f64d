using System.ComponentModel.DataAnnotations;
using AntDesign;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Services;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class PlanVoyageComponentViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public EventCallback<(string, DateTime, DateTime, IEnumerable<QualificationTypeIds>, string)> OnClick_PlanVoyage { get; set; }
    [Parameter] public EventCallback OnClick_Back { get; set; }

    // Parameters for form data restoration
    [Parameter] public string InitialVesselId { get; set; } = "";
    [Parameter] public DateTime? InitialStartDateTime { get; set; }
    [Parameter] public DateTime? InitialEndDateTime { get; set; }
    [Parameter] public IEnumerable<QualificationTypeIds> InitialQualifications { get; set; } = new List<QualificationTypeIds>();
    [Parameter] public string InitialDescription { get; set; } = "";
    #endregion

    #region PROPERTIES
    public PlanVoyageViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new PlanVoyageViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Restore form data after components are fully rendered and vessel data is loaded
            // This ensures the Select component has the vessel options available to match IDs to labels
            if (!IsLoading && ViewModel.OrganizationVessels.Any())
            {
                RestoreFormData();
                StateHasChanged();
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }
    #endregion

    #region METHODS - DATA LOADING
    protected async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StateHasChanged();

            await ViewModel.LoadOrganizationVesselsAsync();
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load vessels. Please try again.",
                Duration = 4.5
            });
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    #endregion

    #region METHODS - FORM DATA RESTORATION
    protected void RestoreFormData()
    {

        // Restore vessel selection - only if vessel data is loaded and vessel exists in the list
        if (!string.IsNullOrEmpty(InitialVesselId) && ViewModel.OrganizationVessels?.Any() == true)
        {
            // Verify the vessel ID exists in the loaded vessel list
            if (ViewModel.OrganizationVessels.Any(v => v.VehicleId == InitialVesselId))
            {
                ViewModel.SelectedVesselId = InitialVesselId;
            }
        }

        // Restore date/time selections
        if (InitialStartDateTime.HasValue)
        {
            ViewModel.StartDateTime = InitialStartDateTime.Value;
        }

        if (InitialEndDateTime.HasValue)
        {
            ViewModel.EndDateTime = InitialEndDateTime.Value;
        }

        // Restore qualifications
        if (InitialQualifications.Any())
        {
            ViewModel.SelectedQualifications = InitialQualifications;
        }

        // Restore description
        if (!string.IsNullOrEmpty(InitialDescription))
        {
            ViewModel.Description = InitialDescription;
        }
    }

    #endregion

    #region METHODS - FORM HANDLING
    protected async Task HandleSubmit()
    {
        if (IsSubmitting)
            return;

        try
        {
            IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();

            List<ValidationResult> validationResult = (await ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Pass form data to parent component
                await OnClick_PlanVoyage.InvokeAsync((ViewModel.SelectedVesselId, ViewModel.StartDateTime, ViewModel.EndDateTime, ViewModel.SelectedQualifications, ViewModel.Description));
            }
            else
            {
                foreach (ValidationResult result in validationResult)
                {
                    if (result.ErrorMessage != null)
                    {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            ValidationErrors.Add("An error occurred while processing your request. Please try again.");
        }
        finally
        {
            IsSubmitting = false;
            StateHasChanged();
        }
    }

    protected async Task NavigateToDashboard()
    {
        await OnClick_Back.InvokeAsync();
    }
    #endregion
}
