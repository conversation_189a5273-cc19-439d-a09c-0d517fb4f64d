using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Services;

namespace Klee.Web.App.Components.Pages.VoyageManagement.FindVoyage;

public class FindVoyageViewModel
{
    #region PROPERTIES
    private readonly ISrpProcessors _srpProcessors;
    
    public List<OpenVoyageListItem> OpenVoyages { get; set; } = new();
    public bool IsLoading { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public FindVoyageViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task LoadOpenVoyagesAsync(bool forceLoad = false)
    {
        if (IsLoading && !forceLoad)
            return;

        IsLoading = true;

        try
        {
            var query = new GetOpenVoyagesQuery(await _srpProcessors.GetQueryContextAsync())
            {
                IsActive = true
            };

            var result = await _srpProcessors.QueryProcessor.ExecuteAsync(query);
            OpenVoyages = result.ToList();
        }
        finally
        {
            IsLoading = false;
        }
    }
    #endregion
}
