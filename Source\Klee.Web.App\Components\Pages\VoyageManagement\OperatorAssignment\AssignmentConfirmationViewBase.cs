using System;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyageManagement.OperatorAssignment;

public class AssignmentConfirmationViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public OpenVoyageListItem VoyageData { get; set; }
    [Parameter] public AvailableCaptainListItem SelectedOperator { get; set; }
    [Parameter] public EventCallback OnClick_Back { get; set; }
    [Parameter] public EventCallback OnAssignmentConfirmed { get; set; }
    #endregion

    #region PROPERTIES
    protected AssignmentConfirmationViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new AssignmentConfirmationViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ViewModel != null && VoyageData != null && SelectedOperator != null)
        {
            ViewModel.SetAssignmentData(VoyageData, SelectedOperator);
            IsLoading = false;
            StateHasChanged();
        }

        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - EVENT HANDLERS
    protected async Task HandleOnClick_Back()
    {
        await OnClick_Back.InvokeAsync();
    }

    protected async Task HandleConfirmAssignment()
    {
        try
        {
            // Get current user's organization
            string userOrgId = await UserAuthenticationService.GetCurrentUserOrganizationIdAsync();
            
            // Assign operator and create invoice
            bool success = await ViewModel.AssignOperatorAsync(userOrgId);
            
            if (success)
            {
                NotificationService.Success(new NotificationConfig()
                {
                    Message = "Operator Assigned",
                    Description = $"Operator {SelectedOperator?.FullName} has been successfully assigned to the voyage!",
                    Duration = 4.5
                });

                await OnAssignmentConfirmed.InvokeAsync();
            }
            else
            {
                // Check if it was a concurrency conflict
                if (ViewModel.HasConcurrencyConflict)
                {
                    NotificationService.Error(new NotificationConfig()
                    {
                        Message = "Operator No Longer Available",
                        Description = ViewModel.ConcurrencyErrorMessage,
                        Duration = 6.0
                    });
                }
                else
                {
                    NotificationService.Error(new NotificationConfig()
                    {
                        Message = "Assignment Failed",
                        Description = "Failed to assign operator to voyage. Please try again.",
                        Duration = 4.5
                    });
                }

                ViewModel.ClearConcurrencyConflict();
                await OnClick_Back.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "An unexpected error occurred while assigning the operator. Please try again.",
                Duration = 4.5
            });
        }
    }
    #endregion
}
