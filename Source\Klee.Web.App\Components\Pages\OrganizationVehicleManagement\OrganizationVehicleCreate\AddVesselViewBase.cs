﻿using System.ComponentModel.DataAnnotations;
using AntDesign;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;
using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleCreate;

public class AddVesselViewBase: ComponentBase {
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<AddVesselViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
  
    #endregion

    #region PROPERTIES
    protected AddVesselViewModel ViewModel { get; set; } 
    protected List<string> ValidationErrors { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();
            
        if (!isUserOrganizationAdmin)
        {
            // Redirect to operators list if not admin
            NavigationManager.NavigateTo(Vessels.GetUri());
            return;
        }
        // Init
        this.ViewModel = new AddVesselViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }
    #endregion

    #region METHODS
    protected void NavigateToVehicleList()
    {
        // Clear ViewModel
        this.ViewModel.Clear();
        NavigationManager.NavigateTo(Vessels.GetUri());
    }

    #endregion

    #region EVENT HANDLERS
    protected async Task HandleSubmit()
    {
        try
        {
            ValidationErrors.Clear();
            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Create OrganizationVehicle
                await this.ViewModel.CreateOrganizationVehicleAsync();

                //Notify - don't await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Created",
                    Description = $"Vessel '{ViewModel.VehicleName}' has been successfully created.",
                    Duration = 4.5
                });

                NavigateToVehicleList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Error on creating vehicle.");
            #endregion

            // Notify - don't await this call to not block the UI
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Created",
                Description = $"Error on creating vehicle.",
                Duration = 4.5
            });

            ValidationErrors.Add($"An error occurred while creating the vessel: {exception.Message}");
        }
    }

    #endregion
}