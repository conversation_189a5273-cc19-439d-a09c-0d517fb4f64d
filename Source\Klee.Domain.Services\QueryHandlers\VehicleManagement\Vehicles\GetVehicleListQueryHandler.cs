﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.VehicleManagement;
using EnumsNET;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VehicleManagement.Vehicles
{
    public sealed class GetVehicleListQueryHandler
        : QueryHandlerAsync<GetVehicleListQuery, IReadOnlyList<VehicleListItem>>
    {
        #region PROPERTIES
        private IVehicleSrpRepository VehicleSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetVehicleListQueryHandler(IVehicleSrpRepository vehicleSrpRepository,
                                          IMemoryCache memoryCache)
        {
            this.VehicleSrpRepository = vehicleSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<VehicleListItem>> ExecuteAsync(GetVehicleListQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            // Get VehicleListItems from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetVehicleListCacheId, out List<VehicleListItem> cachedVehicleListItems);

            // Init
            List<VehicleListItem> vehicleListItems = cachedVehicleListItems ?? new List<VehicleListItem>();

            // Get VehicleListItems from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get VehicleListItems
                vehicleListItems =
                    await this.VehicleSrpRepository.Entities(query)
                              .OrderBy(_ => _.VehicleId)
                              .Select(_ => new VehicleListItem()
                                           {
                                               EntityId = _.EntityId,
                                               VehicleTypeDisplayName = _.VehicleTypeId.AsString(EnumFormat.DisplayName),
                                               VehicleId = _.VehicleId,
                                               VehicleName = _.VehicleName,
                                               ENI = _.ENI,
                                               HourlyRateInEuros = _.HourlyRateInEuros,
                                               OrganizationId = _.OrganizationId,
                                               IsActive = _.IsActive ?? false
                                           })
                              .ToListAsync(cancellationToken: cancellationToken);

                // Cache VehicleListItems
                this.MemoryCache.Set(MemoryCacheIds.GetVehicleListCacheId,
                                     vehicleListItems, new MemoryCacheEntryOptions()
                                                       {
                                                           AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                           Size = 1
                                                       });
            }

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                vehicleListItems = vehicleListItems.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return vehicleListItems;
        }
        #endregion
    }
}