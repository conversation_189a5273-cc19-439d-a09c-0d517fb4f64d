﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Services.CommandHandlers.StationManagement.Stations.Helpers;
using Klee.Domain.Services.Repositories.StationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.StationManagement.Stations;

public sealed class DeleteStationCommandHandler
    : RequestHandlerAsync<DeleteStationCommand>
{
    #region PROPERTIES
    private IStationSrpRepository StationSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public DeleteStationCommandHandler(IStationSrpRepository stationSrpRepository,
        IMemoryCache memoryCache)
    {
        StationSrpRepository = stationSrpRepository;
        MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    public override async Task<DeleteStationCommand> HandleAsync(DeleteStationCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Find Station
        Station Station = await StationSrpRepository.FindAsync(_ => _.StationId == command.StationId &&
                                                                    _.EntityPartitionKey == command.StationId, command);

        if (Station != null)
        {
            // Set IsActive to false (soft delete)
            Station.IsActive = false;

            // Update
            await StationSrpRepository.UpdateAsync(Station, command);

            // Clear Caches
            MemoryCache.RemoveStation(command.StationId);
        }
        else
        {
            throw new EntityNotFoundException(
                $"Station with Station id '{command.StationId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }
    #endregion
}