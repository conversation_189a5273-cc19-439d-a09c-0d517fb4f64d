using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorCreate;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Renoir.Web.Razor.Services.UserNotifications;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;
using AntDesign;
using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorEdit;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList;

public class OperatorsViewBase: ComponentBase {
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private IRUserNotificationService UserNotificationService { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<OperatorsViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }

    #endregion

    #region PROPERTIES
    protected OperatorsViewModel ViewModel { get; set; }

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;

    // Delete modal properties
    protected bool DeleteModalVisible { get; set; } = false;
    protected string CurrentOperatorId { get; set; } = "";
    protected string CurrentOperatorName { get; set; } = "";
    #endregion


    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync() {
        // Init
        this.ViewModel = new OperatorsViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Debug logging to help troubleshoot
            this.Logger.LogInformation("User authentication check - IsUserOrganizationAdmin: {IsUserOrganizationAdmin}",
                this.IsUserOrganizationAdmin);

            // Load OrganizationOperators
            await this.ViewModel.LoadOrganizationOperatorsAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

        }
    }
    #endregion

    #region METHODS - DELETE
    protected void OnClick_Delete(string operatorId, string operatorName)
    {
        CurrentOperatorId = operatorId;
        CurrentOperatorName = operatorName;
        DeleteModalVisible = true;
    }

    protected async Task OnConfirmDelete()
    {
        try
        {
            DeleteModalVisible = false;

            // Delete operator using the existing command
            await this.SrpProcessors.CommandProcessor.SendAsync(new DeleteOperatorCommand(CurrentOperatorId, 
                await this.SrpProcessors.GetCommandContextAsync()));

            // Reload the operators list
            await this.ViewModel.LoadOrganizationOperatorsAsync(forceLoad: true);

            // Notify using AntDesign notifications
            await this.NotificationService.Success(new NotificationConfig()
            {
                Message = "Success",
                Description = $"Operator '{CurrentOperatorName}' has been deleted successfully",
                Duration = 4.0
            });

            // Clear current operator data
            CurrentOperatorId = "";
            CurrentOperatorName = "";
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when deleting operator with id '{CurrentOperatorId}'", CurrentOperatorId);
            #endregion

            // Notify using AntDesign notifications
            await this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Exception when deleting operator '{CurrentOperatorName}'",
                Duration = 6.0
            });
        }
    }

    protected void OnCancelDelete()
    {
        DeleteModalVisible = false;
        CurrentOperatorId = "";
        CurrentOperatorName = "";
    }
    #endregion

    #region EVENT HANDLERS
    protected void OnClick_AddOperator()
    {
        this.NavigationManager.NavigateTo(AddOperator.GetUri());
    }

    protected void OnClick_EditOperator(string id)
    {
        NavigationManager.NavigateTo(EditOperator.GetUri(id));
    }
    #endregion
}
