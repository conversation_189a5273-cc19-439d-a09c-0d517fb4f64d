using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using AntDesign;

namespace Klee.Web.App.Components.Pages.VoyageManagement.VoyageList;

public class VoyagesViewBase : ComponentBase
{
    #region DI
    [Inject]
    protected NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<VoyagesViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES
    protected VoyagesViewModel ViewModel { get; set; }

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        this.ViewModel = new VoyagesViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Load Voyages
            await this.ViewModel.LoadVoyagesAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion
        }
    }
    #endregion

    #region METHODS
    protected async Task OnClickRefresh()
    {
        try
        {
            await this.ViewModel.LoadVoyagesAsync(forceLoad: true);
            StateHasChanged();
        }
        catch (Exception exception)
        {
            this.Logger.LogError(exception, "Exception when refreshing voyage data.");
            
        }
    }
    #endregion
}
