@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Messages.Queries.VoyagePlanning.Data
@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data
@using EnumsNET

@inherits SelectCaptainViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-bold text-teal-700">Select a Captain</h2>
    <Button Type="@ButtonType.Default"
            Class="@TailwindStyleStrings.Button.Outline"
            OnClick="HandleOnClick_Back">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Planning
    </Button>
</div>

<Card Class="@TailwindStyleStrings.Card.Container">
    <div class="p-6">
        @if (ViewModel?.IsLoadingCaptains == true)
        {
            <div class="text-center py-12">
                <Spin Size="SpinSize.Large" />
                <p class="mt-4 text-gray-600">Finding available captains...</p>
            </div>
        }
        else if (ViewModel?.AvailableCaptains?.Any() == true)
        {
            <!-- Available Captains Table -->
            <Table TItem="AvailableCaptainListItem"
                   DataSource="@ViewModel.AvailableCaptains"
                   Class="@TailwindStyleStrings.Table.Container"
                   PageSize="10">
                <ColumnDefinitions>
                    <PropertyColumn Property="c => c.FullName" Title="Name" Sortable/>
                    <PropertyColumn Property="c => c.OrganizationName" Title="Organization" Sortable/>
                    <PropertyColumn Property="c => c.ExperienceDisplay" Title="Experience" Sortable/>
                    <PropertyColumn Property="c => c.HourlyRateInEuros" Title="Hourly Rate" Sortable>
                        <Template>
                            € @context.HourlyRateInEuros.ToString("N2")
                        </Template>
                    </PropertyColumn>
                    <ActionColumn Title="Actions" Fixed="ColumnFixPlacement.Right">
                        <Space>
                            <SpaceItem>
                                <Button Type="@ButtonType.Primary"
                                        Class="@TailwindStyleStrings.Button.Primary"
                                        Size="@ButtonSize.Small"
                                        OnClick="() => HandleSelectCaptain(context)">
                                    Select Captain
                                </Button>
                            </SpaceItem>
                        </Space>
           
                    </ActionColumn>
                </ColumnDefinitions>
                <ExpandTemplate Context="captain">
                    <div class="px-8 py-4 bg-gray-50">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Biography -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Biography</h4>
                                <div class="p-4">
                                    @if (!string.IsNullOrWhiteSpace(captain.Data.Biography))
                                    {
                                        <p class="text-sm text-gray-700 leading-relaxed">@captain.Data.Biography</p>
                                    }
                                    else
                                    {
                                        <p class="text-sm text-gray-500 italic">No biography available</p>
                                    }
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Qualifications</h4>
                                <div class="p-4">
                                    @if (captain.Data.Qualifications?.Any() == true)
                                    {
                                        <div class="flex flex-wrap gap-2">
                                            @foreach (var qualification in captain.Data.Qualifications)
                                            {
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800 border border-teal-200">
                                                    @qualification.AsString(EnumFormat.DisplayName)
                                                </span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <p class="text-sm text-gray-500 italic">No qualifications listed</p>
                                    }
                                </div>
                            </div>

                            <!-- Working Schedule -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Working Schedule</h4>
                                <div class="p-4 space-y-3">
                                    <div>
                                        <p class="text-sm font-medium text-gray-700 mb-2">Working Days:</p>
                                        <div class="flex flex-wrap gap-1">
                                            @foreach (var day in GetWorkingDaysDisplay(captain.Data.WorkingDays))
                                            {
                                                <span class="text-xs font-medium text-teal-800 px-2 py-1 bg-teal-100 rounded">@day</span>
                                            }
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Working Hours:</p>
                                        <p class="text-sm text-gray-600">@captain.Data.WorkingHoursDisplay</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ExpandTemplate>
            </Table>
        }
        else
        {
            <!-- No Captains Available -->
            <div class="text-center py-12">
                <i class="fas fa-user-slash text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Captains</h3>
                <p class="text-gray-600 mb-6">
                    No captains are available for your selected voyage dates and requirements.
                    You can modify your requirements or create the voyage without an operator and assign one later.
                </p>
                <div class="flex justify-center gap-4">
                    <Button Type="@ButtonType.Default"
                            Class="@TailwindStyleStrings.Button.Outline"
                            OnClick="HandleOnClick_Back">
                        Modify Voyage Requirements
                    </Button>
                    <Button Type="@ButtonType.Primary"
                            Class="@TailwindStyleStrings.Button.Primary"
                            OnClick="HandleCreateIncompleteVoyage">
                        Create Voyage Without Operator
                    </Button>
                </div>
            </div>
        }
    </div>
</Card>
