﻿using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateQualifications;
using Microsoft.AspNetCore.Components.Forms;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateQualifications;

public class OperatorUpdateQualificationsDialogViewBase : DialogEditFormOkCancelViewBase<OperatorUpdateQualificationsDialogViewModel>
{
    #region PROPERTIES
    protected EditContext ViewModelEditContext { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        this.ViewModel = new OperatorUpdateQualificationsDialogViewModel(this);
        this.ViewModelEditContext = new EditContext(this.ViewModel);

        //
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }
    #endregion

    #region METHODS
    public Task LoadOperatorAsync(string userId)
    {
        return this.ViewModel.LoadOperatorAsync(userId);
    }
    #endregion

    #region EVENT HANDLERS
    protected async Task OnDialogResetting()
    {
        // Clear ViewModel
        this.ViewModel.Clear();

        // Notify
        await this.InvokeStateHasChangedAsync();
    }

    protected Task OnDialogOpening(RDialogOpeningEventArgs e)
    {
        return Task.CompletedTask;
    }

    protected Task OnDialogOpened()
    {
        return Task.CompletedTask;
    }

    protected async Task OnDialogClosed(RDialogClosedEventArgs e)
    {
        // Reset Dialog
        await this.ResetDialogViewAsync();
    }

    protected async Task OnValidSubmit()
    {
        try
        {
            // Create Operator
            await this.ViewModel.UpdateOperatorAsync();

            // Set DialogResult
            this.DialogExtended.DialogResult = RDialogResult.Ok;
            this.DialogExtended.DialogResultData = this.ViewModel;

            // Close Dialog
            await this.CloseDialogViewAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Error on updating Operator.");
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            // Display exception
            this.DialogExtended.DisplayException(exception);

            // Clear DialogResult
            await this.DialogExtended.ClearDialogResultAsync();

            //throw;
        }
        finally
        {
        }
    }

    protected Task OnInvalidSubmit()
    {
        return Task.CompletedTask;
    }
    #endregion
}