﻿using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators
{
    public class CreateVehicleCommandValidator : AbstractValidator<CreateVehicleCommand>
    {
        public CreateVehicleCommandValidator()
        {
            this.RuleFor(_ => _.SoftwareEnvironmentId).Must(this.SoftwareEnvironmentIdMustBeValid).WithMessage("Software Environment is not valid");
            this.RuleFor(_ => _.VehicleTypeId).Must(this.VehicleTypeIdMustBeValid).WithMessage("Vehicle Type is not valid."); this.RuleFor(_ => _.OrganizationId).NotNull();
            this.RuleFor(_ => _.OrganizationId).NotEmpty();
        }

        private bool SoftwareEnvironmentIdMustBeValid(SoftwareEnvironmentIds softwareEnvironmentId)
        {
            return softwareEnvironmentId != SoftwareEnvironmentIds.None;
        }

        private bool VehicleTypeIdMustBeValid(VehicleTypeIds vehicleTypeId)
        {
            return vehicleTypeId != VehicleTypeIds.None;
        }

    }
}