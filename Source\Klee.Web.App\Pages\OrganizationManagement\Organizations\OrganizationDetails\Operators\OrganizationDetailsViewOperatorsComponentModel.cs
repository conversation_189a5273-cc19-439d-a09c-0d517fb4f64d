﻿using EnumsNET;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Operators;

public class OrganizationDetailsViewOperatorComponentModel
    : ViewModelBase<OrganizationDetailsViewOperatorComponentModel>, IRViewModelWithUserSessionData<UserSessionData>
{
    #region PROPERTIES - STATIC
    // Selectable
    public static IList<OperatorsMainFilterTypeIds> SelectableOperatorsMainFilterTypeIds { get; }
        = Enums.GetMembers<OperatorsMainFilterTypeIds>()
               .Where(_ => _.Value != OperatorsMainFilterTypeIds.None)
               .Select(_ => _.Value).ToList();
    #endregion

    #region FIELDS
    private OperatorsMainFilterTypeIds _OperatorsMainFilterTypeId = OperatorsMainFilterTypeIds.None;
    #endregion

    #region PROPERTIES
    public OperatorsMainFilterTypeIds OperatorsMainFilterTypeId
    {
        get => this._OperatorsMainFilterTypeId;
        set => this.RaiseAndSetIfChanged(ref this._OperatorsMainFilterTypeId, value);
    }

    // Actual Loaded
    public OperatorsMainFilterTypeIds LoadedOperatorsMainFilterTypeId { get; private set; }

    //
    public IReadOnlyList<OperatorListItem> Operators { get; private set; } = new List<OperatorListItem>();
    #endregion

    #region CONSTRUCTORS
    public OrganizationDetailsViewOperatorComponentModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS - OVERRIDES
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - USER SESSION DATA
    public void SetUserSessionData(UserSessionData userSessionData)
    {
        this.OperatorsMainFilterTypeId = userSessionData.OperatorsMainFilterTypeId;
    }

    public UserSessionData GetUserSessionData()
    {
        return new UserSessionData()
               {
                   OperatorsMainFilterTypeId = this.LoadedOperatorsMainFilterTypeId
               };
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadOrganizationOperatorsAsync(string organizationId, bool forceLoad = false)
    {
        // Adjust Properties
        this.OperatorsMainFilterTypeId = this.OperatorsMainFilterTypeId != OperatorsMainFilterTypeIds.None ? this.OperatorsMainFilterTypeId : OperatorsMainFilterTypeIds.All;

        // Load Operators
        if (this.LoadedOperatorsMainFilterTypeId != this.OperatorsMainFilterTypeId ||
            forceLoad)
        {
            // Load Operators
            switch (this.OperatorsMainFilterTypeId)
            {
                case OperatorsMainFilterTypeIds.Active:
                    // Load Operators
                    this.Operators = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationOperatorsQuery(organizationId, this.SrpQueryContext)
                                                                           {
                                                                               IsActive = true
                                                                           });
                    break;
                case OperatorsMainFilterTypeIds.All:
                default:
                    // Load Operators
                    this.Operators = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationOperatorsQuery(organizationId, this.SrpQueryContext));
                    break;
            }

            // Set
            this.LoadedOperatorsMainFilterTypeId = this.OperatorsMainFilterTypeId;

            // Notify
            await this.InvokeStateHasChangedOnHostAsync();

            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOperatorAsync(string operatorId, string organizationId)
    {
        try
        {
            // Delete Operator
            await this.SrpCommandProcessor.SendAsync(new DeleteOperatorCommand(operatorId,
                                                                          this.SrpCommandContext));

            // Load Operators
            await this.LoadOrganizationOperatorsAsync(organizationId, forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion
}