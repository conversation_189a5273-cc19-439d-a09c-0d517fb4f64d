﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.RocManagement.Rocs;

public class GetOrganizationRocsQueryHandler : QueryHandlerAsync<GetOrganizationRocsQuery, IReadOnlyList<RocListItem>> {
    #region PROPERTIES

    private IRocSrpRepository RocSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public GetOrganizationRocsQueryHandler(IRocSrpRepository rocSrpRepository) {
        this.RocSrpRepository = rocSrpRepository;
    }

    #endregion

    #region METHODS

    public override async Task<IReadOnlyList<RocListItem>> ExecuteAsync(GetOrganizationRocsQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        string organizationId = query.OrganizationId;

        // Init
        List<RocListItem> rocListItems =
            // Get RocListItems from DB
            await this.RocSrpRepository.Entities(query)
                .Where(_ => _.OrganizationId == organizationId)
                .OrderBy(_ => _.RocId)
                .Select(_ => new RocListItem() {
                    EntityId = _.EntityId,
                    RocId = _.RocId,
                    RocName = _.RocName,
                    OrganizationId = _.OrganizationId,
                    IsActive = _.IsActive ?? false
                })
                .ToListAsync(cancellationToken: cancellationToken);

        // Filter "IsActive" (if needed)
        if (query.IsActive != null) {
            rocListItems = rocListItems.Where(_ => _.IsActive == query.IsActive).ToList();
        }

        return rocListItems;
    }
    #endregion
}

