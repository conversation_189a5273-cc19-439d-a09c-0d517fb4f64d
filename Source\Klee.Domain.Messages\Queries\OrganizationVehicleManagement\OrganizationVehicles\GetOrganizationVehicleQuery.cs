﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;

public class GetOrganizationVehicleQuery
    : QueryBase<Vehicle>
{
    #region PROPERTIES
    public string VehicleId { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationVehicleQuery(string vehicleId,
        IQueryContext context)
        : base(context)
    {
        VehicleId = vehicleId;
    }
    #endregion
}