﻿using Klee.Domain.Entities.UserManagement.Users;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.UserManagement.Users;

public class GetUserProfileQuery
    : QueryBase<UserProfile>
{
    #region PROPERTIES
    public string UserId { get; }

    public bool AllowCached { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetUserProfileQuery(string userId,
        IQueryContext context)
        : base(context)
    {
        this.UserId = userId;
    }
    #endregion
}