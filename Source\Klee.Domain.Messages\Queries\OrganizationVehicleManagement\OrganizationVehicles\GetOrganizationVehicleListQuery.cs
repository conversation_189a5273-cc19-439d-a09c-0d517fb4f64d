﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;

public class GetOrganizationVehicleListQuery
    : QueryBase<IReadOnlyList<OrganizationVehicleListItem>>
{
    #region PROPERTIES
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationVehicleListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}