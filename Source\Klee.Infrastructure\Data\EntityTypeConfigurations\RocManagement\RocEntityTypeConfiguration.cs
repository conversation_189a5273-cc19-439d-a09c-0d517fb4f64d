﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.RocManagement;

public class RocEntityTypeConfiguration : IEntityTypeConfiguration<Roc> {
    public void Configure(EntityTypeBuilder<Roc> builder) {
        //builder.AddCosmosDbProperties();
        builder.HasIndex(_ => _.RocId)
            .IsUnique();
        builder.Property(_ => _.RocId)
            .IsRequired();
        builder.Property(_ => _.RocName)
            .IsRequired();
        builder.Property(_ => _.OrganizationId)
            .IsRequired();
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        //Set up the relationship with org
        builder
            .HasOne(s => s.Organization)
            .WithMany(o => o.Rocs)
            .HasForeignKey(s => s.OrganizationId)
            .HasPrincipalKey(r => r.OrganizationId);
    }
}
