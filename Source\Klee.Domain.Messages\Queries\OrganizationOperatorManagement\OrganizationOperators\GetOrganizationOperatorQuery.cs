﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;

public class GetOrganizationOperatorQuery
    : QueryBase<Operator>
{
    #region PROPERTIES
    public string OperatorId { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationOperatorQuery(string operatorId,
        IQueryContext context)
        : base(context)
    {
        OperatorId = operatorId;
    }
    #endregion
}