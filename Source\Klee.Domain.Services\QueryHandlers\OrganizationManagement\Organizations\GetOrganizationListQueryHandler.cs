using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.OrganizationManagement.Organizations
{
    public sealed class GetOrganizationListQueryHandler
        : QueryHandlerAsync<GetOrganizationListQuery, IReadOnlyList<OrganizationListItem>>
    {
        #region PROPERTIES
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationListQueryHandler(IOrganizationSrpRepository organizationSrpRepository,
                                                IMemoryCache memoryCache)
        {
            this.OrganizationSrpRepository = organizationSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<OrganizationListItem>> ExecuteAsync(GetOrganizationListQuery query,
                                                                                     CancellationToken cancellationToken = new CancellationToken())
        {
            // Get Organizations from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetOrganizationListCacheId, out List<OrganizationListItem> cachedOrganizations);

            // Init
            List<OrganizationListItem> organizations = cachedOrganizations ?? new List<OrganizationListItem>();

            // Get Organizations from DB (if needed)
            if (query.AllowCached == false || !isCached)
            {
                // Get Organizations
                organizations = await this.OrganizationSrpRepository.Entities(query)
                                  .OrderBy(_ => _.OrganizationId)
                                  .Select(_ => new OrganizationListItem
                                  {
                                      EntityId = _.EntityId,
                                      OrganizationId = _.OrganizationId,
                                      Name = _.Name,
                                      Code = _.Code,
                                      IsActive = _.IsActive ?? false
                                  })
                                  .ToListAsync(cancellationToken: cancellationToken);

                // Cache Organizations
                this.MemoryCache.Set(MemoryCacheIds.GetOrganizationListCacheId,
                                     organizations, new MemoryCacheEntryOptions()
                                     {
                                         AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                         Size = 1
                                     });
            }

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                organizations = organizations.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return organizations;
        }
        #endregion
    }
}