﻿using Klee.Domain.Entities.Common.Exceptions;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users.Validators;
using Klee.Domain.Services.CommandHandlers.UserManagement.Users.Helpers;
using Klee.Domain.Services.Repositories.UserManagement.Users;

namespace Klee.Domain.Services.CommandHandlers.UserManagement.Users;

    public sealed class CreateUserProfileCommandHandler
        : RequestHandlerAsync<CreateUserProfileCommand>
    {
        #region PROPERTIES
        private IUserProfileSrpRepository UserProfileSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateUserProfileCommandHandler(IUserProfileSrpRepository userProfileSrpRepository,
            IMemoryCache memoryCache)
        {
            UserProfileSrpRepository = userProfileSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateUserProfileCommandValidator))]
        public override async Task<CreateUserProfileCommand> HandleAsync(CreateUserProfileCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
            // Create UserProfile (if it doesn't exist)
            if (!await UserProfileSrpRepository.ExistsAsync(_ => _.UserId == command.UserId &&
                                                             _.EntityPartitionKey == command.UserId, command))
            {
                // Create
                UserProfile userProfile = new UserProfile()
                {
                    UserId = command.UserId,
                    FirstName = command.FirstName,
                    LastName = command.LastName,
                    DisplayName = command.DisplayName,
                    OrganizationId = command.OrganizationId,
                    Email = command.Email,
                };

                // Save 
                await UserProfileSrpRepository.AddAsync(userProfile, command);

                // Set Result
                command.Result.EntityId = userProfile.EntityId;

                // Clear Caches
                MemoryCache.RemoveUserProfile(command.UserId);
            }
            else
            {
                throw new EntityAlreadyExistsException(
                    $"UserProfile with UserProfile id '{command.UserId}' already exists.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }

        #endregion
    }