﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles;

public sealed class UpdateVehicleGeneralCommandHandler
    : RequestHandlerAsync<UpdateVehicleGeneralCommand> {
    #region PROPERTIES

    private IVehicleSrpRepository VehicleSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateVehicleGeneralCommandHandler(IVehicleSrpRepository vehicleSrpRepository,
        IMemoryCache memoryCache) {
        VehicleSrpRepository = vehicleSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateVehicleGeneralCommandValidator))]
    public override async Task<UpdateVehicleGeneralCommand> HandleAsync(UpdateVehicleGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {
        //Get vehicle (if it exists) 
        if (await VehicleSrpRepository.ExistsAsync(_ => _.VehicleId == command.VehicleId &&
                                                        _.EntityPartitionKey == command.VehicleId,
                command)) {
            Vehicle vehicle = await VehicleSrpRepository.FindAsync(_ => _.VehicleId == command.VehicleId &&
                                                                        _.EntityPartitionKey == command.VehicleId,
                command);

            vehicle.VehicleName = command.VehicleName;
            vehicle.ENI = command.ENI;
            vehicle.HourlyRateInEuros = command.HourlyRateInEuros;
            vehicle.OrganizationId = command.OrganizationId;

            // Update
            await VehicleSrpRepository.UpdateAsync(vehicle, command);
            
            // Set Result
            command.Result.EntityId = vehicle.EntityId;

            // Clear Caches
            MemoryCache.RemoveVehicle(command.VehicleId);
        }
        else {
            throw new EntityNotFoundException($"Vehicle with vehicle id '{command.VehicleId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}