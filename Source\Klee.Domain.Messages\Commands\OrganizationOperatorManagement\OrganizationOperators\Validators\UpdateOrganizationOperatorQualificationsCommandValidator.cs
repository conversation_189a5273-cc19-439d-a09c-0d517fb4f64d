﻿using FluentValidation;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;

public class UpdateOrganizationOperatorQualificationsCommandValidator : AbstractValidator<UpdateOrganizationOperatorQualificationsCommand> {
    public UpdateOrganizationOperatorQualificationsCommandValidator() {
        this.RuleFor(_ => _.OperatorId).NotNull();
        this.RuleFor(_ => _.OperatorId).NotEmpty();
        this.RuleFor(_ => _.Qualifications).NotNull();
    }

}