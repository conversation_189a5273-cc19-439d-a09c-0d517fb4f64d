﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers
{
    public static class IMemoryCacheExtensions
    {
        public static void RemoveVehicle(this IMemoryCache memoryCache,
                                         string vehicleId)
        {
            memoryCache.Remove(MemoryCacheIds.GetVehicleListCacheId);
            memoryCache.Remove(MemoryCacheIds.GetVehicleCacheId(vehicleId));
        }
    }
}
