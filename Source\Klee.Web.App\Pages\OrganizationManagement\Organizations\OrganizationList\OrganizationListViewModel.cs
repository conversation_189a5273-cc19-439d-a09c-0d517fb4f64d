using EnumsNET;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;
using UserSessionData = Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList.Data.UserSessionData;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList
{
    public class OrganizationListViewModel
        : ViewModelBase<OrganizationListViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - STATIC
        // Selectable
        public static IList<OrganizationsMainFilterTypeIds> SelectableOrganizationMainFilterTypeIds { get; }
            = Enums.GetMembers<OrganizationsMainFilterTypeIds>()
                .Where(_ => _.Value != OrganizationsMainFilterTypeIds.None)
                .Select(_ => _.Value).ToList();
        #endregion

        #region FIELDS
        private OrganizationsMainFilterTypeIds _organizationsMainFilterTypeId = OrganizationsMainFilterTypeIds.None;
        #endregion

        #region PROPERTIES
        public OrganizationsMainFilterTypeIds OrganizationsMainFilterTypeId
        {
            get => this._organizationsMainFilterTypeId;
            set => this.RaiseAndSetIfChanged(ref this._organizationsMainFilterTypeId, value);
        }

        // Actual Loaded
        public OrganizationsMainFilterTypeIds LoadedOrganizationsMainFilterTypeId { get; private set; }

        //
        public IReadOnlyList<OrganizationListItem> Organizations { get; private set; } = new List<OrganizationListItem>();
        #endregion

        #region CONSTRUCTORS
        public OrganizationListViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDES
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
            this.OrganizationsMainFilterTypeId = userSessionData.OrganizationsMainFilterTypeId;
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData() {
                OrganizationsMainFilterTypeId = this.LoadedOrganizationsMainFilterTypeId
            };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationsAsync(bool forceLoad = false)
        {

            // Adjust Properties
            this.OrganizationsMainFilterTypeId = this.OrganizationsMainFilterTypeId != OrganizationsMainFilterTypeIds.None ? this.OrganizationsMainFilterTypeId : OrganizationsMainFilterTypeIds.All;

            // Load Organizations
            if (this.LoadedOrganizationsMainFilterTypeId != this.OrganizationsMainFilterTypeId ||
                forceLoad)
            {
                // Load Organizations
                switch (this.OrganizationsMainFilterTypeId)
                {
                    case OrganizationsMainFilterTypeIds.Active:
                        // Load Organizations
                        this.Organizations = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationListQuery(this.SrpQueryContext)
                        {
                            IsActive = true
                        });
                        break;
                    case OrganizationsMainFilterTypeIds.All:
                    default:
                        // Load Organizations
                        this.Organizations = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationListQuery(this.SrpQueryContext));
                        break;
                }

                // Set
                this.LoadedOrganizationsMainFilterTypeId = this.OrganizationsMainFilterTypeId;

                // Notify
                await this.InvokeStateHasChangedOnHostAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteOrganizationAsync(string organizationId)
        {
            try
            {
                // Delete Organization
                await this.SrpCommandProcessor.SendAsync(new DeleteOrganizationCommand(organizationId, this.SrpCommandContext));

                // Reload Organizations
                await this.LoadOrganizationsAsync(forceLoad: true);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Error on deleting organization with id '{organizationId}'", organizationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }
} 