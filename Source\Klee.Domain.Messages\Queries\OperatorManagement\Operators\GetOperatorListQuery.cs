﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OperatorManagement.Operators;

public class GetOperatorListQuery
    : QueryBase<IReadOnlyList<OperatorListItem>>
{
    #region PROPERTIES
    public bool AllowCached { get; set; } = false;

    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetOperatorListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}