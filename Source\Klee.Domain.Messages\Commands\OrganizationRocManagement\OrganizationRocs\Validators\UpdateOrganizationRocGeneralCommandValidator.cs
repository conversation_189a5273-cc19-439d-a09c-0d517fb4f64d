﻿using FluentValidation;
using Klee.Domain.Entities.RocManagement.Rocs.Data;

namespace Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;

public class UpdateOrganizationRocGeneralCommandValidator : AbstractValidator<UpdateOrganizationRocGeneralCommand>
{
    public UpdateOrganizationRocGeneralCommandValidator() {
        this.RuleFor(_ => _.RocId).NotNull();
        this.RuleFor(_ => _.RocId).NotEmpty();
        this.RuleFor(_ => _.Country).Must(this.CountryMustBeValid).WithMessage("Country is not valid.");
    }

    private bool CountryMustBeValid(CountryIds country) {
        return country != CountryIds.None;
    }
}