﻿@using Renoir.Web.Razor.Components.DialogsEditForm
@using Renoir.Web.Razor.Components.Labels
@using Renoir.Web.Razor.Pages.Dialogs

@inherits VehicleUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="@ViewModelEditContext"
                 Title="Update Vehicle">
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Vehicle Id" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.VehicleId" ReadOnly="true"/>
                <ValidationMessage For="@(() => ViewModel.VehicleId)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Vehicle Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.VehicleName"/>
                <ValidationMessage For="@(() => ViewModel.VehicleName)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="ENI">
                <DxTextBox @bind-Text="ViewModel.ENI"/>
                <ValidationMessage For="@(() => ViewModel.ENI)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Hour Rate (Euros)">
                <DxSpinEdit @bind-Value="@ViewModel.HourlyRateInEuros"
                            DisplayFormat="€ #,##0.00" 
                            Increment="0.5"
                            MinValue="0" />
                <ValidationMessage For="@(() => ViewModel.HourlyRateInEuros)"/>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}