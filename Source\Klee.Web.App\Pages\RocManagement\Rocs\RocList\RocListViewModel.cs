﻿using EnumsNET;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Web.App.Pages.RocManagement.Rocs.RocList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocList;

    public class RocListViewModel
        : ViewModelBase<RocListViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - STATIC
        // Selectable
        public static IList<RocsMainFilterTypeIds> SelectableRocsMainFilterTypeIds { get; }
            = Enums.GetMembers<RocsMainFilterTypeIds>()
                   .Where(_ => _.Value != RocsMainFilterTypeIds.None)
                   .Select(_ => _.Value).ToList();
        #endregion
        
        #region FIELDS
        private RocsMainFilterTypeIds _rocsMainFilterTypeId = RocsMainFilterTypeIds.None;
        #endregion

        #region PROPERTIES
        public RocsMainFilterTypeIds RocsMainFilterTypeId
        {
            get => this._rocsMainFilterTypeId;
            set => this.RaiseAndSetIfChanged(ref this._rocsMainFilterTypeId, value);
        }

        // Actual Loaded
        public RocsMainFilterTypeIds LoadedRocsMainFilterTypeId { get; private set; }

        //
        public IReadOnlyList<RocListItem> Rocs { get; private set; } = new List<RocListItem>();
        #endregion

        #region CONSTRUCTORS
        public RocListViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDES
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
            this.RocsMainFilterTypeId = userSessionData.RocsMainFilterTypeId;
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                       RocsMainFilterTypeId = this.LoadedRocsMainFilterTypeId
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadRocsAsync(bool forceLoad = false)
        {
            // Adjust Properties
            this.RocsMainFilterTypeId = this.RocsMainFilterTypeId != RocsMainFilterTypeIds.None ? this.RocsMainFilterTypeId : RocsMainFilterTypeIds.All;

            // Load Vehicles
            if (this.LoadedRocsMainFilterTypeId != this.RocsMainFilterTypeId ||
                forceLoad)
            {
                // Load Vehicles
                switch (this.RocsMainFilterTypeId)
                {
                    case RocsMainFilterTypeIds.Active:
                        // Load Vehicles
                        this.Rocs = await this.SrpQueryProcessor.ExecuteAsync(new GetRocListQuery(this.SrpQueryContext)
                                                                               {
                                                                                   IsActive = true
                                                                               });
                        break;
                    case RocsMainFilterTypeIds.All:
                    default:
                        // Load Vehicles
                        this.Rocs = await this.SrpQueryProcessor.ExecuteAsync(new GetRocListQuery(this.SrpQueryContext));
                        break;
                }

                // Set
                this.LoadedRocsMainFilterTypeId = this.RocsMainFilterTypeId;

                // Notify
                await this.InvokeStateHasChangedOnHostAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteRocAsync(string rocId)
        {
            try
            {
                // Delete Vehicle
                //await this.SrpCommandProcessor.SendAsync(new DeleteVehicleCommand(vehicleId,
                //                                                               this.SrpCommandContext));

                // Load Vehicles
                await this.LoadRocsAsync(forceLoad: true);
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }