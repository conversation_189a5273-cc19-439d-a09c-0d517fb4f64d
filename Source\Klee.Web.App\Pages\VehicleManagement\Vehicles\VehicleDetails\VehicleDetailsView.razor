﻿@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails.Data
@using Microsoft.IdentityModel.Tokens
@using Microsoft.AspNetCore.Authorization
@using Renoir.Srp.Portal.Web.Application.Security
@using Microsoft.AspNetCore.Components.Authorization
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleUpdateGeneral

@page "/Vehicles/Management/Vehicle/{VehicleIdEncoded}"
@inherits VehicleDetailsViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody @ref="MainLayoutBody"
                 PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 OnClickDelete="@OnClickDelete"
                 CanShowButtonDelete=true
                 OnActiveTabChanged="@OnMainLayoutBody_ActiveTabChanged"
                 ActiveTabId="@DetailsViewProperties.ActiveTabId">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Vehicles"/>
        <RMainLayoutBodyBreadcrumbListItem Text="Details"/>
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarRightMenuItems>
    <TabNavItems>
        <RTabNavItem TabId="@VehicleDetailsViewTabIds.General" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemGeneral_IsActiveChanged">GENERAL</RTabNavItem>
        <RTabNavItem TabId="@VehicleDetailsViewTabIds.YearOverview" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemYearOverview_IsActiveChanged">YEAR OVERVIEW</RTabNavItem>
    </TabNavItems>
    <TabContentItems>
        <RTabContentItem @ref="@TabContentItemGeneral"
                         TabId="@VehicleDetailsViewTabIds.General" MinHeight="200">
            <div class="row">
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Identification</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenVehicleUpdateGeneralDialogView"
                                                         CanShow=@AuthenticatedUser.IsAdminApp() />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="Vehicle Type" Value="@ViewModel.VehicleTypeDisplayName" />
                                    <RControlTextValueDisplay Label="Vehicle Id" Value="@ViewModel.VehicleId" />
                                    <RControlTextValueDisplay Label="Vehicle Name" Value="@ViewModel.VehicleName" />
                                    <RControlTextValueDisplay Label="ENI" Value="@ViewModel.ENI"
                                                              LabelTooltip="European Number of Identification" />
                                    <RControlTextValueDisplay Label="Hourly Rate (Euro)" Value="@ViewModel.HourlyRateInEuros" />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                                <section class="card">
                                    <header class="card-header">
                                        <h2 class="card-title">System</h2>
                                        <div class="card-actions">
                                            <RCardActionToggle />
                                        </div>
                                    </header>
                                    <div class="card-body">
                                        <RControlTextValueDisplay Label="Environment" Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                                        <RControlCheckBoxValueDisplay Label="Active" Value="@ViewModel.IsActive" />
                                    </div>
                                </section>
                            </AuthorizeView>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
        <RTabContentItem @ref="@TabContentItemYearOverview"
                         TabId="@VehicleDetailsViewTabIds.YearOverview" MinHeight="200">
            <div class="row">
                <div class="col-lg-12 col-xl-12 col-xxl-8">
                    <div class="row">
                        <div class="col-lg-12 col-xl-12 col-xl-12">
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
    </TabContentItems>
    <Content>
    </Content>
</RMainLayoutBody>

<VehicleUpdateGeneralDialogView @ref="VehicleUpdateGeneralDialogView"
                                OnDialogViewOpened="OnVehicleUpdateGeneralDialogView_Opened"
                                OnDialogViewClosed="OnVehicleUpdateGeneralDialogView_Closed" />
@code {

    #region METHODS - STATIC
    public static string GetUri(string vehicleId)
    {
        return $"/Vehicles/Management/Vehicle/{Base64UrlEncoder.Encode(vehicleId)}";
    }
    #endregion

}