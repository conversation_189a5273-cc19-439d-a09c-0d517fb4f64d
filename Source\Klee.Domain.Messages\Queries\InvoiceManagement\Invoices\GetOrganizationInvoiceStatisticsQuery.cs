using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.InvoiceManagement.Invoices;

public class GetOrganizationInvoiceStatisticsQuery
    : QueryBase<InvoiceStatistics>
{
    #region PROPERTIES
    public string OrganizationId { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationInvoiceStatisticsQuery(string organizationId, IQueryContext context)
        : base(context)
    {
        this.OrganizationId = organizationId;
    }
    #endregion
}
