﻿using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;

namespace Klee.Domain.Messages.Queries.RocManagement.Rocs;

public class GetOrganizationRocsQuery
    : QueryBase<IReadOnlyList<RocListItem>>
{
    #region PROPERTIES
    public string OrganizationId { get; }
    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationRocsQuery(string organizationId, IQueryContext context)
        : base(context)
    {
        this.OrganizationId = organizationId;
    }
    #endregion
}