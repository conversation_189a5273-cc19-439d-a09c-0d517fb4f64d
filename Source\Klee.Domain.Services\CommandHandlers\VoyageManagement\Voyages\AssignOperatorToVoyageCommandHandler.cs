using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions.VoyageManagement;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.VoyageManagement.Voyages;

public sealed class AssignOperatorToVoyageCommandHandler
    : RequestHandlerAsync<AssignOperatorToVoyageCommand>
{
    #region PROPERTIES
    private IVoyageSrpRepository VoyageSrpRepository { get; }
    private IAppSrpDbContext DbContext { get; }
    #endregion

    #region CONSTRUCTORS
    public AssignOperatorToVoyageCommandHandler(IVoyageSrpRepository voyageSrpRepository,
                                                IAppSrpDbContext dbContext)
    {
        VoyageSrpRepository = voyageSrpRepository;
        DbContext = dbContext;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    public override async Task<AssignOperatorToVoyageCommand> HandleAsync(AssignOperatorToVoyageCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        await using IDbContextTransaction transaction = await DbContext.Database.BeginTransactionAsync(cancellationToken);
        
        try
        {
            // Find the voyage
            Voyage voyage = await VoyageSrpRepository.FindAsync(v => v.VoyageId == command.VoyageId &&
                                                                     v.EntityPartitionKey == command.VoyageId.ToString(), command);

            if (voyage == null)
            {
                throw new InvalidOperationException($"Voyage with ID {command.VoyageId} not found.");
            }

            // Verify voyage still needs an operator
            if (!voyage.NeedsOperator)
            {
                throw new InvalidOperationException("This voyage already has an operator assigned.");
            }

            // Check for operator availability conflicts (including 15-minute buffer)
            TimeSpan buffer = TimeSpan.FromMinutes(15);
            DateTime startWithBuffer = voyage.StartDateTime.Subtract(buffer);
            DateTime endWithBuffer = voyage.EndDateTime.Add(buffer);

            bool hasConflictingVoyages = await DbContext.Set<Voyage>()
                .Where(v => v.IsActive == true)
                .Where(v => v.OperatorId == command.OperatorId)
                .Where(v => v.VoyageId != command.VoyageId) // Exclude current voyage
                .Where(v => v.EndDateTime > startWithBuffer && v.StartDateTime < endWithBuffer)
                .AnyAsync(cancellationToken);

            if (hasConflictingVoyages)
            {
                throw new VoyageConcurrencyException(
                    command.OperatorId,
                    voyage.StartDateTime,
                    voyage.EndDateTime);
            }

            // Assign operator to voyage
            voyage.OperatorId = command.OperatorId;

            // Update voyage
            await VoyageSrpRepository.UpdateAsync(voyage, command);

            // Commit transaction
            await transaction.CommitAsync(cancellationToken);

            // Set result
            command.Result.VoyageId = voyage.VoyageId;
            command.Result.Success = true;
        }
        catch (VoyageConcurrencyException)
        {
            // Re-throw concurrency exceptions
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
        catch (DbUpdateException ex)
        {
            // Handle database constraint violations
            await transaction.RollbackAsync(cancellationToken);
            throw new VoyageConcurrencyException(
                command.OperatorId,
                DateTime.MinValue,
                DateTime.MinValue,
                "The captain is no longer available for the requested time slot. Another booking may have been made.",
                ex);
        }
        catch (Exception)
        {
            // Handle any other unexpected errors
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }
    #endregion
}
