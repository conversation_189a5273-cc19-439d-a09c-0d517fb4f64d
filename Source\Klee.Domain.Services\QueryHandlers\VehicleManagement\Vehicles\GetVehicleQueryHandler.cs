﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Domain.Services.UserContextService;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VehicleManagement.Vehicles
{
    public sealed class GetVehicleQueryHandler
        : QueryHandlerAsync<GetVehicleQuery, Vehicle>
    {
        #region PROPERTIES
        private IVehicleSrpRepository VehicleSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetVehicleQueryHandler(IVehicleSrpRepository vehicleSrpRepository,
                                      IMemoryCache memoryCache)
        {
            this.VehicleSrpRepository = vehicleSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<Vehicle> ExecuteAsync(GetVehicleQuery query,
                                                         CancellationToken cancellationToken = new CancellationToken())
        {
            // Init
            string vehicleId = query.VehicleId;

            // Get Vehicle from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetVehicleCacheId(vehicleId), out Vehicle cachedVehicle);

            // Init
            Vehicle vehicle = cachedVehicle;

            // Get Vehicle from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get Vehicle
                vehicle = await this.VehicleSrpRepository.FindAsync(_ => _.VehicleId == vehicleId &&
                                                                         _.EntityPartitionKey == vehicleId, query);
                // Cache Vehicle
                this.MemoryCache.Set(MemoryCacheIds.GetVehicleCacheId(vehicleId),
                                     vehicle, new MemoryCacheEntryOptions()
                                              {
                                                  AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                  Size = 1
                                              });
            }

            return vehicle;
        }
        #endregion
    }
}