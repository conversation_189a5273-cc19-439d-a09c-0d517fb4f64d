@page "/my-assets/rocs/add"

@layout OrganizationViewLayout
@using AntDesign
@using Klee.Domain.Entities.RocManagement.Rocs.Data
@using Klee.Web.App.Components.UI

@inherits AddRocViewBase

<div class="container py-3">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">Add a ROC</h1>
        <Button Type="@ButtonType.Default"
                Class="@TailwindStyleStrings.Button.Outline"
                OnClick="NavigateToRocList">
            Cancel
        </Button>
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="p-6">
            @if (this.IsLoading)
            {
                <div class="text-center py-12">
                    <Spin Size="SpinSize.Large" />
                    <p class="mt-4 text-gray-600">Loading...</p>
                </div>
            }
            else if (ViewModel != null)
            {
                <EditForm Model="@ViewModel" OnValidSubmit="HandleSubmit" OnInvalidSubmit="HandleSubmit">
                    <DataAnnotationsValidator />
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 lg:grid-cols-1 gap-12">
                            <!-- ROC Details Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-building mr-2"></i>ROC Details
                                </h3>

                                <FormField Label="ROC Name" Id="rocName" IsRequired="true">
                                    <Input @bind-Value="ViewModel.RocName"
                                           Placeholder="Enter ROC name"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="rocName" />
                                    <ValidationMessage For="@(() => ViewModel.RocName)"/>
                                </FormField>

                                <FormField Label="Address" Id="address">
                                    <Input @bind-Value="ViewModel.Address"
                                           Placeholder="Enter street address"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="address" />
                                    <ValidationMessage For="@(() => ViewModel.Address)"/>
                                </FormField>

                                <FormField Label="Location" Id="location">
                                    <Input @bind-Value="ViewModel.Location"
                                           Placeholder="Enter city/location"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="location" />
                                    <ValidationMessage For="@(() => ViewModel.Location)" />
                                </FormField>

                                <FormField Label="Postal Code" Id="postalCode">
                                    <Input @bind-Value="ViewModel.PostalCode"
                                           Placeholder="Enter postal code"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="postalCode" />
                                    <ValidationMessage For="@(() => ViewModel.PostalCode)" />
                                </FormField>

                                <FormField Label="Country" Id="country" IsRequired="true">
                                    <EnumSelect TEnum="CountryIds"
                                                @bind-Value="@ViewModel.Country"
                                                Placeholder="Select country"
                                                Class="@TailwindStyleStrings.Form.Select"
                                                AllowClear="false"
                                                Id="country">
                                    </EnumSelect>
                                    <ValidationMessage For="@(() => ViewModel.Country)"/>
                                </FormField>
                            </div>
                        </div>
                        
                        @if (ValidationErrors.Any())
                        {
                            <div class="rounded-md bg-red-50 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-circle h-5 w-5 text-red-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <ul class="list-disc pl-5 space-y-1">
                                                @foreach (var error in ValidationErrors)
                                                {
                                                    <li>@error</li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="flex justify-end gap-4 pt-4">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="NavigateToRocList">
                                Cancel
                            </Button>
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    HtmlType="submit"
                                    Loading="@this.IsSubmitting">
                                <i class="fas fa-save h-4 w-4 mr-2"></i>
                                Confirm
                            </Button>
                        </div>
                    </div>
                </EditForm>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-500 mb-4"></i>
                    <p class="text-red-600">Failed to load form. Please try again.</p>
                    <Button Type="@ButtonType.Primary"
                            Class="@($"{TailwindStyleStrings.Button.Primary} mt-4")"
                            OnClick="NavigateToRocList">
                        Back to ROCs
                    </Button>
                </div>
            }
        </div>
    </Card>
</div>

@code {

    #region METHODS - STATIC
    public static string GetUri()
    {
        return "/my-assets/rocs/add";
    }
    #endregion

}
