﻿using FluentValidation;
using Klee.Domain.Entities.RocManagement.Rocs.Data;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;

public class CreateOrganizationRocCommandValidator : AbstractValidator<CreateOrganizationRocCommand> {
    public CreateOrganizationRocCommandValidator() {
        this.RuleFor(_ => _.Country).Must(this.CountryMustBeValid).WithMessage("Country is not valid.");
    }

    private bool CountryMustBeValid(CountryIds country) {
        return country != CountryIds.None;
    }
}