@page "/find-voyage/assign-operator/{VoyageId}"

@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data

@layout OrganizationViewLayout

@inherits OperatorAssignmentViewBase

<div class="container py-3">
    @if (IsLoading)
    {
        <div class="text-center py-12">
            <Spin Size="SpinSize.Large" />
            <p class="mt-4 text-gray-600">Loading voyage details...</p>
        </div>
    }
    else if (!ViewModel.HasVoyageData())
    {
        <div class="text-center py-12">
            <i class="fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Voyage Not Found</h3>
            <p class="text-gray-500 mb-4">The requested voyage could not be found or is no longer available for operator assignment.</p>
            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="NavigateToFindVoyage">
                Back to Find Voyage
            </Button>
        </div>
    }
    else
    {
        <!-- Steps Component -->
        <div class="mb-8">
            <Steps Current="@ViewModel.GetCurrentStep()" Class="voyage-steps">
                <Step Title="Select Operator" Description="Choose an available operator" />
                <Step Title="Confirm Assignment" Description="Review and confirm operator assignment" />
            </Steps>
        </div>

        <!-- Main Content -->
        @switch (ViewModel.CurrentViewState) {
            case OperatorAssignmentViewState.SelectingOperator:
                <SelectOperatorForVoyageComponent @ref="this.SelectOperatorComponentRef"
                                                  VoyageStartDateTime="@(ViewModel.VoyageStartDateTime)"
                                                  VoyageEndDateTime="@(ViewModel.VoyageEndDateTime)"
                                                  RequiredQualifications="@ViewModel.RequiredQualifications"
                                                  OnClick_Back="@NavigateToFindVoyage"
                                                  OnSelectOperator="@HandleSelectOperator" />
                break;
            case OperatorAssignmentViewState.ConfirmingAssignment:
                <AssignmentConfirmationComponent @ref="AssignmentConfirmationComponentRef"
                                                 VoyageData="@ViewModel.VoyageData"
                                                 SelectedOperator="@ViewModel.SelectedOperator"
                                                 OnClick_Back="@HandleBackToSelectOperator"
                                                 OnAssignmentConfirmed="@HandleAssignmentConfirmed" />
                break;
        }
    }
</div>
