﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.OrganizationVehicleMangement.OrganizationVehicles;

public sealed class UpdateOrganizationVehicleGeneralCommandHandler
    : RequestHandlerAsync<UpdateOrganizationVehicleGeneralCommand> {
    #region PROPERTIES

    private IVehicleSrpRepository VehicleSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOrganizationVehicleGeneralCommandHandler(IVehicleSrpRepository vehicleSrpRepository) {
        VehicleSrpRepository =vehicleSrpRepository;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationVehicleGeneralCommandValidator))]
    public override async Task<UpdateOrganizationVehicleGeneralCommand> HandleAsync(UpdateOrganizationVehicleGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        //Get vehicle (if it exists) 
        if (await VehicleSrpRepository.ExistsAsync(_ => _.VehicleId == command.VehicleId &&
                                                        _.EntityPartitionKey == command.VehicleId,
                command)) {
            Vehicle vehicle = await VehicleSrpRepository.FindAsync(_ => _.VehicleId == command.VehicleId &&
                                                                        _.EntityPartitionKey == command.VehicleId,
                command);

            vehicle.VehicleName = command.VehicleName;
            vehicle.ENI = command.ENI;
            vehicle.HourlyRateInEuros = command.HourlyRateInEuros;
            vehicle.Length = command.Length;
            vehicle.Beam = command.Beam;
            vehicle.VesselType = command.VesselType;
            // Update
            await VehicleSrpRepository.UpdateAsync(vehicle, command);

            // Set Result
            command.Result.EntityId = vehicle.EntityId;

        }
        else {
            throw new EntityNotFoundException(
                $"Vehicle with vehicle id '{command.VehicleId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}