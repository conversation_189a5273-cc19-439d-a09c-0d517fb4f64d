﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Renoir.Application.Domain;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;

namespace Klee.Domain.Entities.InvoiceManagement.Invoices;

public class VoyageInvoice: DomainEntityAggregateRootBase<Guid> {

    #region PROPERTIES - IDENTIFICATION

    /// <summary>
    /// The Seafar internal id of the VoyageInvoice
    /// </summary>
    [Required]
    public Guid VoyageInvoiceId { get; internal set; } = Guid.NewGuid();

    /// <summary>
    /// Organization that booked the Voyage
    /// </summary>
    [Required]
    public string BookingOrganizationId { get; internal set; } = "";
    public Organization BookingOrganization { get; internal set; }


    /// <summary>
    /// Organization that provides the operator
    /// </summary>
    [Required]
    public string OperatorOrganizationId { get; internal set; } = "";
    public Organization OperatorOrganization { get; internal set; }

    /// <summary>
    /// Voyage that this invoice is related to
    /// </summary>
    [Required]
    public Guid VoyageId { get; internal set; } = Guid.Empty;
    public Voyage? Voyage { get; internal set; }

    public double TotalAmountInEuros { get; internal set; } = 0.0;

    /// <summary>
    /// Status of the invoice (Open, Paid)
    /// </summary>
    public VoyageInvoiceStatus Status { get; internal set; } = VoyageInvoiceStatus.Open;

    /// <summary>
    /// Date when the invoice was paid (null if not paid)
    /// </summary>
    public DateTime? PaymentDate { get; internal set; } = null;

    #endregion

    #region PROPERTIES - SYSTEM
    /// <summary>
    /// Is active when the Voyage is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;
    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return this.VoyageInvoiceId.ToString();
    }

    public override string GetEntityId2()
    {
        return this.VoyageInvoiceId.ToString();
    }

    public override string GetEntityTypeName()
    {
        return "VoyageInvoice";
    }
    #endregion

}