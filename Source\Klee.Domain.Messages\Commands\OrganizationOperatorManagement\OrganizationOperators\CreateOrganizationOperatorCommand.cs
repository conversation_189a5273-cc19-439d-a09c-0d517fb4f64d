﻿using System;
using System.Collections.Generic;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;

public class CreateOrganizationOperatorCommand : CommandBase {
    #region RESULT CLASS

    public class CommandResult {
        public long EntityId { get; set; }
    }

    #endregion

    #region PROPERTIES
    public string FirstName { get; set; } = "";
    public string LastName { get; set; } = "";
    public string Email { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public List<QualificationTypeIds> Qualifications { get; set; } = new();
    public int YearsOfExperience { get; set; } = 0;
    public int YearsOfRemoteExperience { get; set; } = 0;
    public WeekDaysIds WeekDays { get; set; } = WeekDaysIds.Monday | WeekDaysIds.Tuesday | WeekDaysIds.Wednesday | WeekDaysIds.Thursday | WeekDaysIds.Friday;
    public DateTime RegularStartTime { get; set; } = DateTime.Today.AddHours(9);
    public DateTime RegularEndTime { get; set; } = DateTime.Today.AddHours(17);
    public string Biography { get; set; } = "";

    // Result
    public CommandResult Result { get; } = new ();

    #endregion

    #region CONSTRUCTORS

    public CreateOrganizationOperatorCommand(ICommandContext commandContext)
        : base(commandContext) {
    }

    #endregion
}