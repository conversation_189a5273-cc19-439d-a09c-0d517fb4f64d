﻿using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;

using Klee.Web.App.Pages.StationManagement.Stations.StationCreate;
using Klee.Web.App.Pages.StationManagement.Stations.StationDetails;
using Klee.Web.App.Pages.StationManagement.Stations.StationList;
using Klee.Web.App.Pages.StationManagement.Stations.StationList.Data;
using Microsoft.AspNetCore.Components;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Stations;

    public partial class RocDetailsViewStationsComponentBase 
        : LayoutBodyListDxGridViewBase<RocDetailsViewStationComponentModel, StationListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        [Parameter]
        public string RocId { get; set; } = "";

        #endregion

        #region CONSTRUCTORS
        protected RocDetailsViewStationsComponentBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new RocDetailsViewStationComponentModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Stations
                if (await this.ViewModel.LoadRocStationsAsync(this.RocId))
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            StationListItem stationListItem = e.GetDataItem() as StationListItem ?? new StationListItem();
            string stationId = stationListItem.StationId ?? "";

            try
            {
                // View Station
                await this.NavigateToAsync(StationDetailsView.GetUri(stationId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of station '{stationId}'", stationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            StationListItem stationListItem = e.GetDataItem() as StationListItem ?? new StationListItem();
            string stationId = stationListItem.StationId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Station?",
                                                                        $"Delete Station with id '{stationId}'?") == RDialogResult.Ok)
                {
                    // Delete Station
                    await this.ViewModel.DeleteStationAsync(stationId, this.RocId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Station with id '{stationId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting Station with id '{stationId}'", stationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllStations(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.StationsMainFilterTypeId = StationsMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of StationsMainFilterType?
        //    await this.ViewModel.LoadStationsAsync();
        //}

        public async void OnValueChanged_StationsMainFilterTypeId(StationsMainFilterTypeIds stationsMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.StationsMainFilterTypeId = stationsMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of StationsMainFilterType?
                await this.ViewModel.LoadRocStationsAsync(this.RocId);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(RocDetailsViewStationsComponent));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }