﻿using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;
using Monet.Helpers;
using Renoir.SoftwareEnvironments;

namespace Klee.Web.App.Pages.StationManagement.Stations.StationUpdateGeneral;

    public class StationUpdateGeneralDialogViewModel
        : ViewModelBase<StationUpdateGeneralDialogViewModel>
    {
        #region FIELDS
        private string _stationJoystickTypeDisplayName = JoystickTypeIds.Default.GetDisplayName();
        private JoystickTypeIds _stationJoystickTypeId = JoystickTypeIds.Default;
        #endregion

        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableJoystickTypeDisplayNames { get; } = Enums.GetMembers<JoystickTypeIds>()
            .Where(_ => _.Value != JoystickTypeIds.None)
            .Where(_ => _.Value != JoystickTypeIds.Undefined)
            .Select(_ => _.AsString(EnumFormat.DisplayName))
            .OrderBy(_ => _).ToList();
        #endregion

        #region PROPERTIES
        [Required]
        [Display(Name = "Station Id")]
        public string StationId { get; set; } = "";

        [Required]
        [Display(Name = "Station Name")]
        public string StationName { get; set; } = "";

        [Display(Name = "ROC Id")]
        public string RocId { get; set; } = "";

        [Required]
        [Display(Name = "Station joystick type")]
        public string StationJoystickTypeDisplayName
        {
            get => this._stationJoystickTypeDisplayName;
            set => this.SetStationJoystickTypeDisplayName(value);
        }
        
        [Display(Name = "Hourly rate (euro)")]
        public double HourlyRateInEuros { get; set; } = 0.0;
        #endregion

    #region PROPERTIES - CONVERTED
    public JoystickTypeIds StationJoystickTypeId => this._stationJoystickTypeId;
        #endregion

        #region CONSTRUCTORS
        public StationUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.StationId = "";
            this.StationName = "";
            this.RocId = "";
            this.StationJoystickTypeDisplayName = JoystickTypeIds.Default.GetDisplayName();
            this.HourlyRateInEuros = 0.0;
    }

        public async Task LoadStationAsync(string stationId)
        {
            // Get Station
            Station station = await this.SrpQueryProcessor.ExecuteAsync(new GetStationQuery(stationId, this.SrpQueryContext)) ?? new Station();

            // Set Station
            this.StationId = station.StationId;
            this.StationName = station.StationName;
            this.RocId = station.RocId;
            this.StationJoystickTypeDisplayName = station.JoystickTypeId.GetDisplayName();
            this.HourlyRateInEuros = station.HourlyRateInEuros;
    }

        private void SetStationJoystickTypeDisplayName(string stationJoystickTypeDisplayName)
        {
            // Init
            bool isStationTypeChanged = stationJoystickTypeDisplayName != this.StationJoystickTypeDisplayName;

            //
            if (Enums.TryParse(stationJoystickTypeDisplayName, true, out JoystickTypeIds enumValue, EnumFormat.DisplayName))
            {
                this._stationJoystickTypeDisplayName = stationJoystickTypeDisplayName;
                this._stationJoystickTypeId = enumValue;
            }
            else
            {
                this._stationJoystickTypeDisplayName = stationJoystickTypeDisplayName;
                this._stationJoystickTypeId = JoystickTypeIds.None;
            }

            // Handle changed Station type
            if (isStationTypeChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }
        #endregion

        #region METHODS
        public async Task UpdateStationAsync()
        {
            // Init
            UpdateStationGeneralCommand updateStationGeneralCommand = new UpdateStationGeneralCommand(this.StationId,
                                                                                                      this.RocId,               
                                                                                                      this.SrpCommandContext)
                                                                      {
                                                                          StationName = this.StationName,
                                                                          JoystickTypeId = this.StationJoystickTypeId,
                                                                          HourlyRateInEuros = this.HourlyRateInEuros
                                                                      };

            // Create Station
            await this.SrpCommandProcessor
                      .SendAsync(updateStationGeneralCommand, false);
        }
        #endregion
    }