﻿using System.Collections.Generic;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;

public class UpdateOrganizationOperatorQualificationsCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string OperatorId { get; }
    public List<QualificationTypeIds> Qualifications { get; set; } = new();

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOrganizationOperatorQualificationsCommand(string operatorId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.OperatorId = operatorId;
    }
    #endregion
}