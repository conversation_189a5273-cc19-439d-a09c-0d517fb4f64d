@page "/my-assets/operators/edit/{OperatorIdEncoded}"

@layout OrganizationViewLayout
@using AntDesign
@using Klee.Web.App.Components.UI
@using Microsoft.IdentityModel.Tokens
@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data
@using EnumsNET

@inherits EditOperatorViewBase

<div class="container py-3">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">Edit Operator</h1>
        <Button Type="@ButtonType.Default"
                Class="@TailwindStyleStrings.Button.Outline"
                OnClick="NavigateToOperatorList">
            Cancel
        </Button>
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="p-6">
            @if (this.IsLoading)
            {
                <div class="text-center py-12">
                    <Spin Size="SpinSize.Large" />
                    <p class="mt-4 text-gray-600">Loading operator data...</p>
                </div>
            }
            else if (ViewModel != null)
            {
                <EditForm Model="@ViewModel" OnValidSubmit="HandleSubmit" OnInvalidSubmit="HandleSubmit">
                    <DataAnnotationsValidator />
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">

                            <!-- Personal Information Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-user mr-2"></i>Personal Information
                                </h3>

                                <FormField Label="First Name" Id="firstName">
                                    <Input @bind-Value="ViewModel.FirstName"
                                           Placeholder="Enter first name"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="firstName" />
                                    <ValidationMessage For="@(() => ViewModel.FirstName)"/>
                                </FormField>

                                <FormField Label="Last Name" Id="lastName">
                                    <Input @bind-Value="ViewModel.LastName"
                                           Placeholder="Enter last name"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="lastName" />
                                    <ValidationMessage For="@(() => ViewModel.LastName)"/>
                                </FormField>

                                <FormField Label="Email" Id="email">
                                    <Input @bind-Value="ViewModel.Email"
                                           Placeholder="Enter email address"
                                           Class="@TailwindStyleStrings.Form.Input"
                                           Id="email" />
                                    <ValidationMessage For="@(() => ViewModel.Email)"/>
                                </FormField>

                                <FormField Label="Biography" Id="biography">
                                    <TextArea @bind-Value="ViewModel.Biography"
                                              Placeholder="Tell us about yourself..."
                                              Rows="4"
                                              Class="@TailwindStyleStrings.Form.Input"
                                              Id="biography" />
                                    <ValidationMessage For="@(() => ViewModel.Biography)"/>
                                </FormField>
                            </div>

                            <!-- Experience & Qualifications Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-briefcase mr-2"></i>Experience & Qualifications
                                </h3>

                                <FormField Label="Years of Experience" Id="experience">
                                    <AntDesign.InputNumber @bind-Value="ViewModel.YearsOfExperience"
                                                 Min="0"
                                                 Max="50"
                                                 Placeholder="Years of experience"
                                                 Class="@TailwindStyleStrings.Form.InputNumber"
                                                 Id="experience" />
                                    <ValidationMessage For="@(() => ViewModel.YearsOfExperience)"/>
                                </FormField>

                                <FormField Label="Remote Experience" Id="remoteExperience">
                                    <AntDesign.InputNumber @bind-Value="ViewModel.YearsOfRemoteExperience"
                                                           Min="0"
                                                           Max="100"
                                                           Placeholder="Years of remote experience"
                                                           Class="@TailwindStyleStrings.Form.InputNumber"
                                                           Id="remoteExperience" />
                                    <ValidationMessage For="@(() => ViewModel.YearsOfRemoteExperience)"/>
                                </FormField>

                                <FormField Label="Hourly Rate (€)" Id="hourlyRate">
                                    <AntDesign.InputNumber TValue="double"
                                                           @bind-Value="@ViewModel.HourlyRateInEuros"
                                                           Placeholder="0.00"
                                                           Step="0.5"
                                                           Min="0"
                                                           Precision="2"
                                                           Class="@TailwindStyleStrings.Form.InputNumber"
                                                           Id="hourlyRate" />
                                    <ValidationMessage For="@(() => ViewModel.HourlyRateInEuros)"/>
                                </FormField>
                                <FormField Label="Qualifications" Id="qualifications">
                                    <Select Mode="SelectMode.Multiple"
                                            Placeholder="Select qualifications"
                                            @bind-Values="@ViewModel.SelectedQualifications"
                                            TItemValue="string"
                                            TItem="string"
                                            Style="width: 100%; margin-bottom: 8px;"
                                            EnableSearch
                                            AllowClear>
                                        <SelectOptions>
                                            @foreach (var qualification in Enum.GetValues<QualificationTypeIds>())
                                            {
                                                <SelectOption TItemValue="string" TItem="string" Value="@qualification.ToString()" Label="@qualification.AsString(EnumFormat.DisplayName)" />
                                            }
                                        </SelectOptions>
                                    </Select>
                                    <ValidationMessage For="@(() => ViewModel.SelectedQualifications)"/>
                                </FormField>
                            </div>

                            <!-- Availability Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-calendar-alt mr-2"></i>Availability
                                </h3>

                                <FormField Label="Working Days" Id="weekDays">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="space-y-3">
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Monday" Id="monday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Mon</span>
                                                </Checkbox>
                                            </div>
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Tuesday" Id="tuesday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Tue</span>
                                                </Checkbox>
                                            </div>
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Wednesday" Id="wednesday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Wed</span>
                                                </Checkbox>
                                            </div>
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Thursday" Id="thursday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Thu</span>
                                                </Checkbox>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Friday" Id="friday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Fri</span>
                                                </Checkbox>
                                            </div>
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Saturday" Id="saturday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Sat</span>
                                                </Checkbox>
                                            </div>
                                            <div class="flex items-center">
                                                <Checkbox @bind-Value="ViewModel.Sunday" Id="sunday" Class="text-teal-600 focus:ring-teal-500">
                                                    <span class="text-sm font-medium text-gray-700 ml-2">Sun</span>
                                                </Checkbox>
                                            </div>
                                        </div>
                                    </div>
                                </FormField>

                                <FormField Label="Working Hours" Id="workingHours">
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">Start Time</label>
                                            <TimePicker @bind-Value="ViewModel.RegularStartTime"
                                                        Format="HH:mm"
                                                        Placeholder="@("Select start time")"
                                                        Class="@TailwindStyleStrings.Form.TimePicker"
                                                        Id="startTime" />
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">End Time</label>
                                            <TimePicker @bind-Value="ViewModel.RegularEndTime"
                                                        Format="HH:mm"
                                                        Placeholder="@("Select end time")"
                                                        Class="@TailwindStyleStrings.Form.TimePicker"
                                                        Id="endTime" />
                                        </div>
                                    </div>
                                </FormField>
                            </div>
                        </div>
                        
                    @if (ValidationErrors.Any())
                    {
                        <div class="rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle h-5 w-5 text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            @foreach (var error in ValidationErrors)
                                            {
                                                <li>@error</li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                        <div class="flex justify-end gap-4 pt-4 border-t border-gray-200">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="NavigateToOperatorList">
                                Cancel
                            </Button>
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    HtmlType="submit"
                                    Loading="@this.IsSubmitting">
                                <i class="fas fa-save h-4 w-4 mr-2"></i>
                                Save Changes
                            </Button>
                        </div>
                    </div>
                </EditForm>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-500 mb-4"></i>
                    <p class="text-red-600">Failed to load operator data. Please try again.</p>
                    <Button Type="@ButtonType.Primary"
                            Class="@($"{TailwindStyleStrings.Button.Primary} mt-4")"
                            OnClick="NavigateToOperatorList">
                        Back to Operators
                    </Button>
                </div>
            }
        </div>
    </Card>
</div>

@code {

    #region METHODS - STATIC
    public static string GetUri(string operatorId)
    {
        return $"/my-assets/operators/edit/{Base64UrlEncoder.Encode(operatorId)}";
    }
    #endregion

}
