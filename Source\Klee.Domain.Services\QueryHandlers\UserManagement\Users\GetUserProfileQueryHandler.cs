﻿
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Threading.Tasks;
using System.Threading;
using System;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Queries.UserManagement.Users;
using Klee.Domain.Services.Repositories.UserManagement.Users;

namespace Klee.Domain.Services.QueryHandlers.UserManagement.Users;

    public sealed class GetUserProfileQueryHandler
        : QueryHandlerAsync<GetUserProfileQuery, UserProfile>
    {
        #region PROPERTIES
        private IUserProfileSrpRepository UserProfileSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetUserProfileQueryHandler(IUserProfileSrpRepository userProfileSrpRepository,
                                      IMemoryCache memoryCache)
        {
            this.UserProfileSrpRepository = userProfileSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<UserProfile> ExecuteAsync(GetUserProfileQuery query,
                                                         CancellationToken cancellationToken = new CancellationToken())
        {
            // Init
            string userProfileId = query.UserId;

            // Get UserProfile from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetUserProfileCacheId(userProfileId), out UserProfile cachedUserProfile);

            // Init
            UserProfile userProfile = cachedUserProfile;

            // Get UserProfile from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get UserProfile
                userProfile = await this.UserProfileSrpRepository.FindAsync(_ => _.UserId == userProfileId &&
                                                                         _.EntityPartitionKey == userProfileId, query);
                // Cache UserProfile
                this.MemoryCache.Set(MemoryCacheIds.GetUserProfileCacheId(userProfileId),
                                     userProfile, new MemoryCacheEntryOptions()
                                              {
                                                  AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                  Size = 1
                                              });
            }

            return userProfile;
        }
        #endregion
    }