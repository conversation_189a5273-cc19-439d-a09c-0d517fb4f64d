@layout OrganizationViewLayout
@using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleCreate
@using AntDesign
@using System.Linq.Expressions
@using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
@using Klee.Web.App.Components.UI

@page "/my-assets/vessels"

@inherits VesselsViewBase

<Modal Title="Delete Vessel"
       Visible="@this.DeleteModalVisible"
       OnOk="@this.OnConfirmDelete"
       OnCancel="@this.OnCancelDelete"
       OkText="@("Delete")"
       CancelText="@("Cancel")"
       OkButtonProps="@(new ButtonProps { Danger = true })">
    <div>
        <p>Are you sure you want to delete vessel '<strong>@CurrentVesselName</strong>'?</p>
    </div>
</Modal>

<div class="container py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">My Vessels</h1>

        @if(this.IsUserOrganizationAdmin){

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddVehicle">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add Vessel
            </Button>
        }

    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="overflow-x-auto">
            <Table TItem="OrganizationVehicleListItem"
                   DataSource="@this.ViewModel.OrganizationVehicles"
                   Class="@TailwindStyleStrings.Table.Container"
                   ScrollX="1200">
                <PropertyColumn Property="@(v => v.VehicleName)" Title="Name" Sortable Filterable Width="150">
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-ship h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span class="font-medium">@context.VehicleName</span>
                        </div>
                    </Template>
                </PropertyColumn>
                <PropertyColumn Property="@(v => v.VehicleTypeDisplayName)" Title="Type" Sortable Filterable Width="120" />
                <PropertyColumn Property="@(v => v.ENI)" Title="ENI" Sortable Filterable Width="100" />
                <PropertyColumn Property="@(v => v.Length)" Title="Length (m)" Sortable Width="100" Align="ColumnAlign.Right">
                    <Template>
                        <span class="font-mono">@context.Length.ToString("N2")</span>
                    </Template>
                </PropertyColumn>
                <PropertyColumn Property="@(v => v.Beam)" Title="Beam (m)" Sortable Width="100" Align="ColumnAlign.Right">
                    <Template>
                        <span class="font-mono">@context.Beam.ToString("N2")</span>
                    </Template>
                </PropertyColumn>
                <PropertyColumn Property="@(v => v.VesselTypeDisplayName)" Title="Vessel Type" Sortable Filterable Width="140">
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-anchor h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span>@context.VesselTypeDisplayName</span>
                        </div>
                    </Template>
                </PropertyColumn>
                <ActionColumn Title="Actions" Fixed="ColumnFixPlacement.Right" Width="120">
                    @if(this.IsUserOrganizationAdmin){
                        <Space>
                            <SpaceItem>
                                <Button Type="@ButtonType.Link"
                                        Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                        OnClick="@(() => this.OnClick_EditVehicle(context.VehicleId))">
                                    <i class="@($"fas fa-pencil-alt h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                    <span class="sr-only">Edit</span>
                                </Button>
                            </SpaceItem>
                            <SpaceItem>
                                <Button Type="@ButtonType.Link"
                                        Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                        OnClick="@(() => OnClick_DeleteVehicle(context.VehicleId, context.VehicleName))">
                                    <i class="@($"fas fa-trash h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                    <span class="sr-only">Delete</span>
                                </Button>
                            </SpaceItem>
                        </Space>
                    }
                </ActionColumn>
            </Table>
        </div>
    </Card>
</div>

@code {

    #region METHODS - STATIC
    public static string GetUri()
    {
        return "/my-assets/vessels";
    }
    #endregion

}