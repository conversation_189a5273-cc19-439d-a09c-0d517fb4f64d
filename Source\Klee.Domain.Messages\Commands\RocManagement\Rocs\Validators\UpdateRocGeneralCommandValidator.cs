﻿using FluentValidation;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;

namespace Klee.Domain.Messages.Commands.RocManagement.Rocs.Validators;

public class UpdateRocGeneralCommandValidator : AbstractValidator<UpdateRocGeneralCommand>
{
    public UpdateRocGeneralCommandValidator() { 
        this.RuleFor(_ => _.RocId).NotNull();
        this.RuleFor(_ => _.RocId).NotEmpty();
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
    }

}