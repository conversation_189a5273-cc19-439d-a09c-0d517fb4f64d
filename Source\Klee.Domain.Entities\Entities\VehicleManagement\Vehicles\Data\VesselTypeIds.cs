﻿using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.VehicleManagement.Vehicles.Data;

public enum VesselTypeIds {
    [Display(Name = "None")]
    None = 0,
    [Display(Name = "General Cargo")]
    GeneralCargo = 1,
    [Display(Name = "Bulk Carrier")]
    BulkCarrier = 2,
    [Display(Name = "Container Ship")]
    ContainerShip = 3,
    [Display(Name = "Oil Tanker")]
    OilTanker = 4,
    [Display(Name = "Chemical Tanker")]
    ChemicalTanker = 5,
    [Display(Name = "Gas Tanker")]
    GasTanker = 6,
    [Display(Name = "Other")]
    Other = 7,
}
