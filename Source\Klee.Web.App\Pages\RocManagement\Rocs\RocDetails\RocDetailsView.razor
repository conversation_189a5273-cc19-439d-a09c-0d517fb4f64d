﻿@using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Data
@using Microsoft.AspNetCore.Authorization
@using Microsoft.IdentityModel.Tokens
@using Renoir.Srp.Portal.Web.Application.Security
@using Microsoft.AspNetCore.Components.Authorization
@using Klee.Web.App.Pages.RocManagement.Rocs.RocUpdateGeneral
@using Klee.Web.App.Pages.StationManagement.Stations.StationCreate
@using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Stations

@page "/Rocs/Management/Roc/{RocIdEncoded}"

@inherits RocDetailsViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody @ref="MainLayoutBody"
                 PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 OnClickDelete="@OnClickDelete"
                 CanShowButtonDelete=true
                 OnActiveTabChanged="@OnMainLayoutBody_ActiveTabChanged"
                 ActiveTabId="@DetailsViewProperties.ActiveTabId">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Rocs"/>
        <RMainLayoutBodyBreadcrumbListItem Text="Details"/>
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
        <DxMenuItem Text="Add station" IconCssClass="fas fa-plus" Click="OnClickAddStation_OpenStationCreateDialogView"/>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarRightMenuItems>
    <TabNavItems>
        <RTabNavItem TabId="@RocDetailsViewTabIds.General" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemGeneral_IsActiveChanged">GENERAL</RTabNavItem>
        <RTabNavItem TabId="@RocDetailsViewTabIds.Stations" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemStations_IsActiveChanged">STATIONS</RTabNavItem>
    </TabNavItems>
    <TabContentItems>
        <RTabContentItem @ref="@TabContentItemGeneral"
                         TabId="@RocDetailsViewTabIds.General" MinHeight="200">
            <div class="row">
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Identification</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenRocUpdateGeneralDialogView"
                                                         CanShow=@AuthenticatedUser.IsAdminApp() />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="ROC Id" Value="@ViewModel.RocId" />
                                    <RControlTextValueDisplay Label="ROC Name" Value="@ViewModel.RocName" />
                                    <RControlTextValueDisplay Label="Address" Value="@ViewModel.Address" />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                                <section class="card">
                                    <header class="card-header">
                                        <h2 class="card-title">System</h2>
                                        <div class="card-actions">
                                            <RCardActionToggle />
                                        </div>
                                    </header>
                                    <div class="card-body">
                                        <RControlTextValueDisplay Label="Environment" Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                                        <RControlCheckBoxValueDisplay Label="Active" Value="@ViewModel.IsActive" />
                                    </div>
                                </section>
                            </AuthorizeView>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
        <RTabContentItem @ref="@TabContentItemStations"
                         TabId="@RocDetailsViewTabIds.Stations" MinHeight="200">
            <div class="row">
                <div class="">
                    <div class="row">
                        <div class="">
                            <RocDetailsViewStationsComponent @ref="RocDetailsViewStationComponentRef" RocId="@this.RocId"/>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
    </TabContentItems>
    <Content>
    </Content>
</RMainLayoutBody>

<RocUpdateGeneralDialogView @ref="RocUpdateGeneralDialogView"
                            OnDialogViewOpened="OnRocUpdateGeneralDialogView_Opened"
                            OnDialogViewClosed="OnRocUpdateGeneralDialogView_Closed" />

<StationCreateDialogView @ref="StationCreateDialogView"
                         OnDialogViewOpened="OnStationCreateDialogView_Opened"
                         OnDialogViewClosed="OnStationCreateDialogView_Closed" />
@code {

    #region METHODS - STATIC
    public static string GetUri(string rocId)
    {
        return $"/Rocs/Management/Roc/{Base64UrlEncoder.Encode(rocId)}";
    }
    #endregion

}