using System.Collections.Generic;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Renoir.Application.Messages.Queries.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Domain.Messages.Queries.OrganizationManagement.Organizations
{
    public class GetOrganizationListQuery
        : QueryBase<IReadOnlyList<OrganizationListItem>>
    {
        #region PROPERTIES
        public bool AllowCached { get; set; } = false;

        /// <summary>
        /// == true     => only active,
        /// == false    => only inactive,
        /// == null     => no specific filtering
        /// </summary>
        public bool? IsActive { get; set; } = null;
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationListQuery(IQueryContext context)
            : base(context)
        {
        }
        #endregion
    }
} 