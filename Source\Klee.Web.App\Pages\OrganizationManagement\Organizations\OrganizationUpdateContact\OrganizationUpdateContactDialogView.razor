﻿@using Renoir.Web.Razor.Components.DialogsEditForm


@inherits Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateGeneral.OrganizationUpdateContactDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 Title="Update Organization"
                 EditContext="@ViewModelEditContext"
                 OnDialogResetting="@OnDialogResetting"
                 OnDialogOpening="@OnDialogOpening"
                 OnDialogOpened="@OnDialogOpened"
                 OnDialogClosed="@OnDialogClosed"
                 OnValidSubmit="@OnValidSubmit"
                 OnInvalidSubmit="@OnInvalidSubmit">

    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Organization Id" IsRequired="true">
                <DxTextBox @bind-Text="@ViewModel.OrganizationId" ReadOnly="true" />
                <ValidationMessage For="@(() => ViewModel.OrganizationId)" />
            </RControlValueEdit>

            <RControlValueEdit Label="Address">
                <DxTextBox @bind-Text="ViewModel.Address"/>
                <ValidationMessage For="@(() => ViewModel.Address)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Email">
                <DxTextBox @bind-Text="ViewModel.Email"/>
                <ValidationMessage For="@(() => ViewModel.Email)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-12">
            <RControlValueEdit Label="Phone">
                <DxTextBox @bind-Text="ViewModel.Phone"/>
                <ValidationMessage For="@(() => ViewModel.Phone)"/>
            </RControlValueEdit>
        </div>
    </div>
 
</RDialogEditForm>