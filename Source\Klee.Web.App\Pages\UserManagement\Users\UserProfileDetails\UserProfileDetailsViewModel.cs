﻿using EnumsNET;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Messages.Queries.UserManagement.Users;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileDetails.Data;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileDetails;

    public class UserProfileDetailsViewModel 
        : ViewModelBase<UserProfileDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - ENTITY
        public UserProfile UserProfile { get; private set; }
        #endregion

        #region PROPERTIES - VIEW
        public string UserProfileId { get; private set; } = "";

        public string FirstName { get; private set; } = "";
        public string LastName { get; private set; } = "";
        public string Email { get; private set; } = "";
        public string DisplayName { get; private set; } = "";
        public bool IsActive { get; private set; }
        public string CreatedBy { get; private set; } = "";
        public string CreatedByName { get; private set; } = "";
        public string CreatedDateTimeRstAsString { get; private set; } = "";
        public string ModifiedBy { get; private set; } = "";
        public string ModifiedByName { get; private set; } = "";
        public string ModifiedDateTimeRstAsString { get; private set; } = "";
        #endregion

        #region CONSTRUCTORS
        public UserProfileDetailsViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadUserProfileAsync(string userProfileId,
                                                 bool forceLoad = false)
        {
            if(this.UserProfileId != userProfileId ||
               forceLoad)
            {
                // Get UserProfile
                UserProfile userProfile = await this.SrpQueryProcessor.ExecuteAsync(new GetUserProfileQuery(userProfileId, this.SrpQueryContext)) ?? new UserProfile();
                
                // Set UserProfile
                this.FirstName = userProfile.FirstName;
                this.LastName = userProfile.LastName;
                this.Email = userProfile.Email;
                this.DisplayName = userProfile.DisplayName;
                this.UserProfileId = userProfile.UserId;
                this.IsActive = userProfile.IsActive ?? false;
                this.CreatedBy = userProfile.CreatedBy;
                this.CreatedByName = userProfile.GetCreatedByName();
                this.CreatedDateTimeRstAsString = userProfile.GetCreatedDateTimeRstAsString();
                this.ModifiedBy = userProfile.ModifiedBy;
                this.ModifiedByName = userProfile.GetModifiedByName();
                this.ModifiedDateTimeRstAsString = userProfile.GetModifiedDateTimeRstAsString();

                // Set UserProfile
                this.UserProfile = userProfile;

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteUserProfileAsync(string userProfileId)
        {
            try
            {
                // Delete UserProfile
                await this.SrpCommandProcessor.SendAsync(new DeleteUserProfileCommand(userProfileId, 
                                                                            this.SrpCommandContext));
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }