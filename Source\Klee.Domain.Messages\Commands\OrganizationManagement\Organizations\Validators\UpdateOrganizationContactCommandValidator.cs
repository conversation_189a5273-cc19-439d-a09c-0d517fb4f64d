﻿using FluentValidation;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;

public class UpdateOrganizationContactCommandValidator  : AbstractValidator<UpdateOrganizationContactCommand>{
    public UpdateOrganizationContactCommandValidator()
    {
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
    }
}