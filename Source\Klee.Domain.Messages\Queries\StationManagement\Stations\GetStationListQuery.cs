﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.StationManagement.Stations;

public class GetStationListQuery
    : QueryBase<IReadOnlyList<StationListItem>>
{
    #region PROPERTIES
    public bool AllowCached { get; set; } = false;

    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetStationListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}