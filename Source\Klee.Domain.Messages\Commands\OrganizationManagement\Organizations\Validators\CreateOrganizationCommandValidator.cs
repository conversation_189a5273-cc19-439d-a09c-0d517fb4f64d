﻿using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators
{
    public class CreateOrganizationCommandValidator : AbstractValidator<CreateOrganizationCommand>
    {
        public CreateOrganizationCommandValidator()
        {
            this.RuleFor(_ => _.SoftwareEnvironmentId).Must(this.SoftwareEnvironmentIdMustBeValid).WithMessage("Software Environment is not valid");
            this.RuleFor(_ => _.Name).NotNull();
            this.RuleFor(_ => _.Name).NotEmpty();
        }

        private bool SoftwareEnvironmentIdMustBeValid(SoftwareEnvironmentIds softwareEnvironmentId)
        {
            return softwareEnvironmentId != SoftwareEnvironmentIds.None;
        }

        private bool OrganizationIdMustStartWithCharacters(string vehicleId)
        {
            return vehicleId.StartsWith("O") ||
                   vehicleId.StartsWith("Org") ||
                   vehicleId.StartsWith("Tst");
        }

        private bool OrganizationIdMustEndWithNumbers(string vehicleId)
        {
            string vehicleIdEnd =
                vehicleId.Replace("Org", "")
                    .Replace("O", "")
                    .Replace("Tst", "");

            return int.TryParse(vehicleIdEnd, out int result);
        }
    }
}