using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VoyagePlanning;

public sealed class GetAvailableCaptainsForVoyageQueryHandler
    : QueryHandlerAsync<GetAvailableCaptainsForVoyageQuery, IReadOnlyList<AvailableCaptainListItem>>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IVoyageSrpRepository VoyageSrpRepository { get; }
    #endregion

    #region CONSTRUCTORS
    public GetAvailableCaptainsForVoyageQueryHandler(IOperatorSrpRepository operatorSrpRepository, IVoyageSrpRepository voyageSrpRepository)
    {
        this.OperatorSrpRepository = operatorSrpRepository;
        this.VoyageSrpRepository = voyageSrpRepository;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<AvailableCaptainListItem>> ExecuteAsync(GetAvailableCaptainsForVoyageQuery query,
                                                                        CancellationToken cancellationToken = new CancellationToken())
    {
        // Calculate time buffer for availability check
        TimeSpan buffer = TimeSpan.FromMinutes(15);
        DateTime queryStartWithBuffer = query.VoyageStartDateTime.ToUniversalTime().Subtract(buffer);
        DateTime queryEndWithBuffer = query.VoyageEndDateTime.ToUniversalTime().Add(buffer);

        // Single query: Get all active operators with their conflicting voyages in one operation
        var operatorsWithConflicts = await (
            from op in OperatorSrpRepository.Entities(query)
                .Include(op => op.Organization)
                .Where(op => op.IsActive == true)
            from voyage in VoyageSrpRepository.Entities(query)
                .Where(v => v.IsActive == true)
                .Where(v => v.OperatorId == op.OperatorId)
                .Where(v => v.EndDateTime > queryStartWithBuffer && v.StartDateTime < queryEndWithBuffer)
                .DefaultIfEmpty() // LEFT JOIN - include operators even if they have no conflicting voyages
            select new { Operator = op, ConflictingVoyage = voyage }
        ).ToListAsync(cancellationToken);

        // Group by operator and filter out those with conflicts
        List<Operator> availableOperators = operatorsWithConflicts
            .GroupBy(x => x.Operator.OperatorId)
            .Where(g => g.All(x => x.ConflictingVoyage == null)) // Only operators with no conflicting voyages
            .Select(g => g.First().Operator) // Take the operator from the group
            .ToList();

        // Filter operators based on voyage requirements and create result list
        List<AvailableCaptainListItem> availableCaptains = availableOperators
            .Where(op => HasRequiredQualifications(op.Qualifications, query.RequiredQualifications))
            .Where(op => IsAvailableForVoyageDates(op, query.VoyageStartDateTime, query.VoyageEndDateTime))
            .Select(op => new AvailableCaptainListItem
            {
                EntityId = op.EntityId,
                OperatorId = op.OperatorId,
                FirstName = op.FirstName,
                LastName = op.LastName,
                Email = op.OperatorEmail,
                Biography = op.Biography,
                OrganizationName = op.Organization.Name,
                OrganizationId = op.OrganizationId,
                YearsOfExperience = op.YearsOfExperience,
                YearsOfRemoteExperience = op.YearsOfRemoteExperience,
                HourlyRateInEuros = op.HourlyRateInEuros,
                Qualifications = op.Qualifications,
                WorkingDays = op.WorkingDays,
                RegularStartTime = op.RegularStartTime.ToLocalTime(),
                RegularEndTime = op.RegularEndTime.ToLocalTime(),
                IsActive = op.IsActive ?? false
            })
            .OrderBy(op => op.HourlyRateInEuros)
            .ThenBy(op => op.LastName)
            .ToList();

        return availableCaptains;
    }

    private bool HasRequiredQualifications(List<QualificationTypeIds> operatorQualifications, List<QualificationTypeIds> requiredQualifications)
    {
        //No requirements needed
        if (!requiredQualifications.Any())
            return true;

        return requiredQualifications.All(required => operatorQualifications.Contains(required));
    }

    private bool IsAvailableForVoyageDates(Operator operatorData, DateTime voyageStart, DateTime voyageEnd)
    {
        // Basic availability check - can be enhanced with more complex scheduling logic
        // For now, check if operator works on the days of the week for the voyage period
        DayOfWeek startDay = voyageStart.DayOfWeek;
        WeekDaysIds operatorWorkingDays = operatorData.WorkingDays;

        bool isAvailable = startDay switch {
            DayOfWeek.Monday => operatorWorkingDays.HasFlag(WeekDaysIds.Monday),
            DayOfWeek.Tuesday => operatorWorkingDays.HasFlag(WeekDaysIds.Tuesday),
            DayOfWeek.Wednesday => operatorWorkingDays.HasFlag(WeekDaysIds.Wednesday),
            DayOfWeek.Thursday => operatorWorkingDays.HasFlag(WeekDaysIds.Thursday),
            DayOfWeek.Friday => operatorWorkingDays.HasFlag(WeekDaysIds.Friday),
            DayOfWeek.Saturday => operatorWorkingDays.HasFlag(WeekDaysIds.Saturday),
            DayOfWeek.Sunday => operatorWorkingDays.HasFlag(WeekDaysIds.Sunday),
            _ => false
        };

        // Check if the operator's working hours overlap with the voyage start and end times
        if (isAvailable)
        {
            DateTime operatorStart = operatorData.RegularStartTime;
            DateTime operatorEnd = operatorData.RegularEndTime;
            // Check if the voyage start and end times fall within the operator's working hours
            isAvailable = voyageStart.TimeOfDay >= operatorStart.TimeOfDay &&
                          voyageEnd.TimeOfDay <= operatorEnd.TimeOfDay;
        }
        return isAvailable;
    }

    #endregion
}
