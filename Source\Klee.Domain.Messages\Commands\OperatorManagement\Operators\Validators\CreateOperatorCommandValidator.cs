﻿using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;

public class CreateOperatorCommandValidator : AbstractValidator<CreateOperatorCommand> {
    public CreateOperatorCommandValidator() {
        this.RuleFor(_ => _.SoftwareEnvironmentId).Must(this.SoftwareEnvironmentIdMustBeValid)
            .WithMessage("Software Environment is not valid");
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
    }

    private bool SoftwareEnvironmentIdMustBeValid(SoftwareEnvironmentIds softwareEnvironmentId) {
        return softwareEnvironmentId != SoftwareEnvironmentIds.None;
    }

}