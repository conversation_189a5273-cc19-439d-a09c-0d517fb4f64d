using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Data;
using EnumsNET;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using System.Collections.Generic;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails
{
    public class OrganizationDetailsViewModel
        : ViewModelBase<OrganizationDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - ENTITY
        public Organization Organization { get; private set; }
        #endregion

        #region PROPERTIES - VIEW
        public string OrganizationId { get; private set; } = "";
        public string OrganizationName { get; private set; } = "";
        public string Code { get; private set; } = "";
        public string Description { get; private set; } = "";
        public string ContactEmail { get; private set; } = "";
        public string ContactPhone { get; private set; } = "";
        public string Address { get; private set; } = "";
        public string CreatedBy { get; private set; } = "";
        public string CreatedByName { get; private set; } = "";
        public string CreatedDateTimeRstAsString { get; private set; } = "";
        public string ModifiedBy { get; private set; } = "";
        public string ModifiedByName { get; private set; } = "";
        public string ModifiedDateTimeRstAsString { get; private set; } = "";
        public string SoftwareEnvironmentDisplayName { get; private set; } = "";
        public bool IsActive { get; private set; }

        // Relationship properties
        public IReadOnlyList<string> VehicleIds { get; private set; } = new List<string>();
        public IReadOnlyList<string> OperatorIds { get; private set; } = new List<string>();
        public IReadOnlyList<string> RocIds { get; private set; } = new List<string>();
        #endregion

        #region CONSTRUCTORS
        public OrganizationDetailsViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData();
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationAsync(string organizationId, bool forceLoad = false)
        {
            if (this.OrganizationId != organizationId || forceLoad)
            {
                // Get Organization
                Organization organization = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationQuery(organizationId, this.SrpQueryContext)) ?? new Organization();

                // Set Organization
                this.OrganizationId = organization.OrganizationId;
                this.OrganizationName = organization.Name;
                this.Code = organization.Code;
                this.Description = organization.Description;
                this.ContactEmail = organization.ContactEmail;
                this.ContactPhone = organization.ContactPhone;
                this.Address = organization.Address;
                this.CreatedBy = organization.CreatedBy;
                this.CreatedByName = organization.GetCreatedByName();
                this.CreatedDateTimeRstAsString = organization.GetCreatedDateTimeRstAsString();
                this.ModifiedBy = organization.ModifiedBy;
                this.ModifiedByName = organization.GetModifiedByName();
                this.ModifiedDateTimeRstAsString = organization.GetModifiedDateTimeRstAsString();
                this.SoftwareEnvironmentDisplayName = organization.SoftwareEnvironmentId.AsString(EnumFormat.DisplayName);
                this.IsActive = organization.IsActive ?? false;
                
                // Set Entity
                this.Organization = organization;

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteOrganizationAsync(string organizationId)
        {
            try
            {
                // Delete Organization
                await this.SrpCommandProcessor.SendAsync(new DeleteOrganizationCommand(organizationId, this.SrpCommandContext));
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }
}