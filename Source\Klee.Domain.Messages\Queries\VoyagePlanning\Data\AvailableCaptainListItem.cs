using System;
using System.Collections.Generic;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;

namespace Klee.Domain.Messages.Queries.VoyagePlanning.Data;

public class AvailableCaptainListItem
{
    #region PROPERTIES
    public long EntityId { get; set; }
    public string OperatorId { get; set; } = "";
    public string FirstName { get; set; } = "";
    public string LastName { get; set; } = "";
    public string Email { get; set; } = "";
    public string Biography { get; set; } = "";
    public string OrganizationName { get; set; } = "";
    public string OrganizationId { get; set; } = "";
    public int YearsOfExperience { get; set; } = 0;
    public int YearsOfRemoteExperience { get; set; } = 0;
    public double HourlyRateInEuros { get; set; } = 0.0;
    public List<QualificationTypeIds> Qualifications { get; set; } = new();
    public WeekDaysIds WorkingDays { get; set; } = WeekDaysIds.None;
    public DateTime RegularStartTime { get; set; } = DateTime.Today;
    public DateTime RegularEndTime { get; set; } = DateTime.Today;
    public bool IsActive { get; set; } = true;
    
    // Computed properties for display
    public string FullName => $"{FirstName} {LastName}";
    public string ExperienceDisplay => $"{YearsOfExperience} years ({YearsOfRemoteExperience} remote)";
    public string WorkingHoursDisplay => $"{RegularStartTime:HH:mm} - {RegularEndTime:HH:mm}";
    #endregion
}
