using System;
using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.OperatorManagement.Operators.Data;

[Flags]
public enum WeekDaysIds
{
    [Display(Name = "None")]
    None = 0,
    [Display(Name = "Monday")]
    Monday = 1,
    [Display(Name = "Tuesday")]
    Tuesday = 2,
    [Display(Name = "Wednesday")]
    Wednesday = 4,
    [Display(Name = "Thursday")]
    Thursday = 8,
    [Display(Name = "Friday")]
    Friday = 16,
    [Display(Name = "Saturday")]
    Saturday = 32,
    [Display(Name = "Sunday")]
    Sunday = 64,
}
