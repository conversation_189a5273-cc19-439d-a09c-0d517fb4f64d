using System;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Renoir.Application.Messages.Queries.Common;
using Renoir.Application.Validations.Helpers;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorEdit;

public class EditOperatorViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES

    public string OperatorId { get; private set; } = "";

    public string FirstName { get; set; } = "";

    public string LastName { get; set; } = "";

    [EmailAddress]
    public string Email { get; set; } = "";

    public string Biography { get; set; } = "";

    public int YearsOfExperience { get; set; } = 0;

    public int YearsOfRemoteExperience { get; set; } = 0;

    [Range(0.00, 1000.00, ErrorMessage = "Hourly rate must be between €0.01 and €1000.00")]
    public double HourlyRateInEuros { get; set; } = 0.0;

    public IEnumerable<string> SelectedQualifications { get; set; } = new List<string>();

    // Working Days - Individual day selection
    public bool Monday { get; set; } = true;
    public bool Tuesday { get; set; } = true;
    public bool Wednesday { get; set; } = true;
    public bool Thursday { get; set; } = true;
    public bool Friday { get; set; } = true;
    public bool Saturday { get; set; } = false;
    public bool Sunday { get; set; } = false;

    // Working Hours - Start and End times
    public DateTime RegularStartTime { get; set; } = DateTime.Today.AddHours(9); // 9:00 AM
    public DateTime RegularEndTime { get; set; } = DateTime.Today.AddHours(17); // 5:00 PM
    #endregion

    #region CONSTRUCTORS
    public EditOperatorViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task LoadOperatorAsync(string operatorId)
    {
        
        // Get query context
        IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();

        // Load the operator
        Operator operatorEntity = await _srpProcessors.QueryProcessor.ExecuteAsync(
            new GetOrganizationOperatorQuery(operatorId, queryContext));

        // Map operator data to view model
        OperatorId = operatorEntity.OperatorId;
        FirstName = operatorEntity.FirstName;
        LastName = operatorEntity.LastName;
        Email = operatorEntity.OperatorEmail;
        HourlyRateInEuros = operatorEntity.HourlyRateInEuros;
        SelectedQualifications = operatorEntity.Qualifications?.Select(q => q.ToString()).ToList() ?? new List<string>();

        // Map new properties from entity
        Biography = operatorEntity.Biography;
        YearsOfExperience = operatorEntity.YearsOfExperience;
        YearsOfRemoteExperience = operatorEntity.YearsOfRemoteExperience;

        // Map working days from enum to checkboxes
        SetCheckboxesFromWorkingDays(operatorEntity.WorkingDays);

        // Map working hours
        RegularStartTime = operatorEntity.RegularStartTime;
        RegularEndTime = operatorEntity.RegularEndTime;
  
    }

    private async Task<UpdateOrganizationOperatorGeneralCommand> NewUpdateOrganizationOperatorGeneralCommandAsync()
    {
        ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
        
        return new UpdateOrganizationOperatorGeneralCommand(this.OperatorId, commandContext)
        {
            FirstName = FirstName,
            LastName = LastName,
            Email = Email,
            HourlyRateInEuros = HourlyRateInEuros,
            YearsOfExperience = YearsOfExperience,
            YearsOfRemoteExperience = YearsOfRemoteExperience,
            WeekDays = GetWorkingDaysFromCheckboxes(),
            RegularStartTime = RegularStartTime,
            RegularEndTime = RegularEndTime,
            Biography = Biography
        };
    }

    public async Task UpdateOperatorAsync()
    {
        try
        {
            // Get command context
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();

            // Update general information
            UpdateOrganizationOperatorGeneralCommand updateGeneralCommand = await NewUpdateOrganizationOperatorGeneralCommandAsync();

            await _srpProcessors.CommandProcessor.SendAsync(updateGeneralCommand);

            // Update qualifications
            var updateQualificationsCommand = new UpdateOrganizationOperatorQualificationsCommand(OperatorId, commandContext)
            {
                Qualifications = SelectedQualifications.Select(s => Enum.Parse<QualificationTypeIds>(s)).ToList()
            };

            await _srpProcessors.CommandProcessor.SendAsync(updateQualificationsCommand);

        }
        catch
        {
            throw;
        }
    }

    private WeekDaysIds GetWorkingDaysFromCheckboxes()
    {
        WeekDaysIds weekDays = WeekDaysIds.None;

        if (Monday) weekDays |= WeekDaysIds.Monday;
        if (Tuesday) weekDays |= WeekDaysIds.Tuesday;
        if (Wednesday) weekDays |= WeekDaysIds.Wednesday;
        if (Thursday) weekDays |= WeekDaysIds.Thursday;
        if (Friday) weekDays |= WeekDaysIds.Friday;
        if (Saturday) weekDays |= WeekDaysIds.Saturday;
        if (Sunday) weekDays |= WeekDaysIds.Sunday;

        return weekDays;
    }

    private void SetCheckboxesFromWorkingDays(WeekDaysIds weekDays)
    {
        Monday = weekDays.HasFlag(WeekDaysIds.Monday);
        Tuesday = weekDays.HasFlag(WeekDaysIds.Tuesday);
        Wednesday = weekDays.HasFlag(WeekDaysIds.Wednesday);
        Thursday = weekDays.HasFlag(WeekDaysIds.Thursday);
        Friday = weekDays.HasFlag(WeekDaysIds.Friday);
        Saturday = weekDays.HasFlag(WeekDaysIds.Saturday);
        Sunday = weekDays.HasFlag(WeekDaysIds.Sunday);
    }
    #endregion

    #region METHODS - VALIDATE
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        UpdateOrganizationOperatorGeneralCommandValidator commandValidator = new UpdateOrganizationOperatorGeneralCommandValidator();
        (await commandValidator.ValidateAsync(await this.NewUpdateOrganizationOperatorGeneralCommandAsync()))
            .AddTo(validationResults);

        return validationResults;
    }
    #endregion
}
