﻿using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using EnumsNET;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.RocManagement.Rocs;

   public sealed class GetRocListQueryHandler
        : QueryHandlerAsync<GetRocListQuery, IReadOnlyList<RocListItem>>
    {
        #region PROPERTIES
        private IRocSrpRepository RocSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetRocListQueryHandler(IRocSrpRepository rocSrpRepository,
                                          IMemoryCache memoryCache)
        {
            this.RocSrpRepository = rocSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<RocListItem>> ExecuteAsync(GetRocListQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            // Get RocListItems from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetRocListCacheId, out List<RocListItem> cachedRocListItems);

            // Init
            List<RocListItem> rocListItems = cachedRocListItems ?? new List<RocListItem>();

            // Get RocListItems from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get VehicleListItems
                rocListItems =
                    await this.RocSrpRepository
                        .Entities(query)
                        .OrderBy(_ => _.RocId)
                        .Select(_ => new RocListItem()
                                   {
                                       EntityId = _.EntityId,
                                       RocId = _.RocId,
                                       RocName = _.RocName,
                                       Address = _.Address,
                                       OrganizationId = _.OrganizationId,
                                       IsActive = _.IsActive ?? false
                                   })
                        .ToListAsync(cancellationToken: cancellationToken);

                // Cache VehicleListItems
                this.MemoryCache.Set(MemoryCacheIds.GetRocListCacheId,
                    rocListItems, new MemoryCacheEntryOptions()
                                                       {
                                                           AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                           Size = 1
                                                       });
            }

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                rocListItems = rocListItems.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return rocListItems;
        }
        #endregion
    }