﻿using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs.Data;
using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;

namespace Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;

public class GetOrganizationRocListQuery
    : QueryBase<IReadOnlyList<OrganizationRocListItem>>
{
    #region PROPERTIES
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationRocListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}