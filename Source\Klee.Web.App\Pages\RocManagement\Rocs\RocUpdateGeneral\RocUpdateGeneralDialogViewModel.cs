﻿using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.RocManagement.Rocs;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocUpdateGeneral;

public class RocUpdateGeneralDialogViewModel
        : ViewModelBase<RocUpdateGeneralDialogViewModel>
    {
        #region PROPERTIES
        [Required]
        [Display(Name = "ROC Id")]
        public string RocId { get; set; } = "";

        [Required]
        [Display(Name = "ROC Name")]
        public string RocName { get; set; } = "";

        [Required]
        [Display(Name = "Address")]
        public string Address { get; set; } = "";

        public string OrganizationId { get; set; } = "";
        #endregion

        #region CONSTRUCTORS
        public RocUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.RocId = "";
            this.RocName = "";
            this.OrganizationId = "";
            this.Address = "";
        }

        public async Task LoadRocAsync(string rocId)
        {
            // Get Roc
            Roc roc = await this.SrpQueryProcessor.ExecuteAsync(new GetRocQuery(rocId, this.SrpQueryContext)) ?? new Roc();

            // Set Roc
            this.RocId = roc.RocId;
            this.OrganizationId = roc.OrganizationId;
            this.RocName = roc.RocName;
            this.Address = roc.Address;
        }
        #endregion

        #region METHODS
        public async Task UpdateRocAsync()
        {
            // Init
            UpdateRocGeneralCommand updateRocGeneralCommand = new UpdateRocGeneralCommand(this.RocId,
                                                                                          this.OrganizationId,
                                                                                          this.SrpCommandContext)
                                                                      {
                                                                          RocName = this.RocName,
                                                                          Address = this.Address
                                                                      };

            // Update Roc
            await this.SrpCommandProcessor
                      .SendAsync(updateRocGeneralCommand, false);
        }
        #endregion
    }