using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;
using Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations.Helpers;
using Klee.Domain.Services.MessagingService;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations
{
    public sealed class CreateOrganizationCommandHandler
        : RequestHandlerAsync<CreateOrganizationCommand>
    {
        #region PROPERTIES
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateOrganizationCommandHandler(IOrganizationSrpRepository organizationSrpRepository,
                                                IMemoryCache memoryCache)
        {
            OrganizationSrpRepository = organizationSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateOrganizationCommandValidator))]
        public override async Task<CreateOrganizationCommand> HandleAsync(CreateOrganizationCommand command,
                                                                          CancellationToken cancellationToken = new CancellationToken())
        {
            // Create Organization 

            Organization organization = new Organization()
            {
                Name = command.Name,
                Description = command.Description,
                Code = command.Code,
                SoftwareEnvironmentId = command.SoftwareEnvironmentId,
                ContactEmail = command.ContactEmail,
                ContactPhone = command.ContactPhone,
                Address = command.Address,
            };

            // Save
            await OrganizationSrpRepository.AddAsync(organization, command);

            // Set Result
            command.Result.EntityId = organization.EntityId;

            // Clear Caches
            MemoryCache.RemoveOrganization(organization.OrganizationId);

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}