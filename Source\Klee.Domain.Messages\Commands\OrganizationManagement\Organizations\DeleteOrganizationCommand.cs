using Paramore.Brighter;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations
{
    public sealed class DeleteOrganizationCommand : CommandBase, IRequest
    {
        #region PROPERTIES
        public string OrganizationId { get; }
        #endregion

        #region CONSTRUCTORS
        public DeleteOrganizationCommand(string organizationId, ICommandContext commandContext)
            : base(commandContext)
        {
            OrganizationId = organizationId;
        }
        #endregion
    }
}