﻿using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users.Validators;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Renoir.Application.Validations.Helpers;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileCreate;

    public class UserProfileCreateDialogViewModel
        : ViewModelBase<UserProfileCreateDialogViewModel>, IValidatableObject
    {
        #region FIELDS
        #endregion

        #region PROPERTIES - STATIC
        #endregion

        #region PROPERTIES
        public IReadOnlyList<OrganizationNameListItem> OrganizationNames { get; set; } = new List<OrganizationNameListItem>();

        [Required]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = "";

        [Required]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = "";

        [Display(Name = "Display Name")]
        public string DisplayName { get; set; } = "";

        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = "";

        [Required]
        [Display(Name = "Organization Name")]
        public string SelectedOrganizationId { get; set; } = "";

        #endregion

        #region PROPERTIES - CONVERTED
        #endregion

        #region CONSTRUCTORS
        public UserProfileCreateDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear() {
            this.FirstName = "";
            this.LastName = "";
            this.DisplayName = "";
            this.Email = "";
            this.SelectedOrganizationId = "";
            this.OrganizationNames = new List<OrganizationNameListItem>();
    }
        #endregion

        #region METHODS - VALIDATE
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateUserProfileCommandValidator createUserProfileCommandValidator = new CreateUserProfileCommandValidator();
            createUserProfileCommandValidator.Validate(this.NewCreateUserProfileCommand())
                                         .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationNamesAsync()
        {
            // Load organizations
            this.OrganizationNames = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationNameListQuery(this.SrpQueryContext)
            {
                IsActive = false
            });
            
            this.OrganizationNames = this.OrganizationNames
                .OrderBy(orgListItem => orgListItem.Name)
                .ToList();

            // Notify
            await this.InvokeStateHasChangedOnHostAsync();

            return true;
        }

        private CreateUserProfileCommand NewCreateUserProfileCommand() {
            return new CreateUserProfileCommand(this.Email,
                                                this.SelectedOrganizationId,
                                                this.SrpCommandContext)
                   {
                       FirstName = this.FirstName,
                       LastName = this.LastName,
                       DisplayName = this.DisplayName,
                   };
        }

        public async Task CreateUserProfileAsync()
        {
            // Init
            CreateUserProfileCommand createUserProfileCommand = this.NewCreateUserProfileCommand();

            // Create UserProfile
            await this.SrpCommandProcessor
                      .SendAsync(createUserProfileCommand, false);
        }
        #endregion
    }