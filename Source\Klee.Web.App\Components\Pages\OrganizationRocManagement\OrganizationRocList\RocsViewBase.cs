using AntDesign;
using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocCreate;
using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocEdit;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;


namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocList;

public class RocsViewBase: ComponentBase
{
    #region PROPERTIES - INJECTED
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject] 
    protected ILogger<RocsViewBase> Logger { get; set; }

    [Inject] 
    protected NotificationService NotificationService { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }
    #endregion

    #region PROPERTIES - STATE
    protected RocsViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    protected bool DeleteModalVisible { get; set; } = false;
    protected string CurrentRocId { get; set; } = "";
    protected string CurrentRocName { get; set; } = "";

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        this.ViewModel = new RocsViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Load OrganizationVehicles
            await this.ViewModel.LoadOrganizationRocsAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            // Notify using AntDesign notifications
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Exception when setting parameters.",
                Duration = 6.0
            });

        }
    }
    #endregion

    #region DELETE MODAL


    protected async Task OnConfirmDelete()
    {
        try
        {
            // Delete Organization Vehicle
            await this.ViewModel.DeleteOrganizationRocAsync(CurrentRocId);

            // Close modal
            DeleteModalVisible = false;

            // Notify using AntDesign notifications
            this.NotificationService.Success(new NotificationConfig()
            {
                Message = "Deleted",
                Description = $"Roc '{CurrentRocName}' has been deleted successfully.",
                Duration = 4.5
            });

            // Clear current vessel data
            CurrentRocId = "";
            CurrentRocName = "";

            //Reload vehicles
           await this.ViewModel.LoadOrganizationRocsAsync(forceLoad: true);
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when deleting roc with id '{CurrentRocId}'", CurrentRocId);
            #endregion

            // Notify using AntDesign notifications
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Exception when deleting roc '{CurrentRocName}'",
                Duration = 6.0
            });
        }
    }

    protected void OnCancelDelete()
    {
        DeleteModalVisible = false;
        CurrentRocId = "";
        CurrentRocName = "";
    }
    #endregion

    #region EVENT HANDLERS
    protected void OnClick_AddRoc()
    {
        this.NavigationManager.NavigateTo(AddRoc.GetUri());
    }

    protected void OnClick_EditRoc(string id)
    {
        NavigationManager.NavigateTo(EditRoc.GetUri(id));
    }

    protected void OnClick_DeleteRoc(string rocId, string rocName)
    {
        CurrentRocId = rocId;
        CurrentRocName = rocName;
        DeleteModalVisible = true;
    }
    #endregion
}
