﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.UserManagement.Users.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.UserManagement.Users;

public class GetUserProfileListQuery
    : QueryBase<IReadOnlyList<UserProfileListItem>>
{
    #region PROPERTIES
    public bool AllowCached { get; set; } = false;

    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetUserProfileListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}