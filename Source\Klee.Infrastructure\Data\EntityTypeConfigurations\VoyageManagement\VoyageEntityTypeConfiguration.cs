using Klee.Domain.Entities.VoyageManagement.Voyages;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.VoyageManagement
{
    public class VoyageEntityTypeConfiguration : IEntityTypeConfiguration<Voyage>
    {
        public void Configure(EntityTypeBuilder<Voyage> builder)
        {
            builder.HasIndex(_ => _.VoyageId)
                   .IsUnique();
            builder.Property(_ => _.VoyageId)
                   .IsRequired();
            builder.Property(_ => _.BookingOrganizationId)
                   .IsRequired();
            builder.Property(_ => _.StartDateTime)
                   .IsRequired();
            builder.Property(_ => _.EndDateTime)
                   .IsRequired();
            builder.Property(_ => _.VehicleId)
                   .IsRequired();

            // Create index to improve performance of availability queries
            builder.HasIndex(v => new { v.OperatorId, v.StartDateTime, v.EndDateTime, v.IsActive })
                   .HasDatabaseName("IX_Voyage_OperatorAvailability");

            builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

            // Set up the relationship with booking organization
            builder
                .HasOne(v => v.BookingOrganization)
                .WithMany()
                .HasForeignKey(v => v.BookingOrganizationId)
                .HasPrincipalKey(o => o.OrganizationId)
                .IsRequired();

            // Set up the relationship with vehicle
            builder
                .HasOne(v => v.Vehicle)
                .WithMany()
                .HasForeignKey(v => v.VehicleId)
                .HasPrincipalKey(ve => ve.VehicleId)
                .IsRequired();

            // Set up the relationship with operator (optional)
            builder
                .HasOne(v => v.Operator)
                .WithMany()
                .HasForeignKey(v => v.OperatorId)
                .HasPrincipalKey(op => op.OperatorId)
                .IsRequired(false);

        }
    }
}
