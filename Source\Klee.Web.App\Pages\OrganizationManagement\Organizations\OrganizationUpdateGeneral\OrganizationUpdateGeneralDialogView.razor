@using Renoir.Web.Razor.Components.DialogsEditForm


@inherits OrganizationUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 Title="Update Organization"
                 EditContext="@ViewModelEditContext"
                 OnDialogResetting="@OnDialogResetting"
                 OnDialogOpening="@OnDialogOpening"
                 OnDialogOpened="@OnDialogOpened"
                 OnDialogClosed="@OnDialogClosed"
                 OnValidSubmit="@OnValidSubmit"
                 OnInvalidSubmit="@OnInvalidSubmit">

    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Organization Id" IsRequired="true">
                <DxTextBox @bind-Text="@ViewModel.OrganizationId" ReadOnly="true" />
                <ValidationMessage For="@(() => ViewModel.OrganizationId)" />
            </RControlValueEdit>

            <RControlValueEdit Label="Code">
                <DxTextBox @bind-Text="ViewModel.Code"/>
                <ValidationMessage For="@(() => ViewModel.Code)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.Name"/>
                <ValidationMessage For="@(() => ViewModel.Name)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Description">
                <DxMemo @bind-Text="ViewModel.Description"/>
                <ValidationMessage For="@(() => ViewModel.Description)"/>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>
