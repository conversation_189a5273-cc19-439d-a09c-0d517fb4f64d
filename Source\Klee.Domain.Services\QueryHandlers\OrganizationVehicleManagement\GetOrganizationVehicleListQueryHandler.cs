﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Domain.Services.UserContextService;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.OrganizationVehicleManagement;

public sealed class GetOrganizationVehicleListQueryHandler
    : QueryHandlerAsync<GetOrganizationVehicleListQuery, IReadOnlyList<OrganizationVehicleListItem>>
{
    #region PROPERTIES
    private IVehicleSrpRepository VehicleSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationVehicleListQueryHandler(IVehicleSrpRepository organizationVehicleSrpRepository,
        IUserContextHelperService userContextHelperService)
        {
            this.VehicleSrpRepository = organizationVehicleSrpRepository;
            this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<OrganizationVehicleListItem>> ExecuteAsync(GetOrganizationVehicleListQuery query,
                                                                            CancellationToken cancellationToken = new CancellationToken())
    {
        // Get Organization Vehicles from DB
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        List<OrganizationVehicleListItem> organizationVehicleListItems =
            await this.VehicleSrpRepository.Entities(query)
                      .Where(_ => _.OrganizationId == organizationId)
                      .Where(_ => _.IsActive == true)
                      .OrderBy(_ => _.VehicleId)
                      .Select(_ => new OrganizationVehicleListItem()
                                   {
                                       EntityId = _.EntityId,
                                       VehicleTypeDisplayName = _.VehicleTypeId.AsString(EnumFormat.DisplayName),
                                       VehicleId = _.VehicleId,
                                       VehicleName = _.VehicleName,
                                       ENI = _.ENI,
                                       HourlyRateInEuros = _.HourlyRateInEuros,
                                       Length = _.Length,
                                       Beam = _.Beam,
                                       VesselType = _.VesselType,
                                       VesselTypeDisplayName = _.VesselType.AsString(EnumFormat.DisplayName),
                      })
                      .ToListAsync(cancellationToken: cancellationToken);

        return organizationVehicleListItems;
    }
    #endregion
}