using System;

namespace Klee.Domain.Messages.Queries.VoyagePlanning.Data;

public class VesselAvailabilityResult
{
    #region PROPERTIES
    public bool IsAvailable { get; set; }
    public ConflictingVoyageInfo? ConflictingVoyage { get; set; }
    #endregion
}

public class ConflictingVoyageInfo
{
    #region PROPERTIES
    public Guid VoyageId { get; set; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public string Description { get; set; } = "";
    public string BookingOrganizationName { get; set; } = "";
    #endregion
}
