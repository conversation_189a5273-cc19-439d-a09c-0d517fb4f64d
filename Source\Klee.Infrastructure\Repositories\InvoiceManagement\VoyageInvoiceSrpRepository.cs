using System;
using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.InvoiceManagement
{
    public class VoyageInvoiceSrpRepository
        : EfDomainEntityRepository<AppSrpDbContext, VoyageInvoice, Guid>, IVoyageInvoiceSrpRepository
    {
        public VoyageInvoiceSrpRepository(AppSrpDbContext dbContext,
                                          IDbExceptionConverter dbExceptionConverter,
                                          ILogger<VoyageInvoiceSrpRepository> logger)
            : base(dbContext, dbExceptionConverter, (ILogger)logger)
        {
        }

        protected override void Dispose()
        {
            base.Dispose();
        }
    }
}
