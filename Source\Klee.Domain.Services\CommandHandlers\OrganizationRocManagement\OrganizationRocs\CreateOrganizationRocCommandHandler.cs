﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;

namespace Klee.Domain.Services.CommandHandlers.OrganizationRocManagement.OrganizationRocs;

public sealed class CreateOrganizationRocCommandHandler
    : RequestHandlerAsync<CreateOrganizationRocCommand>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
#endregion

#region CONSTRUCTORS
public CreateOrganizationRocCommandHandler(IRocSrpRepository rocSrpRepository, IUserContextHelperService userContextHelperService)
    {
        RocSrpRepository = rocSrpRepository;
        UserContextHelperService = userContextHelperService;
}
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateOrganizationRocCommandValidator))]
    public override async Task<CreateOrganizationRocCommand> HandleAsync(CreateOrganizationRocCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
 
        // Get Organization ID from logged in user
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(command.Context.User);

        // Create
        Roc roc = new Roc()
        {
            RocName = command.RocName,
            Address = command.Address,
            Location = command.Location,
            PostalCode = command.PostalCode,
            Country = command.Country,
            OrganizationId = organizationId
        };

        // Save 
        await RocSrpRepository.AddAsync(roc, command);

        // Set Result
        command.Result.EntityId = roc.EntityId;

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}