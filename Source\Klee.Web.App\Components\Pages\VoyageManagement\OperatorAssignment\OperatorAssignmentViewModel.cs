using System;
using System.Collections.Generic;
using System.Linq;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;

namespace Klee.Web.App.Components.Pages.VoyageManagement.OperatorAssignment;

public enum OperatorAssignmentViewState
{
    SelectingOperator,
    ConfirmingAssignment
}

public class OperatorAssignmentViewModel
{
    #region PROPERTIES
    // View state management
    public OperatorAssignmentViewState CurrentViewState { get; set; } = OperatorAssignmentViewState.SelectingOperator;
    
    // Voyage data
    public Guid VoyageId { get; set; }
    public OpenVoyageListItem VoyageData { get; set; } = null;
    
    // Selected operator data (after operator selection)
    public string SelectedOperatorId { get; set; } = "";
    public AvailableCaptainListItem SelectedOperator { get; set; } = null;
    #endregion

    #region METHODS - STATE MANAGEMENT
    public void SetVoyageData(Guid voyageId, OpenVoyageListItem voyageData)
    {
        VoyageId = voyageId;
        VoyageData = voyageData;
    }

    public void NavigateToSelectOperator()
    {
        CurrentViewState = OperatorAssignmentViewState.SelectingOperator;
    }

    public void NavigateToConfirmAssignment()
    {
        CurrentViewState = OperatorAssignmentViewState.ConfirmingAssignment;
    }

    public void SetSelectedOperator(AvailableCaptainListItem operatorData)
    {
        SelectedOperator = operatorData;
        SelectedOperatorId = operatorData.OperatorId;
    }

    public bool HasVoyageData()
    {
        return VoyageData != null && VoyageId != Guid.Empty;
    }

    public int GetCurrentStep()
    {
        return CurrentViewState switch
        {
            OperatorAssignmentViewState.SelectingOperator => 0,
            OperatorAssignmentViewState.ConfirmingAssignment => 1,
            _ => 0
        };
    }

    // Helper properties for components
    public DateTime VoyageStartDateTime => VoyageData?.StartDateTime ?? DateTime.Now;
    public DateTime VoyageEndDateTime => VoyageData?.EndDateTime ?? DateTime.Now.AddHours(1);
    public IEnumerable<QualificationTypeIds> RequiredQualifications => VoyageData?.RequiredQualifications ?? new List<QualificationTypeIds>();
    public string VesselId => VoyageData?.VehicleId ?? "";
    #endregion
}
