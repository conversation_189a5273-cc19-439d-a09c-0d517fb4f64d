﻿@using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data
@inherits OrganizationDetailsViewVehiclesComponentBase

<section class="card">
    <header class="card-header">
        <h2 class="card-title">Overview</h2>
        <div class="card-actions">
            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
            <RCardActionToggle />
        </div>
    </header>
    <div class="card-body">
        <DxToolbar>
            <Items>
                <DxToolbarItem>
                    <Template>
                        <div class="control-value pe-2" style="width: 100px">
                            <DxComboBox Data="@VehicleListViewModel.SelectableVehiclesMainFilterTypeIds"
                                        ListRenderMode="@ListRenderMode.Entire"
                                        FilteringMode="@DataGridFilteringMode.None"
                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                        Value="@ViewModel.VehiclesMainFilterTypeId"
                                        ValueChanged="@((VehiclesMainFilterTypeIds vehiclesMainFilterTypeId) => OnValueChanged_VehiclesMainFilterTypeId(vehiclesMainFilterTypeId))"
                                        SizeMode="SizeMode.Small" />
                        </div>
                    </Template>
                </DxToolbarItem>
            </Items>
        </DxToolbar>

        <DxGrid Data="@this.ViewModel.Vehicles"
                @ref="@DxGrid"
                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                SelectionMode="GridSelectionMode.Multiple"
                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                PageSize="15"
                PageSizeSelectorVisible="true"
                PageSizeSelectorAllRowsItemVisible="true"
                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                PagerNavigationMode="PagerNavigationMode.InputBox"
                PageIndex="@ListViewProperties.DxGridPageIndex"
                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                SizeMode="SizeMode.Small">
            <Columns>
                <DxGridSelectionColumn Width="40px"
                                       VisibleIndex="1"
                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                           OnClickDelete="OnDxGrid_ClickDelete"
                                           CanShowButtonDelete=true
                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                           VisibleIndex="2" />
                <DxGridDataColumn FieldName="@nameof(VehicleListItem.VehicleId)" Caption="Vehicle Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="3" />
                <DxGridDataColumn FieldName="@nameof(VehicleListItem.VehicleName)" Caption="Vehicle Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4" />
                <DxGridDataColumn FieldName="@nameof(VehicleListItem.OrganizationId)" Caption="Organization Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5" />
                @if (this.ViewModel.VehiclesMainFilterTypeId != VehiclesMainFilterTypeIds.Active)
                {
                    <DxGridDataColumn FieldName="@nameof(VehicleListItem.IsActive)"
                                      Width="60px" VisibleIndex="6">
                        <CellDisplayTemplate>
                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                }
                <RDxGridEmptyDataColumn />
            </Columns>
        </DxGrid>

    </div>
</section>

@code {
}