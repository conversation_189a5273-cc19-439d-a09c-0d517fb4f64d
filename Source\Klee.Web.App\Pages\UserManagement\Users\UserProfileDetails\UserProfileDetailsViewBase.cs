﻿using Klee.Web.App.Pages.UserManagement.Users.UserProfileDetails.Data;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileUpdateGeneral;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileDetails;

    public partial class UserProfileDetailsViewBase : LayoutBodyDetailsViewBase<UserProfileDetailsViewModel, UserSessionData>
    {
        #region PROPERTIES
        [Parameter]
        public string UserProfileIdEncoded { get; set; }
        public string UserProfileId => Base64UrlEncoder.Decode(this.UserProfileIdEncoded);

        protected string PageHeaderTitle { get; private set; } = "UserProfile :                                      ";

        // Dialog Views
        protected UserProfileUpdateGeneralDialogView UserProfileUpdateGeneralDialogView { get; set; }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new UserProfileDetailsViewModel(this);

            //
            await base.OnInitializedAsync();

            // Set PageHeaderTitle
            this.PageHeaderTitle = this.GetPageHeaderTitle();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();

                // Load UserProfile
                if (await this.ViewModel.LoadUserProfileAsync(this.UserProfileId))
                {
                    // Set PageHeaderTitle
                    this.PageHeaderTitle = this.GetPageHeaderTitle();
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override Task OnAfterRenderFirstAsync()
        {
            // Set EventHandlers
            return base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
        {
            await base.OnMainLayoutBodyActiveTabChangedAsync(e);

            // Handle Tab Change
            switch (e.ActiveTabId)
            {
                default:
                    break;
            }
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS - SUPPORT
        private string GetPageHeaderTitle()
        {
            return $"UserProfile : {this.UserProfileId} ({this.ViewModel.DisplayName})";
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickDelete()
        {
            // Init
            string userProfileId = this.ViewModel?.UserProfileId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete User Profile?",
                                                                        $"Delete user profile of user '{userProfileId}'?") == RDialogResult.Ok)
                {
                    // Delete UserProfile
                    await this.ViewModel!.DeleteUserProfileAsync(userProfileId);

                    // Close View
                    await this.CloseViewAsync();

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"User profile of user '{userProfileId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting UserProfile with id '{UserProfileId}'", userProfileId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClick_ShowSysEntityInfoDetailsView()
        {
            // Init
            string UserProfileId = this.ViewModel?.UserProfileId ?? "";

            try
            {
                // Show EntityInfoDetails
                //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.UserProfile);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to UserProfile entity view of UserProfile '{UserProfileId}'", UserProfileId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenUserProfileUpdateGeneralDialogView()
        {
            try
            {
                await this.UserProfileUpdateGeneralDialogView.LoadUserProfileAsync(this.UserProfileId);
                await this.UserProfileUpdateGeneralDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.UserProfileUpdateGeneralDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - UserProfile UPDATE GENERAL DIALOG VIEW
        protected void OnUserProfileUpdateGeneralDialogView_Opened()
        {
        }

        protected async Task OnUserProfileUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as UserProfileUpdateGeneralDialogViewModel;
                    
                    // Load Update
                    await this.ViewModel.LoadUserProfileAsync(this.UserProfileId, forceLoad:true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        #endregion
    }