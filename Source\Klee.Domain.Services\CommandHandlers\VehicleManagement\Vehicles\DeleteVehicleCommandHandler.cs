using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles
{
    public sealed class DeleteVehicleCommandHandler
        : RequestHandlerAsync<DeleteVehicleCommand>
    {
        #region PROPERTIES
        private IVehicleSrpRepository VehicleSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public DeleteVehicleCommandHandler(IVehicleSrpRepository vehicleSrpRepository,
                                            IMemoryCache memoryCache)
        {
            VehicleSrpRepository = vehicleSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<DeleteVehicleCommand> HandleAsync(DeleteVehicleCommand command,
                                                                      CancellationToken cancellationToken = new CancellationToken())
        {
            // Find Vehicle
            Vehicle vehicle = await VehicleSrpRepository.FindAsync(_ => _.VehicleId == command.VehicleId &&
                                                                        _.EntityPartitionKey == command.VehicleId, command);

            if (vehicle != null)
            {
                // Set IsActive to false (soft delete)
                vehicle.IsActive = false;

                // Update
                await VehicleSrpRepository.UpdateAsync(vehicle, command);

                // Clear Caches
                MemoryCache.RemoveVehicle(command.VehicleId);
            }
            else
            {
                throw new EntityNotFoundException(
                    $"Vehicle with vehicle id '{command.VehicleId}' not found.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
} 