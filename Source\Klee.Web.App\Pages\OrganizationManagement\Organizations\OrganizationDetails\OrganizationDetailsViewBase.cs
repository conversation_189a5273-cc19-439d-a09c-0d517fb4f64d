using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorCreate;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Data;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Operators;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Rocs;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Vehicles;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateContact;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateGeneral;
using Klee.Web.App.Pages.RocManagement.Rocs.RocCreate;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleCreate;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails
{
    public partial class OrganizationDetailsViewBase : LayoutBodyDetailsViewBase<OrganizationDetailsViewModel, UserSessionData>
    {
        #region PROPERTIES
        [Parameter]
        public string OrganizationIdEncoded { get; set; }
        public string OrganizationId => Base64UrlEncoder.Decode(this.OrganizationIdEncoded);

        protected string PageHeaderTitle { get; private set; } = "Organization :                                      ";

        // Tab Content Items
        protected RTabContentItem TabContentItemGeneral { get; set; }
        protected RTabContentItem TabContentItemVehicles { get; set; }
        protected RTabContentItem TabContentItemRocs { get; set; }
        protected RTabContentItem TabContentItemOperators { get; set; }

        //Component refs
        public OrganizationDetailsViewVehiclesComponent OrganizationDetailsViewVehiclesComponentRef { get; set; }
        public OrganizationDetailsViewRocsComponent OrganizationDetailsViewRocsComponentRef { get; set; }
        public OrganizationDetailsViewOperatorsComponent OrganizationDetailsViewOperatorsComponentRef { get; set; }
        
        // Dialog Views
        protected OrganizationUpdateGeneralDialogView OrganizationUpdateGeneralDialogView { get; set; }
        protected OrganizationUpdateContactDialogView OrganizationUpdateContactDialogView { get; set; }
        protected VehicleCreateDialogView VehicleCreateDialogView { get; set; }
        protected RocCreateDialogView RocCreateDialogView { get; set; }
        protected OperatorCreateDialogView OperatorCreateDialogView { get; set; }
        
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new OrganizationDetailsViewModel(this);

            //
            await base.OnInitializedAsync();

            // Set PageHeaderTitle
            this.PageHeaderTitle = this.GetPageHeaderTitle();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();

                // Load Organization
                if (await this.ViewModel.LoadOrganizationAsync(this.OrganizationId))
                {
                    // Set PageHeaderTitle
                    this.PageHeaderTitle = this.GetPageHeaderTitle();
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override Task OnAfterRenderFirstAsync()
        {
            return base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
        {
            await base.OnMainLayoutBodyActiveTabChangedAsync(e);

            // Handle Tab Change
            switch (e.ActiveTabId)
            {
                case OrganizationDetailsViewTabIds.General:
                    break;
                case OrganizationDetailsViewTabIds.Vehicles:
                    break;
                case OrganizationDetailsViewTabIds.Rocs:
                    break;
                case OrganizationDetailsViewTabIds.Operators:
                    break;
            }
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS - SUPPORT
        private string GetPageHeaderTitle()
        {
            return $"Organization : {this.OrganizationId} ({this.ViewModel.OrganizationName})";
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickDelete()
        {
            // Init
            string organizationId = this.ViewModel?.OrganizationId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Organization?",
                                                                        $"Delete organization with id '{organizationId}'?") == RDialogResult.Ok)
                {
                    // Delete Organization
                    await this.ViewModel!.DeleteOrganizationAsync(organizationId);

                    // Close View
                    await this.CloseViewAsync();

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Organization with id '{organizationId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting organization with id '{organizationId}'", organizationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClick_ShowSysEntityInfoDetailsView()
        {
            // Init
            string organizationId = this.ViewModel?.OrganizationId ?? "";

            try
            {
                // Show EntityInfoDetails
                //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.Organization);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to organization entity view of organization '{organizationId}'", organizationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenOrganizationUpdateGeneralDialogView()
        {
            try
            {
                await this.OrganizationUpdateGeneralDialogView.LoadOrganizationAsync(this.OrganizationId);
                await this.OrganizationUpdateGeneralDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.OrganizationUpdateGeneralDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenOrganizationUpdateContactDialogView()
        {
            try
            {
                await this.OrganizationUpdateContactDialogView.LoadOrganizationAsync(this.OrganizationId);
                await this.OrganizationUpdateContactDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.OrganizationUpdateContactDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickAddRoc_OpenRocCreateDialogView() {
            try
            {
                await this.RocCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.RocCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickAddVehicle_OpenVehicleCreateDialogView() {
            try
            {
                await this.VehicleCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.RocCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickAddOperator_OpenOperatorCreateDialogView() {
            try
            {
                await this.OperatorCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.RocCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - ORGANIZATION UPDATE GENERAL DIALOG VIEW
        protected void OnOrganizationUpdateGeneralDialogView_Opened()
        {
        }

        protected async Task OnOrganizationUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as OrganizationUpdateGeneralDialogViewModel;

                    // Load Update
                    await this.ViewModel.LoadOrganizationAsync(this.OrganizationId, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS - ORGANIZATION UPDATE CONTACT DIALOG VIEW
        protected void OnOrganizationUpdateContactDialogView_Opened()
        {
        }

        protected async Task OnOrganizationUpdateContactDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as OrganizationUpdateContactDialogViewModel;

                    // Load Update
                    await this.ViewModel.LoadOrganizationAsync(this.OrganizationId, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS - ROC CREATE DIALOG VIEW
        protected void OnRocCreateDialogView_Opened()
        {
            //Automatically set organization ID based on current organization in create dialog view
            RocCreateDialogView.ViewModel.SelectedOrganizationId = this.OrganizationId;
        }

        protected async Task OnRocCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created station
                    //this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);

                    //Reload rocs to show newly created roc
                    await this.OrganizationDetailsViewRocsComponentRef.ViewModel.LoadOrganizationRocsAsync(
                        this.OrganizationId, forceLoad: true);

                    //Clear dialog view model data 
                    this.RocCreateDialogView.ViewModel.Clear();
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS - ROC CREATE DIALOG VIEW
        protected void OnVehicleCreateDialogView_Opened()
        {
            //Automatically set organization ID based on current organization in create dialog view
            VehicleCreateDialogView.ViewModel.SelectedOrganizationId = this.OrganizationId;
        }

        protected async Task OnVehicleCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created station
                    //this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                    await this.OrganizationDetailsViewVehiclesComponentRef.ViewModel.LoadOrganizationVehiclesAsync(
                        this.OrganizationId, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion


        #region EVENT HANDLERS - OPERATOR CREATE DIALOG VIEW
        protected void OnOperatorCreateDialogView_Opened()
        {
            //Automatically set organization ID based on current organization in create dialog view
            OperatorCreateDialogView.ViewModel.SelectedOrganizationId = this.OrganizationId;
        }

        protected async Task OnOperatorCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created station
                    //this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);

                    //Reload Operators to show newly created Operator
                    await this.OrganizationDetailsViewOperatorsComponentRef.ViewModel.LoadOrganizationOperatorsAsync(
                        this.OrganizationId, forceLoad: true);

                    //Clear dialog view model data 
                    this.RocCreateDialogView.ViewModel.Clear();
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnTabNavItemGeneral_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        protected Task OnTabNavItemRealtime_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        #endregion
    }
}