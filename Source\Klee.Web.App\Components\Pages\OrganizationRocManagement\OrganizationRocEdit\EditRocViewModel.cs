using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Entities.RocManagement.Rocs.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Queries.Common;
using Renoir.Application.Validations.Helpers;
using Z.Expressions;

namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocEdit;

public class EditRocViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES - FORM FIELDS
    public string RocId { get; set; } = "";

    [Required]
    public string RocName { get; set; } = "";

    public string Address { get; set; } = "";

    public string Location { get; set; } = "";

    public string PostalCode { get; set; } = "";

    [Required]
    public CountryIds Country { get; set; } = CountryIds.None;
    #endregion

    #region PROPERTIES - STATIC
    // Selectable
    public static IList<string> SelectableCountryDisplayNames { get; } = Enums.GetMembers<CountryIds>()
                                                                              .Where(_ => _.Value != CountryIds.None)
                                                                              .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                              .OrderBy(_ => _).ToList();
    #endregion

    #region CONSTRUCTORS
    public EditRocViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task LoadRocAsync(string rocId)
    {
        // Get query context
        IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();

        // Load the operator
        Roc roc = await _srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationRocQuery(rocId, queryContext));

        // Map ROC data to view model
        RocId = roc.RocId;
        RocName = roc.RocName;
        Address = roc.Address;
        Location = roc.Location;
        PostalCode = roc.PostalCode;
        Country = roc.Country;
    }
    private async Task<UpdateOrganizationRocGeneralCommand> NewUpdateOrganizationRocGeneralCommandAsync()
    {
        ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
        
        return new UpdateOrganizationRocGeneralCommand(this.RocId, commandContext)
        {
            RocName = this.RocName,
            Address = this.Address,
            Location = this.Location,
            PostalCode = this.PostalCode,
            Country = this.Country
        };
    }

    public async Task UpdateRocAsync()
    {
        try
        {
            // Update general information
            UpdateOrganizationRocGeneralCommand updateGeneralCommand = await NewUpdateOrganizationRocGeneralCommandAsync();

            await _srpProcessors.CommandProcessor.SendAsync(updateGeneralCommand);
        }
        catch
        {
            throw;
        }
    }
    #endregion

    #region METHODS - VALIDATE
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        UpdateOrganizationRocGeneralCommandValidator commandValidator = new UpdateOrganizationRocGeneralCommandValidator();
        (await commandValidator.ValidateAsync(await this.NewUpdateOrganizationRocGeneralCommandAsync()))
            .AddTo(validationResults);

        return validationResults;
    }
    #endregion
}
