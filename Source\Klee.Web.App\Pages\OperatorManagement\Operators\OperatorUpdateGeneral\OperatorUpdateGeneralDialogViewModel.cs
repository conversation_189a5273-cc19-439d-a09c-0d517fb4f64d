﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateGeneral;

public class OperatorUpdateGeneralDialogViewModel
    : ViewModelBase<OperatorUpdateGeneralDialogViewModel>
{
    #region PROPERTIES
    [Required]
    [Display(Name = "Operator Id")]
    public string OperatorId { get; set; } = "";

    [Display(Name = "First Name")]
    public string OperatorFirstName { get; set; } = "";

    [Display(Name = "Last Name")]
    public string OperatorLastName { get; set; } = "";

    [Display(Name = "Email")]
    public string OperatorEmail { get; set; } = "";

    [Display(Name = "Hourly rate (Euro)")]
    public double HourlyRateInEuros { get; set; } = 0.0;

    public string OrganizationId { get; set; } = "";
    #endregion

    #region CONSTRUCTORS
    public OperatorUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS
    public void Clear()
    {
        this.OperatorId = "";
        this.OperatorFirstName = "";
        this.OperatorLastName = "";
        this.OperatorEmail = "";
        this.HourlyRateInEuros = 0.0;
        this.OrganizationId = "";
    }

    public async Task LoadOperatorAsync(string operatorId)
    {
        // Get Operator
        Operator operatorObj = await this.SrpQueryProcessor.ExecuteAsync(new GetOperatorQuery(operatorId, this.SrpQueryContext)) ?? new Operator();

        // Set Operator
        this.OperatorId = operatorObj.OperatorId;
        this.OperatorFirstName = operatorObj.FirstName;
        this.OperatorLastName = operatorObj.LastName;
        this.OperatorEmail = operatorObj.OperatorEmail;
        this.HourlyRateInEuros = operatorObj.HourlyRateInEuros;
        this.OrganizationId = operatorObj.OrganizationId;
    }
    #endregion

    #region METHODS
    public async Task UpdateOperatorAsync()
    {
        // Init
        UpdateOperatorGeneralCommand updateOperatorGeneralCommand = new UpdateOperatorGeneralCommand(this.OperatorId,
                                                                                                 this.OrganizationId,
                                                                                                  this.SrpCommandContext)
                                                                    {
                                                                        OperatorFirstName = this.OperatorFirstName,
                                                                        OperatorLastName = this.OperatorLastName,
                                                                        OperatorEmail = this.OperatorEmail,
                                                                        HourlyRateInEuros = this.HourlyRateInEuros
                                                                    };

        // Create Operator
        await this.SrpCommandProcessor
                  .SendAsync(updateOperatorGeneralCommand, false);
    }
    #endregion
}