@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Messages.Queries.VoyagePlanning.Data
@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data
@using Klee.Domain.Entities.OperatorManagement.Operators.Data
@using EnumsNET

@inherits SelectOperatorForVoyageViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-bold text-teal-700">Select an Operator</h2>
    <Button Type="@ButtonType.Default"
            Class="@TailwindStyleStrings.Button.Outline"
            OnClick="HandleOnClick_Back">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Find Voyage
    </Button>
</div>

<Card Class="@TailwindStyleStrings.Card.Container">
    <div class="p-6">
        @if (ViewModel?.IsLoadingCaptains == true)
        {
            <div class="text-center py-12">
                <Spin Size="SpinSize.Large" />
                <p class="mt-4 text-gray-600">Finding available operators...</p>
            </div>
        }
        else if (ViewModel?.AvailableCaptains?.Any() == true)
        {
            <!-- Available Operators Table -->
            <Table TItem="AvailableCaptainListItem"
                   DataSource="@ViewModel.AvailableCaptains"
                   Class="@TailwindStyleStrings.Table.Container"
                   PageSize="10">
                <ColumnDefinitions>
                    <PropertyColumn Property="c => c.FullName" Title="Name" Sortable/>
                    <PropertyColumn Property="c => c.OrganizationName" Title="Organization" Sortable/>
                    <PropertyColumn Property="c => c.ExperienceDisplay" Title="Experience" Sortable/>
                    <PropertyColumn Property="c => c.HourlyRateInEuros" Title="Hourly Rate" Sortable>
                        <Template>
                            € @context.HourlyRateInEuros.ToString("N2")
                        </Template>
                    </PropertyColumn>
                    <ActionColumn Title="Actions" Fixed="ColumnFixPlacement.Right">
                        <Space>
                            <SpaceItem>
                                <Button Type="@ButtonType.Primary"
                                        Class="@TailwindStyleStrings.Button.Primary"
                                        Size="@ButtonSize.Small"
                                        OnClick="() => HandleSelectOperator(context)">
                                    Select Operator
                                </Button>
                            </SpaceItem>
                        </Space>
                    </ActionColumn>
                </ColumnDefinitions>
                <ExpandTemplate Context="captain">
                    <div class="px-8 py-4 bg-gray-50">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Biography -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Biography</h4>
                                <div class="p-4">
                                    @if (!string.IsNullOrWhiteSpace(captain.Data.Biography))
                                    {
                                        <p class="text-sm text-gray-700 leading-relaxed">@captain.Data.Biography</p>
                                    }
                                    else
                                    {
                                        <p class="text-sm text-gray-500 italic">No biography available</p>
                                    }
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Qualifications</h4>
                                <div class="p-4">
                                    @if (captain.Data.Qualifications.Any())
                                    {
                                        <div class="flex flex-wrap gap-2">
                                            @foreach (var qualification in captain.Data.Qualifications)
                                            {
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-teal-100 text-teal-800">
                                                    @qualification.AsString(EnumFormat.DisplayName)
                                                </span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <p class="text-sm text-gray-500 italic">No qualifications listed</p>
                                    }
                                </div>
                            </div>

                            <!-- Working Schedule -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-teal-700 border-b border-teal-200 pb-2">Working Schedule</h4>
                                <div class="p-4 space-y-4">
                                    <!-- Working Days -->
                                    <div>
                                        <p class="text-sm font-medium text-teal-700 mb-2">Working Days</p>
                                        <div class="grid grid-cols-2 gap-2">
                                            @{
                                                var workingDays = captain.Data.WorkingDays;
                                                var dayFlags = new[]
                                                {
                                                    (WeekDaysIds.Monday, "Mon"),
                                                    (WeekDaysIds.Tuesday, "Tue"),
                                                    (WeekDaysIds.Wednesday, "Wed"),
                                                    (WeekDaysIds.Thursday, "Thu"),
                                                    (WeekDaysIds.Friday, "Fri"),
                                                    (WeekDaysIds.Saturday, "Sat"),
                                                    (WeekDaysIds.Sunday, "Sun")
                                                };
                                            }
                                            @foreach (var (flag, label) in dayFlags)
                                            {
                                                <div class="flex items-center gap-2">
                                                    <div class="@($"w-3 h-3 rounded-full {(workingDays.HasFlag(flag) ? "bg-teal-500" : "bg-gray-300")}")"></div>
                                                    <span class="text-sm text-gray-700">@label</span>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    <!-- Working Hours -->
                                    <div>
                                        <p class="text-sm font-medium text-teal-700 mb-1">Working Hours</p>
                                        <p class="text-sm text-gray-700">
                                            @captain.Data.RegularStartTime.ToString("HH:mm") - @captain.Data.RegularEndTime.ToString("HH:mm")
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ExpandTemplate>
            </Table>
        }
        else
        {
            <div class="text-center py-12">
                <i class="fas fa-user-slash text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Operators</h3>
                <p class="text-gray-500">No operators are available for the selected voyage time and requirements.</p>
            </div>
        }
    </div>
</Card>
