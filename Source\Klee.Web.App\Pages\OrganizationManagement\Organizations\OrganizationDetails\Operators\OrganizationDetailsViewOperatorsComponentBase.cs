﻿using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList.Data;
using Microsoft.AspNetCore.Components;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Operators;

    public partial class OrganizationDetailsViewOperatorsComponentBase 
        : LayoutBodyListDxGridViewBase<OrganizationDetailsViewOperatorComponentModel, OperatorListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        [Parameter]
        public string OrganizationId { get; set; } = "";

        #endregion

        #region CONSTRUCTORS
        protected OrganizationDetailsViewOperatorsComponentBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new OrganizationDetailsViewOperatorComponentModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Operators
                if (await this.ViewModel.LoadOrganizationOperatorsAsync(this.OrganizationId))
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            OperatorListItem operatorListItem = e.GetDataItem() as OperatorListItem ?? new OperatorListItem();
            string operatorId = operatorListItem.OperatorId ?? "";

            try
            {
                // View Operator
                await this.NavigateToAsync(OperatorDetailsView.GetUri(operatorId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of Operator '{OperatorId}'", operatorId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            OperatorListItem operatorListItem = e.GetDataItem() as OperatorListItem ?? new OperatorListItem();
            string operatorId = operatorListItem.OperatorId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete operator?",
                                                                        $"Delete operator with id '{operatorId}'?") == RDialogResult.Ok)
                {
                    // Delete Operator
                    await this.ViewModel.DeleteOperatorAsync(operatorId, this.OrganizationId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Operator with id '{operatorId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting operator with id '{operatorId}'", operatorId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllOperators(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.OperatorsMainFilterTypeId = OperatorsMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of OperatorsMainFilterType?
        //    await this.ViewModel.LoadOperatorsAsync();
        //}

        public async void OnValueChanged_OperatorsMainFilterTypeId(OperatorsMainFilterTypeIds operatorsMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.OperatorsMainFilterTypeId = operatorsMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of OperatorsMainFilterType?
                await this.ViewModel.LoadOrganizationOperatorsAsync(this.OrganizationId);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(OperatorListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }