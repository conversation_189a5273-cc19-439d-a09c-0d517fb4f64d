﻿using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateContact
{
    public class OrganizationUpdateContactDialogViewModel
        : ViewModelBase<OrganizationUpdateContactDialogViewModel>
    {
        #region PROPERTIES

        [Required]
        [Display(Name = "Organization Id")]
        public string OrganizationId { get; set; } = "";

        [Display(Name = "Contact Address")]
        public string Address { get; set; } = "";

        [Display(Name = "Contact Email")]
        public string Email { get; set; } = "";

        [Display(Name = "Contact Phone")]
        public string Phone { get; set; } = "";


        #endregion

        #region CONSTRUCTORS
        public OrganizationUpdateContactDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.OrganizationId = "";
            this.Address = "";
            this.Email = "";
            this.Phone = "";
        }

        public async Task LoadOrganizationAsync(string organizationId)
        {
            // Get Organization
            var organization = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationQuery(organizationId, this.SrpQueryContext)) ?? new Organization();

            // Set Organization
            this.OrganizationId = organizationId;
            this.Address = organization.Address;
            this.Email = organization.ContactEmail;
            this.Phone = organization.ContactPhone;
        }

        #endregion

        #region METHODS

        public async Task UpdateOrganizationAsync()
        {
            UpdateOrganizationContactCommand updateOrganizationContactCommand = new UpdateOrganizationContactCommand(this.OrganizationId,
                                            this.SrpCommandContext)
                   {
                       Address = this.Address,
                       Email = this.Email,
                       Phone = this.Phone,
                   };
            await this.SrpCommandProcessor.SendAsync(updateOrganizationContactCommand);
        }
        #endregion
    }
} 