using System;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions.VoyageManagement;
using Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;

namespace Klee.Web.App.Components.Pages.VoyageManagement.OperatorAssignment;

public class AssignmentConfirmationViewModel
{
    #region PROPERTIES
    private readonly ISrpProcessors _srpProcessors;
    
    // Voyage and operator data
    public OpenVoyageListItem VoyageData { get; set; } = null;
    public AvailableCaptainListItem SelectedOperator { get; set; } = null;
    
    // Cost calculations
    public double VoyageDurationHours => VoyageData != null 
        ? (VoyageData.EndDateTime - VoyageData.StartDateTime).TotalHours 
        : 0;
    public double OperatorCost => VoyageDurationHours * (SelectedOperator?.HourlyRateInEuros ?? 0);
    public double CommissionRate => 0.05; // 5%
    public double CommissionAmount => OperatorCost * CommissionRate;
    public double TotalCost => OperatorCost + CommissionAmount;
    
    // UI state
    public bool IsAssigningOperator { get; set; } = false;

    // Concurrency handling
    public bool HasConcurrencyConflict { get; set; } = false;
    public string ConcurrencyErrorMessage { get; set; } = "";
    #endregion

    #region CONSTRUCTORS
    public AssignmentConfirmationViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA MANAGEMENT
    public void SetAssignmentData(OpenVoyageListItem voyageData, AvailableCaptainListItem selectedOperator)
    {
        VoyageData = voyageData;
        SelectedOperator = selectedOperator;
    }

    public void ClearConcurrencyConflict()
    {
        HasConcurrencyConflict = false;
        ConcurrencyErrorMessage = "";
    }
    #endregion

    #region METHODS - ASSIGNMENT PROCESSING
    public async Task<bool> AssignOperatorAsync(string bookingOrganizationId)
    {
        if (VoyageData == null || SelectedOperator == null)
            return false;

        IsAssigningOperator = true;
        HasConcurrencyConflict = false;
        ConcurrencyErrorMessage = "";

        try
        {
            // Assign operator to voyage command
            var assignOperatorCommand = new AssignOperatorToVoyageCommand(VoyageData.VoyageId, await _srpProcessors.GetCommandContextAsync())
            {
                OperatorId = SelectedOperator.OperatorId
            };

            // Execute operator assignment
            await _srpProcessors.CommandProcessor.SendAsync(assignOperatorCommand);

            // Create invoice command
            var createInvoiceCommand = new CreateVoyageInvoiceCommand(bookingOrganizationId, await _srpProcessors.GetCommandContextAsync())
            {
                OperatorOrganizationId = SelectedOperator.OrganizationId,
                VoyageId = VoyageData.VoyageId,
                TotalAmountInEuros = TotalCost
            };

            // Execute invoice creation
            await _srpProcessors.CommandProcessor.SendAsync(createInvoiceCommand);

            return true;
        }
        catch (VoyageConcurrencyException ex)
        {
            HasConcurrencyConflict = true;
            ConcurrencyErrorMessage = ex.Message;
            return false;
        }
        catch
        {
            return false;
        }
        finally
        {
            IsAssigningOperator = false;
        }
    }
    #endregion
}
