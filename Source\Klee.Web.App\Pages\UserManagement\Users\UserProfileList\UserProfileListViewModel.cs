﻿using EnumsNET;
using Klee.Domain.Messages.Queries.UserManagement.Users;
using Klee.Domain.Messages.Queries.UserManagement.Users.Data;
using Klee.Web.App.Pages.UserManagement.Users.UserProfileList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileList;

    public class UserProfileListViewModel
        : ViewModelBase<UserProfileListViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - STATIC
        // Selectable
        public static IList<UserProfilesMainFilterTypeIds> SelectableUserProfilesMainFilterTypeIds { get; }
            = Enums.GetMembers<UserProfilesMainFilterTypeIds>()
                   .Where(_ => _.Value != UserProfilesMainFilterTypeIds.None)
                   .Select(_ => _.Value).ToList();
        #endregion

        #region FIELDS
        private UserProfilesMainFilterTypeIds _UserProfilesMainFilterTypeId = UserProfilesMainFilterTypeIds.None;
        #endregion

        #region PROPERTIES
        public UserProfilesMainFilterTypeIds UserProfilesMainFilterTypeId
        {
            get => this._UserProfilesMainFilterTypeId;
            set => this.RaiseAndSetIfChanged(ref this._UserProfilesMainFilterTypeId, value);
        }

        // Actual Loaded
        public UserProfilesMainFilterTypeIds LoadedUserProfilesMainFilterTypeId { get; private set; }

        //
        public IReadOnlyList<UserProfileListItem> UserProfiles { get; private set; } = new List<UserProfileListItem>();
        #endregion

        #region CONSTRUCTORS
        public UserProfileListViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDES
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
            this.UserProfilesMainFilterTypeId = userSessionData.UserProfilesMainFilterTypeId;
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                       UserProfilesMainFilterTypeId = this.LoadedUserProfilesMainFilterTypeId
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadUserProfilesAsync(bool forceLoad = false)
        {
            // Adjust Properties
            this.UserProfilesMainFilterTypeId = this.UserProfilesMainFilterTypeId != UserProfilesMainFilterTypeIds.None ? this.UserProfilesMainFilterTypeId : UserProfilesMainFilterTypeIds.All;

            // Load UserProfiles
            if (this.LoadedUserProfilesMainFilterTypeId != this.UserProfilesMainFilterTypeId ||
                forceLoad)
            {
                // Load UserProfiles
                switch (this.UserProfilesMainFilterTypeId)
                {
                    case UserProfilesMainFilterTypeIds.Active:
                        // Load UserProfiles
                        this.UserProfiles = await this.SrpQueryProcessor.ExecuteAsync(new GetUserProfileListQuery(this.SrpQueryContext)
                                                                               {
                                                                                   IsActive = true
                                                                               });
                        break;
                    case UserProfilesMainFilterTypeIds.All:
                    default:
                        // Load UserProfiles
                        this.UserProfiles = await this.SrpQueryProcessor.ExecuteAsync(new GetUserProfileListQuery(this.SrpQueryContext));
                        break;
                }

                // Set
                this.LoadedUserProfilesMainFilterTypeId = this.UserProfilesMainFilterTypeId;

                // Notify
                await this.InvokeStateHasChangedOnHostAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteUserProfileAsync(string userProfileId)
        {
            try
            {
                // Delete UserProfile
                //await this.SrpCommandProcessor.SendAsync(new DeleteUserProfileCommand(UserProfileId,
                //                                                               this.SrpCommandContext));

                // Load UserProfiles
                await this.LoadUserProfilesAsync(forceLoad: true);
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }