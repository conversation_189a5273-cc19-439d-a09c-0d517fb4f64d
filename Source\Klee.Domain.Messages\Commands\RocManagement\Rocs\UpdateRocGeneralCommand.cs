﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.RocManagement.Rocs;

public class UpdateRocGeneralCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string RocId { get; }

    public string RocName { get; set; } = "";
    public string Address { get; set; } = "";
    public string OrganizationId { get; }
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateRocGeneralCommand(string rocId,
        string organizationId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.RocId = rocId;
        this.OrganizationId = organizationId;
    }
    #endregion
}