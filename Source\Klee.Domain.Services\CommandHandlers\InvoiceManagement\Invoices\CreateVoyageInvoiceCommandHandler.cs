using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.InvoiceManagement.Invoices
{
    public sealed class CreateVoyageInvoiceCommandHandler
        : RequestHandlerAsync<CreateVoyageInvoiceCommand>
    {
        #region PROPERTIES
        private IVoyageInvoiceSrpRepository VoyageInvoiceSrpRepository { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateVoyageInvoiceCommandHandler(IVoyageInvoiceSrpRepository voyageInvoiceSrpRepository)
        {
            VoyageInvoiceSrpRepository = voyageInvoiceSrpRepository;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<CreateVoyageInvoiceCommand> HandleAsync(CreateVoyageInvoiceCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
            // Create VoyageInvoice
            VoyageInvoice voyageInvoice = new VoyageInvoice()
            {
                BookingOrganizationId = command.BookingOrganizationId,
                OperatorOrganizationId = command.OperatorOrganizationId,
                VoyageId = command.VoyageId,
                TotalAmountInEuros = command.TotalAmountInEuros,
                Status = VoyageInvoiceStatus.Open,
                PaymentDate = null
            };

            // Save 
            await VoyageInvoiceSrpRepository.AddAsync(voyageInvoice, command);

            // Set Result
            command.Result.VoyageInvoiceId = voyageInvoice.VoyageInvoiceId;

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}
