﻿using FluentValidation;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;

public class UpdateOrganizationOperatorGeneralCommandValidator : AbstractValidator<UpdateOrganizationOperatorGeneralCommand> {
    public UpdateOrganizationOperatorGeneralCommandValidator() {
        this.RuleFor(_ => _.OperatorId).NotNull();
        this.RuleFor(_ => _.OperatorId).NotEmpty();
        this.RuleFor(_ => _.WeekDays).Must(this.WorkingDaysMustBeValid).WithMessage("At least one working day must be selected.");
    }

    private bool WorkingDaysMustBeValid(WeekDaysIds weekDays) {
        return weekDays != WeekDaysIds.None;
    }
}