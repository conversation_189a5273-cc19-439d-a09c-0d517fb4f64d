﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using EnumsNET;
using Klee.Domain.Messages.Queries.UserManagement.Users;
using Klee.Domain.Messages.Queries.UserManagement.Users.Data;
using Klee.Domain.Services.Repositories.UserManagement.Users;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.UserManagement.Users;

    public sealed class GetUserProfileListQueryHandler
        : QueryHandlerAsync<GetUserProfileListQuery, IReadOnlyList<UserProfileListItem>>
    {
        #region PROPERTIES
        private IUserProfileSrpRepository UserProfileSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetUserProfileListQueryHandler(IUserProfileSrpRepository userProfileSrpRepository,
                                          IMemoryCache memoryCache)
        {
            this.UserProfileSrpRepository = userProfileSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<UserProfileListItem>> ExecuteAsync(GetUserProfileListQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            // Get UserProfileListItems from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetUserProfileListCacheId, out List<UserProfileListItem> cachedUserProfileListItems);

            isCached = false;
            // Init
            List<UserProfileListItem> userProfileListItems = cachedUserProfileListItems ?? new List<UserProfileListItem>();

            // Get UserProfileListItems from DB (if needed)
            if (query.AllowCached == false ||
                !isCached) {
                
                // Get UserProfileListItems
                userProfileListItems =
                    (await this.UserProfileSrpRepository.Entities(query).ToListAsync(cancellationToken: cancellationToken))
                        .OrderBy(_ => _.UserId)
                        .Select(_ => new UserProfileListItem()
                                       {
                                           EntityId = _.EntityId,
                                           UserId = _.UserId,
                                           FirstName = _.FirstName,
                                           LastName = _.LastName,
                                           DisplayName = _.DisplayName,
                                           Email = _.Email,
                                           OrganizationId = _.OrganizationId,
                                           IsActive = _.IsActive ?? false
                                       })
                        .ToList();

                // Cache UserProfileListItems
                this.MemoryCache.Set(MemoryCacheIds.GetUserProfileListCacheId,
                                     userProfileListItems, new MemoryCacheEntryOptions()
                                                       {
                                                           AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                           Size = 1
                                                       });
            }

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                userProfileListItems = userProfileListItems.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return userProfileListItems;
        }
        #endregion
    }