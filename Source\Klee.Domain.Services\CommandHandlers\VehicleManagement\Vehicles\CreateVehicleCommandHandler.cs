﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles
{
    public sealed class CreateVehicleCommandHandler
        : RequestHandlerAsync<CreateVehicleCommand>
    {
        #region PROPERTIES
        private IVehicleSrpRepository VehicleSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateVehicleCommandHandler(IVehicleSrpRepository vehicleSrpRepository,
            IMemoryCache memoryCache)
        {
            VehicleSrpRepository = vehicleSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateVehicleCommandValidator))]
        public override async Task<CreateVehicleCommand> HandleAsync(CreateVehicleCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
            // Create
            Vehicle vehicle = new Vehicle()
            {
                VehicleTypeId = command.VehicleTypeId,
                VehicleName = command.VehicleName,
                ENI = command.ENI,
                HourlyRateInEuros = command.HourlyRateInEuros,
                SoftwareEnvironmentId = command.SoftwareEnvironmentId,
                OrganizationId = command.OrganizationId,
            };

            // Save 
            await VehicleSrpRepository.AddAsync(vehicle, command);

            // Set Result
            command.Result.EntityId = vehicle.EntityId;

            // Clear Caches
            MemoryCache.RemoveVehicle(vehicle.VehicleId);
            
            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }

        #endregion
    }
}