using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.InvoiceManagement
{
    public class VoyageInvoiceEntityTypeConfiguration : IEntityTypeConfiguration<VoyageInvoice>
    {
        public void Configure(EntityTypeBuilder<VoyageInvoice> builder)
        {
            builder.HasIndex(_ => _.VoyageInvoiceId)
                   .IsUnique();
            builder.Property(_ => _.VoyageInvoiceId)
                   .IsRequired();
            builder.Property(_ => _.BookingOrganizationId)
                   .IsRequired();
            builder.Property(_ => _.OperatorOrganizationId)
                   .IsRequired();
            builder.Property(_ => _.VoyageId)
                   .IsRequired();
            builder.Property(_ => _.Status)
                   .IsRequired();
            builder.Property(_ => _.PaymentDate)
                   .IsRequired(false);
            builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

            // Set up the relationship with booking organization
            builder
                .HasOne(vi => vi.BookingOrganization)
                .WithMany()
                .HasForeignKey(vi => vi.BookingOrganizationId)
                .HasPrincipalKey(o => o.OrganizationId);

            // Set up the relationship with operator organization
            builder
                .HasOne(vi => vi.OperatorOrganization)
                .WithMany()
                .HasForeignKey(vi => vi.OperatorOrganizationId)
                .HasPrincipalKey(o => o.OrganizationId);

            // Set up the relationship with voyage
            builder
                .HasOne(vi => vi.Voyage)
                .WithMany()
                .HasForeignKey(vi => vi.VoyageId)
                .HasPrincipalKey(v => v.VoyageId);
        }
    }
}
