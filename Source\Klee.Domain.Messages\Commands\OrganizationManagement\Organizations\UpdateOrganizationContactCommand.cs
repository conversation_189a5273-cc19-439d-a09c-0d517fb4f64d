﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations {
    public class UpdateOrganizationContactCommand : CommandBase
    {

        #region RESULT CLASS
        public class CommandResult
        {
            public long EntityId { get; set; }
        }
        #endregion

        #region PROPERTIES
        public string OrganizationId { get; }
        public string Address { get; set; } = "";
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";

        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public UpdateOrganizationContactCommand(string organizationId,
            ICommandContext commandContext)
            : base(commandContext) {
            this.OrganizationId = organizationId;
        }
        #endregion
    }
}