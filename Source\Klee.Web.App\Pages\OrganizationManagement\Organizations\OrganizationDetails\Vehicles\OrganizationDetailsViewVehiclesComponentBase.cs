﻿using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Vehicles;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList;
using Microsoft.AspNetCore.Components;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Vehicles;

    public partial class OrganizationDetailsViewVehiclesComponentBase 
        : LayoutBodyListDxGridViewBase<OrganizationDetailsViewVehicleComponentModel, VehicleListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        [Parameter]
        public string OrganizationId { get; set; } = "";

        #endregion

        #region CONSTRUCTORS
        protected OrganizationDetailsViewVehiclesComponentBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new OrganizationDetailsViewVehicleComponentModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Vehicles
                if (await this.ViewModel.LoadOrganizationVehiclesAsync(this.OrganizationId))
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            VehicleListItem vehicleListItem = e.GetDataItem() as VehicleListItem ?? new VehicleListItem();
            string vehicleId = vehicleListItem.VehicleId ?? "";

            try
            {
                // View Vehicle
                await this.NavigateToAsync(VehicleDetailsView.GetUri(vehicleId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of vehicle '{vehicleId}'", vehicleId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            VehicleListItem vehicleListItem = e.GetDataItem() as VehicleListItem ?? new VehicleListItem();
            string vehicleId = vehicleListItem.VehicleId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Vehicle?",
                                                                        $"Delete vehicle with id '{vehicleId}'?") == RDialogResult.Ok)
                {
                    // Delete Vehicle
                    await this.ViewModel.DeleteVehicleAsync(vehicleId, this.OrganizationId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"vehicle with id '{vehicleId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting vehicle with id '{vehicleId}'", vehicleId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllVehicles(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.VehiclesMainFilterTypeId = VehiclesMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of VehiclesMainFilterType?
        //    await this.ViewModel.LoadVehiclesAsync();
        //}

        public async void OnValueChanged_VehiclesMainFilterTypeId(VehiclesMainFilterTypeIds vehiclesMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.VehiclesMainFilterTypeId = vehiclesMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of VehiclesMainFilterType?
                await this.ViewModel.LoadOrganizationVehiclesAsync(this.OrganizationId);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(VehicleListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }