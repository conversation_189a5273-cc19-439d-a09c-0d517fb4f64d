﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;

public class GetOrganizationOperatorListQuery
    : QueryBase<IReadOnlyList<OrganizationOperatorListItem>> {
    #region PROPERTIES

    #endregion

    #region CONSTRUCTORS
    public GetOrganizationOperatorListQuery(IQueryContext context)
        : base(context) { }

    #endregion
}