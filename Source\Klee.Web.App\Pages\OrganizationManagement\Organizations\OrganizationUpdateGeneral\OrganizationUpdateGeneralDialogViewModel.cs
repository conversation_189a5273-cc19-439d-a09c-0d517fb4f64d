using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Renoir.SoftwareEnvironments;
using Renoir.Srp.Portal.Web.Pages.Common;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;
using Monet.Helpers;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateGeneral
{
    public class OrganizationUpdateGeneralDialogViewModel
        : ViewModelBase<OrganizationUpdateGeneralDialogViewModel>
    {
        #region PROPERTIES

        [Required]
        [Display(Name = "Organization Id")]
        public string OrganizationId { get; set; } = "";

        [Required]
        [Display(Name = "Name")]
        public string Name { get; set; } = "";

        [Display(Name = "Code")]
        public string Code { get; set; } = "";

        [Display(Name = "Description")]
        public string Description { get; set; } = "";

        #endregion

        #region CONSTRUCTORS
        public OrganizationUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.OrganizationId = "";
            this.Name = "";
            this.Code = "";
            this.Description = "";
        }

        public async Task LoadOrganizationAsync(string organizationId)
        {
            // Get Organization
            var organization = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationQuery(organizationId, this.SrpQueryContext)) ?? new Organization();

            // Set Organization
            this.OrganizationId = organization.OrganizationId;
            this.Name = organization.Name;
            this.Code = organization.Code;
            this.Description = organization.Description;
        }

        #endregion

        #region METHODS

        public async Task UpdateOrganizationAsync()
        {
            UpdateOrganizationGeneralCommand updateOrganizationGeneralCommand = new UpdateOrganizationGeneralCommand(this.OrganizationId, this.Name,
                                            this.SrpCommandContext)
                   {
                       Code = this.Code,
                       Description = this.Description,
                   };
            await this.SrpCommandProcessor.SendAsync(updateOrganizationGeneralCommand);
        }
        #endregion
    }
} 