﻿using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Web.App.Pages.RocManagement.Rocs.RocCreate;
using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails;
using Klee.Web.App.Pages.RocManagement.Rocs.RocList.Data;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocList;

    public partial class RocListViewBase 
        : LayoutBodyListDxGridViewBase<RocListViewModel, RocListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        // Dialog Views
        protected RocCreateDialogView RocCreateDialogView { get; set; }
        #endregion

        #region CONSTRUCTORS
        protected RocListViewBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new RocListViewModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Rocs
                if (await this.ViewModel.LoadRocsAsync())
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            RocListItem rocListItem = e.GetDataItem() as RocListItem ?? new RocListItem();
            string rocId = rocListItem.RocId ?? "";

            try
            {
                // View ROC
                await this.NavigateToAsync(RocDetailsView.GetUri(rocId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of ROC '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            RocListItem rocListItem = e.GetDataItem() as RocListItem ?? new RocListItem();
            string rocId = rocListItem.RocId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete ROC?",
                                                                        $"Delete ROC with id '{rocId}'?") == RDialogResult.Ok)
                {
                    // Delete Vehicle
                    await this.ViewModel.DeleteRocAsync(rocId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"ROC with id '{rocId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting ROC with id '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickNew()
        {
            try
            {
                await this.RocCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                //this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.VehicleCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - ROC CREATE DIALOG VIEW
        protected void OnRocCreateDialogView_Opened()
        {
        }

        protected async Task OnRocCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created vehicle
                    this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllVehicles(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.VehiclesMainFilterTypeId = VehiclesMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of VehiclesMainFilterType?
        //    await this.ViewModel.LoadVehiclesAsync();
        //}

        public async void OnValueChanged_RocsMainFilterTypeId(RocsMainFilterTypeIds rocsMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.RocsMainFilterTypeId = rocsMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of VehiclesMainFilterType?
                await this.ViewModel.LoadRocsAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(RocListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }