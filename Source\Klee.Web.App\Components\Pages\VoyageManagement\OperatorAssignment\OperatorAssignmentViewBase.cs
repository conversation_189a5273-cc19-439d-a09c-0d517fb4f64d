using System;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.VoyageManagement.FindVoyage;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Klee.Web.App.Components.Pages.VoyageManagement.OperatorAssignment;

public class OperatorAssignmentViewBase : ComponentBase
{
    #region DI
    [Inject]
    protected NavigationManager NavigationManager { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<OperatorAssignmentViewBase> Logger { get; set; }

    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public string VoyageId { get; set; } = "";
    #endregion

    #region PROPERTIES
    protected OperatorAssignmentViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    #endregion

    #region COMPONENT REFERENCES
    protected SelectOperatorForVoyageComponent SelectOperatorComponentRef { get; set; }
    protected AssignmentConfirmationComponent AssignmentConfirmationComponentRef { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new OperatorAssignmentViewModel();
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            IsLoading = true;

            if (Guid.TryParse(VoyageId, out Guid voyageGuid))
            {
                // Load voyage data
                await LoadVoyageDataAsync(voyageGuid);
            }
            else
            {
                // Invalid voyage ID, navigate back
                NavigateToFindVoyage();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading voyage data for operator assignment");
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load voyage data. Please try again.",
                Duration = 4.5
            });
            NavigateToFindVoyage();
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }

        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA LOADING
    private async Task LoadVoyageDataAsync(Guid voyageId)
    {
        var query = new GetOpenVoyagesQuery(await SrpProcessors.GetQueryContextAsync())
        {
            IsActive = true
        };

        var openVoyages = await SrpProcessors.QueryProcessor.ExecuteAsync(query);
        var voyageData = openVoyages.FirstOrDefault(v => v.VoyageId == voyageId);

        if (voyageData != null)
        {
            ViewModel.SetVoyageData(voyageId, voyageData);
        }
        else
        {
            throw new InvalidOperationException($"Voyage with ID {voyageId} not found or is not available for operator assignment.");
        }
    }
    #endregion

    #region METHODS - NAVIGATION
    protected void NavigateToFindVoyage()
    {
        NavigationManager.NavigateTo("/find-voyage");
    }

    protected void HandleBackToSelectOperator()
    {
        ViewModel.NavigateToSelectOperator();
        StateHasChanged();
    }

    protected async Task HandleSelectOperator(AvailableCaptainListItem selectedOperator)
    {
        try
        {
            // Store selected operator data
            ViewModel.SetSelectedOperator(selectedOperator);

            // Navigate to assignment confirmation
            ViewModel.NavigateToConfirmAssignment();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error selecting operator");
            await NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to select operator. Please try again.",
                Duration = 4.5
            });
        }
    }

    protected async Task HandleAssignmentConfirmed()
    {
        // Navigate back to find voyage page after successful assignment
        NavigationManager.NavigateTo("/find-voyage");
    }
    #endregion
}
