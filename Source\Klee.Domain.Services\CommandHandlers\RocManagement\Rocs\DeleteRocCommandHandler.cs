﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Services.CommandHandlers.RocManagement.Rocs.Helpers;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers;
using Klee.Domain.Services.Repositories.RocManagement;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.RocManagement.Rocs;

public sealed class DeleteRocCommandHandler
    : RequestHandlerAsync<DeleteRocCommand>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public DeleteRocCommandHandler(IRocSrpRepository rocSrpRepository,
        IMemoryCache memoryCache)
    {
        RocSrpRepository = rocSrpRepository;
        MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    public override async Task<DeleteRocCommand> HandleAsync(DeleteRocCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Find ROC
        Roc roc = await RocSrpRepository.FindAsync(_ => _.RocId == command.RocId &&
                                                        _.EntityPartitionKey == command.RocId, command);

        if (roc != null)
        {
            // Set IsActive to false (soft delete)
            roc.IsActive = false;

            // Update
            await RocSrpRepository.UpdateAsync(roc, command);

            // Clear Caches
            MemoryCache.RemoveRoc(command.RocId);
        }
        else
        {
            throw new EntityNotFoundException(
                $"Roc with roc id '{command.RocId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }
    #endregion
}