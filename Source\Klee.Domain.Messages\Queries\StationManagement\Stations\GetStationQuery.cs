﻿using Klee.Domain.Entities.Entities.StationManagement.Stations;
using Klee.Domain.Entities.StationManagement.Stations;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.StationManagement.Stations;

public class GetStationQuery
    : QueryBase<Station>
{
    #region PROPERTIES
    public string StationId { get; }

    public bool AllowCached { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetStationQuery(string stationId,
                           IQueryContext context)
        : base(context)
    {
        this.StationId = stationId;
    }
    #endregion
}