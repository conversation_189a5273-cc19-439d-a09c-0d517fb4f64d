@using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList.Data
@using DevExpress.Blazor
@using Microsoft.AspNetCore.Authorization
@using Renoir.Srp.Portal.Web.Application.Security
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationCreate

@page "/Organizations/Management/Organizations"
@inherits OrganizationListViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody PageHeaderTitle="Organizations"
                 ParentView="this"
                 CanShowButtonNew="true"
                 CanShowToolbarRightContent="@IsUserSessionDataRestored"
                 OnClickNew="@OnClickNew">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Organizations" />
        <RMainLayoutBodyBreadcrumbListItem Text="Overview" />
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
    </ToolbarRightMenuItems>
    <Content>
        <div class="row">
            <div class="col">
                <section class="card">
                    <header class="card-header">
                        <h2 class="card-title">Organizations</h2>
                        <div class="card-actions">
                            <RCardActionShowFilter @ref="@RCardActionShowFilterRow"/>
                            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection"/>
                            <RCardActionToggle />
                        </div>
                    </header>
                    <div class="card-body">
                        <DxToolbar>
                            <Items>
                                <DxToolbarItem>
                                    <Template>
                                        <div class="control-value pe-2" style="width: 100px">
                                            <DxComboBox Data="@OrganizationListViewModel.SelectableOrganizationMainFilterTypeIds"
                                                        ListRenderMode="@ListRenderMode.Entire"
                                                        FilteringMode="@DataGridFilteringMode.None"
                                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                                        Value="@ViewModel.OrganizationsMainFilterTypeId"
                                                        ValueChanged="@((OrganizationsMainFilterTypeIds organizationsMainFilterTypeId) => OnValueChanged_OrganizationsMainFilterTypeId(organizationsMainFilterTypeId))"
                                                        SizeMode="SizeMode.Small" />
                                        </div>
                                    </Template>
                                </DxToolbarItem>
                            </Items>
                        </DxToolbar>
              
                        <DxGrid Data="@ViewModel.Organizations"
                                @ref="@DxGrid"
                                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                                SelectionMode="GridSelectionMode.Multiple"
                                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                PageSize="15"
                                PageSizeSelectorVisible="true"
                                PageSizeSelectorAllRowsItemVisible="true"
                                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                                PagerNavigationMode="PagerNavigationMode.InputBox"
                                PageIndex="@ListViewProperties.DxGridPageIndex"
                                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                                FocusedRowChanged="@OnDxGrid_FocusedRowChanged"
                                SizeMode="SizeMode.Small">
                            <Columns>
                                <DxGridSelectionColumn Width="40px"
                                                       VisibleIndex="1"
                                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection"/>
                                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                                           OnClickDelete="OnDxGrid_ClickDelete"
                                                           CanShowButtonDelete=false
                                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                                           VisibleIndex="2"/>
                                <DxGridDataColumn FieldName="@nameof(OrganizationListItem.OrganizationId)" Caption="Organization Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="3"/>
                                <DxGridDataColumn FieldName="@nameof(OrganizationListItem.Name)" Caption="Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4"/>
                                <DxGridDataColumn FieldName="@nameof(OrganizationListItem.Code)" Caption="Code" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5"/>
                                @if (this.ViewModel.OrganizationsMainFilterTypeId != OrganizationsMainFilterTypeIds.Active)
                                {
                                    <DxGridDataColumn FieldName="@nameof(OrganizationListItem.IsActive)"
                                                      Width="60px" VisibleIndex="7">
                                        <CellDisplayTemplate>
                                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                                        </CellDisplayTemplate>
                                    </DxGridDataColumn>
                                }
                                <RDxGridEmptyDataColumn/>
                            </Columns>
                        </DxGrid>
                    </div>
                </section>
            </div>
        </div>
    </Content>
</RMainLayoutBody>

<OrganizationCreateDialogView @ref="OrganizationCreateDialogView"
                             OnDialogViewOpened="OnOrganizationCreateDialogView_Opened"
                             OnDialogViewClosed="OnOrganizationCreateDialogView_Closed" />

@code {
    #region METHODS - STATIC
    public static string GetUri()
    {
        return $"/Organizations/Management/Organizations";
    }
    #endregion
}