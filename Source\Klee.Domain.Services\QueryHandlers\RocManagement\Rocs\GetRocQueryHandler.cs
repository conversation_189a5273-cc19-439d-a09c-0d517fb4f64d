﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.RocManagement.Rocs;

public sealed class GetRocQueryHandler
    : QueryHandlerAsync<GetRocQuery, Roc>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public GetRocQueryHandler(IRocSrpRepository rocSrpRepository,
        IMemoryCache memoryCache)
    {
        this.RocSrpRepository = rocSrpRepository;
        this.MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS

    public override async Task<Roc> ExecuteAsync(GetRocQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        // Init
        string rocId = query.RocId;

        // Get ROC from cache
        var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetRocCacheId(rocId), out Roc cachedRoc);

        // Init
        Roc roc = cachedRoc;

        // Get ROC from DB (if needed)
        if (query.AllowCached == false || query.IncludeRelations ||
            !isCached) {

            IQueryable<Roc>? rocs =  RocSrpRepository.Entities(query);
            // Get ROC 

            //Include relations if needed
            if (query.IncludeRelations) {
                rocs =  rocs.Include(_ => _.Stations);
            } 
            roc = await rocs
                .FirstOrDefaultAsync(_ => _.RocId == rocId && _.EntityPartitionKey == rocId, cancellationToken);
               
            // Cache ROC
            this.MemoryCache.Set(MemoryCacheIds.GetRocCacheId(rocId),
                roc, new MemoryCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                    Size = 1
                });
        }

        return roc;
    }
    #endregion
}