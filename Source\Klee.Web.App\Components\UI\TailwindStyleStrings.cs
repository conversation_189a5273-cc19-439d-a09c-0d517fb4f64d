using System;

namespace Klee.Web.App.Components.UI
{
    public static class TailwindStyleStrings
    {
        public static class Button
        {
            public static string Primary => "bg-teal-700 hover:bg-teal-600 border border-teal-700 hover:border-teal-600 text-white rounded-md";
            public static string Secondary => "bg-teal-50 hover:bg-teal-100 text-teal-700 border border-teal-200 hover:border-teal-300 rounded-md";
            public static string Outline => "bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 rounded-md";
            public static string Ghost => "bg-transparent hover:bg-gray-50 text-gray-600 hover:text-teal-700 rounded-md";
            public static string Link => "bg-transparent text-teal-700 hover:text-teal-600 underline-offset-4 hover:underline rounded-md";
            public static string Danger => "bg-red-600 hover:bg-red-700 text-white border border-red-600 hover:border-red-700 rounded-md";
            public static string DangerGhost => "bg-transparent hover:bg-red-50 text-red-600 hover:text-red-700 rounded-md";
        }

        public static class Table
        {
            public static string Container => "min-w-full divide-y divide-gray-200";
            public static string Header => "bg-gray-50";
            public static string HeaderCell => "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider";
            public static string Row => "bg-white hover:bg-gray-50";
            public static string Cell => "px-6 py-4 whitespace-nowrap text-sm text-gray-500";
        }

        public static class Card
        {
            public static string Container => "bg-white shadow-sm rounded-lg border border-gray-200";
            public static string Header => "px-6 py-4 border-b border-gray-200";
            public static string Title => "text-lg font-medium text-gray-900";
            public static string Content => "px-6 py-4";
        }

        public static class Badge
        {
            public static string Success => "bg-green-100 text-green-800 border-green-200";
            public static string Warning => "bg-amber-100 text-amber-800 border-amber-200";
            public static string Error => "bg-red-100 text-red-800 border-red-200";
            public static string Info => "bg-blue-100 text-blue-800 border-blue-200";
            public static string Default => "bg-gray-100 text-gray-800 border-gray-200";
        }

        public static class Icon
        {
            public static string Default => "text-teal-700 hover:text-teal-600";
        }

        public static class Form
        {
            public static string Input => "w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200";
            public static string InputNumber => "w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200";
            public static string Select => "w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200";
            public static string TextArea => "w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 resize-vertical";
            public static string Checkbox => "text-teal-600 focus:ring-teal-500 border-gray-300 rounded";
            public static string TimePicker => "w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200";
        }
    }
}