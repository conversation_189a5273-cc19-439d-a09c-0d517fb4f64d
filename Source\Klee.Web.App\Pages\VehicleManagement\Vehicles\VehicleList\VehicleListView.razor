﻿@using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data
@using DevExpress.Blazor
@using Microsoft.AspNetCore.Authorization
@using Renoir.Srp.Portal.Web.Application.Security
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleCreate


@page "/Vehicles/Management/Vehicles"
@inherits VehicleListViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody PageHeaderTitle="Vehicles"
                 ParentView="this"
                 CanShowButtonNew=true
                 CanShowToolbarRightContent="@IsUserSessionDataRestored"
                 OnClickNew="@OnClickNew">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Vehicles" />
        <RMainLayoutBodyBreadcrumbListItem Text="Overview" />
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
    </ToolbarRightMenuItems>
    <Content>
        <div class="row">
            <div class="col">
                <section class="card">
                    <header class="card-header">
                        <h2 class="card-title">Overview</h2>
                        <div class="card-actions">
                            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
                            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
                            <RCardActionToggle />
                        </div>
                    </header>
                    <div class="card-body">
                        <DxToolbar>
                            <Items>
                                <DxToolbarItem>
                                    <Template>
                                        <div class="control-value pe-2" style="width: 100px">
                                            <DxComboBox Data="@VehicleListViewModel.SelectableVehiclesMainFilterTypeIds"
                                                        ListRenderMode="@ListRenderMode.Entire"
                                                        FilteringMode="@DataGridFilteringMode.None"
                                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                                        Value="@ViewModel.VehiclesMainFilterTypeId"
                                                        ValueChanged="@((VehiclesMainFilterTypeIds vehiclesMainFilterTypeId) => OnValueChanged_VehiclesMainFilterTypeId(vehiclesMainFilterTypeId))"
                                                        SizeMode="SizeMode.Small" />
                                        </div>
                                    </Template>
                                </DxToolbarItem>
                            </Items>
                        </DxToolbar>

                        <DxGrid Data="@ViewModel.Vehicles"
                                @ref="@DxGrid"
                                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                                SelectionMode="GridSelectionMode.Multiple"
                                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                PageSize="15"
                                PageSizeSelectorVisible="true"
                                PageSizeSelectorAllRowsItemVisible="true"
                                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                                PagerNavigationMode="PagerNavigationMode.InputBox"
                                PageIndex="@ListViewProperties.DxGridPageIndex"
                                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                                SizeMode="SizeMode.Small">
                            <Columns>
                                <DxGridSelectionColumn Width="40px"
                                                       VisibleIndex="1" 
                                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                                           OnClickDelete="OnDxGrid_ClickDelete"
                                                           CanShowButtonDelete=false
                                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                                           VisibleIndex="2" />
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.VehicleTypeDisplayName)" Caption="Vehicle Type" Width="100px" VisibleIndex="3"/>
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.VehicleId)" Caption="Vehicle Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4"/>
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.VehicleName)" Caption="Vehicle Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5"/>
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.ENI)" Caption="ENI" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="6" />
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.HourlyRateInEuros)" Caption="Hourly Rate (Euro)" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="7" />
                                <DxGridDataColumn FieldName="@nameof(VehicleListItem.OrganizationId)" Caption="OrganizationId" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="8" />
                                @if (this.ViewModel.VehiclesMainFilterTypeId != VehiclesMainFilterTypeIds.Active)
                                {
                                    <DxGridDataColumn FieldName="@nameof(VehicleListItem.IsActive)"
                                                      Width="60px" VisibleIndex="9">
                                        <CellDisplayTemplate>
                                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                                        </CellDisplayTemplate>
                                    </DxGridDataColumn>
                                }
                                <RDxGridEmptyDataColumn/>
                            </Columns>
                        </DxGrid>
                    </div>
                </section>
            </div>
        </div>
    </Content>
</RMainLayoutBody>

<VehicleCreateDialogView @ref="VehicleCreateDialogView"
                         OnDialogViewOpened="OnVehicleCreateDialogView_Opened"
                         OnDialogViewClosed="OnVehicleCreateDialogView_Closed" />

@code {
    #region METHODS - STATIC
    public static string GetUri()
    {
        return $"/Vehicles/Management/Vehicles";
    }
    #endregion
}