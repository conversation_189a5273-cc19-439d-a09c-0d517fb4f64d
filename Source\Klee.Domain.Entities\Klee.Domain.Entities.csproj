﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
	<LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Entities\NewFolder1\**" />
    <EmbeddedResource Remove="Entities\NewFolder1\**" />
    <None Remove="Entities\NewFolder1\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Claus.Domain.Entities.csproj.DotSettings" />
    <None Remove="Klee.Domain.Entities.csproj.DotSettings" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Renoir.Application.Core" Version="1.1.25078.1-beta" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\Exceptions\JobManagement\JobProposals\" />
    <Folder Include="Entities\QualificationManagement\Qualifications\Data\" />
    <Folder Include="Entities\StationManagement\Stations\Data\" />
    <Folder Include="Entities\VoyageManagement\Voyages\" />
  </ItemGroup>

</Project>
