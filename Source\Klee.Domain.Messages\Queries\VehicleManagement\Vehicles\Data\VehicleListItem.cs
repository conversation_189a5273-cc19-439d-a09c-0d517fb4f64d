﻿using System;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;

namespace Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data
{
    public class VehicleListItem
    {
        #region PROPERTIES
        public long EntityId { get; set; }

        public string VehicleTypeDisplayName { get; set; } = "";
        public string VehicleId { get; set; } = "";

        public string VehicleName { get; set; } = "";

        public string ENI { get; set; } = "";
        public double HourlyRateInEuros { get; set; } = 0.0;
        public double Length { get; set; } = 0.0;
        public double Beam { get; set; } = 0.0;
        public VesselTypeIds VesselType { get; set; } = VesselTypeIds.None;
        public string OrganizationId { get; set; } = "";
        public bool IsActive { get; set; }
        #endregion
    }
}
