﻿using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleCreate;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Renoir.Web.Razor.Services.UserNotifications;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;
using AntDesign;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleEdit;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList;

public class VesselsViewBase: ComponentBase {
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<AddVesselViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }

    #endregion

    #region PROPERTIES
    protected VesselsViewModel ViewModel { get; set; }

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;
    #endregion


    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync() {
        // Init
        this.ViewModel = new VesselsViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Load OrganizationVehicles
            await this.ViewModel.LoadOrganizationVehiclesAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            // Notify using AntDesign notifications
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Exception when setting parameters.",
                Duration = 6.0
            });

        }
    }
    #endregion

    #region DELETE MODAL
    protected bool DeleteModalVisible = false;
    protected string CurrentVesselId = "";
    protected string CurrentVesselName = "";

    protected async Task OnConfirmDelete()
    {
        try
        {
            // Delete Organization Vehicle
            await this.ViewModel.DeleteOrganizationVehicleAsync(CurrentVesselId);

            // Close modal
            DeleteModalVisible = false;

            // Notify using AntDesign notifications
            this.NotificationService.Success(new NotificationConfig()
            {
                Message = "Deleted",
                Description = $"Vessel '{CurrentVesselName}' has been deleted successfully.",
                Duration = 4.5
            });

            // Clear current vessel data
            CurrentVesselId = "";
            CurrentVesselName = "";

            //Reload vehicles
           await this.ViewModel.LoadOrganizationVehiclesAsync(forceLoad: true);
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when deleting vessel with id '{CurrentVesselId}'", CurrentVesselId);
            #endregion

            // Notify using AntDesign notifications
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Exception when deleting vessel '{CurrentVesselName}'",
                Duration = 6.0
            });
        }
    }

    protected void OnCancelDelete()
    {
        DeleteModalVisible = false;
        CurrentVesselId = "";
        CurrentVesselName = "";
    }
    #endregion

    #region EVENT HANDLERS
    protected void OnClick_AddVehicle()
    {
        this.NavigationManager.NavigateTo(AddVessel.GetUri());
    }

    protected void OnClick_EditVehicle(string id)
    {
        NavigationManager.NavigateTo(EditVessel.GetUri(id));
    }

    protected void OnClick_DeleteVehicle(string vehicleId, string vehicleName)
    {
        CurrentVesselId = vehicleId;
        CurrentVesselName = vehicleName;
        DeleteModalVisible = true;
    }
    #endregion
}