@using AntDesign
@using Klee.Web.App.Components.UI
@using EnumsNET

@inherits AssignmentConfirmationViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-bold text-teal-700">Assignment Summary</h2>
    <Button Type="@ButtonType.Default"
            Class="@TailwindStyleStrings.Button.Outline"
            OnClick="HandleOnClick_Back">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Operator Selection
    </Button>
</div>

@if (IsLoading)
{
    <div class="text-center py-12">
        <Spin Size="SpinSize.Large" />
        <p class="mt-4 text-gray-600">Loading assignment details...</p>
    </div>
}
else
{
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Voyage Details Card -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-teal-700 mb-4 border-b-2 border-teal-200 pb-3">Voyage Details</h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Vessel</p>
                            <p class="text-sm text-gray-900">@VoyageData?.VesselDisplayName</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Duration</p>
                            <p class="text-sm text-gray-900">@ViewModel.VoyageDurationHours.ToString("F1") hours</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Start Date & Time</p>
                            <p class="text-sm text-gray-900">@VoyageData?.StartDateTime.ToString("MMM dd, yyyy HH:mm")</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">End Date & Time</p>
                            <p class="text-sm text-gray-900">@VoyageData?.EndDateTime.ToString("MMM dd, yyyy HH:mm")</p>
                        </div>
                    </div>
                    
                    @if (!string.IsNullOrWhiteSpace(VoyageData?.Description))
                    {
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Description</p>
                            <p class="text-sm text-gray-900">@VoyageData.Description</p>
                        </div>
                    }

                    @if (VoyageData?.RequiredQualifications?.Any() == true)
                    {
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-2">Required Qualifications</p>
                            <div class="flex flex-wrap gap-2">
                                @foreach (var qualification in VoyageData.RequiredQualifications)
                                {
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-teal-100 text-teal-800">
                                        @qualification.AsString(EnumFormat.DisplayName)
                                    </span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </Card>

        <!-- Operator Details Card -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-teal-700 mb-4 border-b-2 border-teal-200 pb-3">Operator Details</h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Name</p>
                            <p class="text-sm text-gray-900">@SelectedOperator?.FullName</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Organization</p>
                            <p class="text-sm text-gray-900">@SelectedOperator?.OrganizationName</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Experience</p>
                            <p class="text-sm text-gray-900">@SelectedOperator?.ExperienceDisplay</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Hourly Rate</p>
                            <p class="text-sm text-gray-900">€@SelectedOperator?.HourlyRateInEuros.ToString("N2")</p>
                        </div>
                    </div>

                    @if (!string.IsNullOrWhiteSpace(SelectedOperator?.Biography))
                    {
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-1">Biography</p>
                            <p class="text-sm text-gray-900 leading-relaxed">@SelectedOperator.Biography</p>
                        </div>
                    }

                    @if (SelectedOperator?.Qualifications?.Any() == true)
                    {
                        <div>
                            <p class="text-sm font-medium text-teal-700 mb-2">Qualifications</p>
                            <div class="flex flex-wrap gap-2">
                                @foreach (var qualification in SelectedOperator.Qualifications)
                                {
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        @qualification.AsString(EnumFormat.DisplayName)
                                    </span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </Card>
    </div>

    <!-- Cost Summary Card -->
    <Card Class="@($"{TailwindStyleStrings.Card.Container} mt-8")">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-teal-700 mb-4 border-b-2 border-teal-200 pb-3">Cost Summary</h3>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-900 font-medium">Operator Cost (@ViewModel.VoyageDurationHours.ToString("F1") hours × €@SelectedOperator?.HourlyRateInEuros.ToString("N2"))</span>
                    <span class="text-gray-900 font-semibold">€@ViewModel.OperatorCost.ToString("N2")</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-gray-900 font-medium">Commission (@((ViewModel.CommissionRate * 100).ToString("F0"))%)</span>
                    <span class="text-gray-900 font-semibold">€@ViewModel.CommissionAmount.ToString("N2")</span>
                </div>

                <div class="border-t border-gray-200 pt-3">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-gray-900">Total Cost</span>
                        <span class="text-lg font-bold text-teal-700">€@ViewModel.TotalCost.ToString("N2")</span>
                    </div>
                </div>
            </div>
        </div>
    </Card>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-4 mt-6">
        <Button Type="@ButtonType.Default"
                Class="@TailwindStyleStrings.Button.Outline"
                OnClick="HandleOnClick_Back"
                Disabled="@ViewModel.IsAssigningOperator">
            Back to Operator Selection
        </Button>
        
        <Button Type="@ButtonType.Primary"
                Class="@TailwindStyleStrings.Button.Primary"
                OnClick="HandleConfirmAssignment"
                Loading="@ViewModel.IsAssigningOperator">
            @if (ViewModel.IsAssigningOperator)
            {
                <span>Assigning Operator...</span>
            }
            else
            {
                <span>Confirm Assignment</span>
            }
        </Button>
    </div>
}
