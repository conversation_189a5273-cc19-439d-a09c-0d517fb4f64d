﻿using System;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.VehicleManagement
{
    public class VehicleSrpRepository
        : EfDomainEntityRepository<AppSrpDbContext, Vehicle, long>, IVehicleSrpRepository
    {
        public VehicleSrpRepository(AppSrpDbContext dbContext,
                                    IDbExceptionConverter dbExceptionConverter,
                                    ILogger<VehicleSrpRepository> logger)
            : base(dbContext, dbExceptionConverter, (ILogger)logger)
        {
        }

        protected override void Dispose()
        {
            base.Dispose();
        }
    }
}