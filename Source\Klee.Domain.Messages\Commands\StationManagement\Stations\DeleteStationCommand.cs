﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.StationManagement.Stations;

public class DeleteStationCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
    }
    #endregion

    #region PROPERTIES
    public string StationId { get; }

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public DeleteStationCommand(string StationId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.StationId = StationId;
    }
    #endregion
}