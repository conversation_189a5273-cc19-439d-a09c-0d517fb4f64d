﻿
@using Klee.Domain.Messages.Queries.UserManagement.Users.Data
@using Klee.Web.App.Pages.UserManagement.Users.UserProfileList.Data
@using Microsoft.AspNetCore.Authorization
@using Renoir.Srp.Portal.Web.Application.Security
@using Klee.Web.App.Pages.UserManagement.Users.UserProfileCreate

@page "/UserProfiles/Management/UserProfiles"

@inherits UserProfileListViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody PageHeaderTitle="UserProfiles"
                 ParentView="this"
                 CanShowButtonNew=true
                 CanShowToolbarRightContent="@IsUserSessionDataRestored"
                 OnClickNew="@OnClickNew">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="User Profiles" />
        <RMainLayoutBodyBreadcrumbListItem Text="Overview" />
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
    </ToolbarRightMenuItems>
    <Content>
        <div class="row">
            <div class="col">
                <section class="card">
                    <header class="card-header">
                        <h2 class="card-title">Overview</h2>
                        <div class="card-actions">
                            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
                            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
                            <RCardActionToggle />
                        </div>
                    </header>
                    <div class="card-body">
                        <DxToolbar>
                            <Items>
                                <DxToolbarItem>
                                    <Template>
                                        <div class="control-value pe-2" style="width: 100px">
                                            <DxComboBox Data="@UserProfileListViewModel.SelectableUserProfilesMainFilterTypeIds"
                                                        ListRenderMode="@ListRenderMode.Entire"
                                                        FilteringMode="@DataGridFilteringMode.None"
                                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                                        Value="@ViewModel.UserProfilesMainFilterTypeId"
                                                        ValueChanged="@((UserProfilesMainFilterTypeIds userProfilesMainFilterTypeId) => OnValueChanged_UserProfilesMainFilterTypeId(userProfilesMainFilterTypeId))"
                                                        SizeMode="SizeMode.Small" />
                                        </div>
                                    </Template>
                                </DxToolbarItem>
                            </Items>
                        </DxToolbar>

                        <DxGrid Data="@ViewModel.UserProfiles"
                                @ref="@DxGrid"
                                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                                SelectionMode="GridSelectionMode.Multiple"
                                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                PageSize="15"
                                PageSizeSelectorVisible="true"
                                PageSizeSelectorAllRowsItemVisible="true"
                                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                                PagerNavigationMode="PagerNavigationMode.InputBox"
                                PageIndex="@ListViewProperties.DxGridPageIndex"
                                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                                SizeMode="SizeMode.Small">
                            <Columns>
                                <DxGridSelectionColumn Width="40px"
                                                       VisibleIndex="1" 
                                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                                           OnClickDelete="OnDxGrid_ClickDelete"
                                                           CanShowButtonDelete=false
                                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                                           VisibleIndex="2" />
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.UserId)" Caption="User Id" Width="100px" VisibleIndex="3"/>
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.FirstName)" Caption="First Name" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4"/>
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.LastName)" Caption="Last Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5"/>
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.DisplayName)" Caption="Display Name" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="6" />
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.Email)" Caption="Email" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="7" />
                                <DxGridDataColumn FieldName="@nameof(UserProfileListItem.OrganizationId)" Caption="Organization Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="8" />
                                @if (this.ViewModel.UserProfilesMainFilterTypeId != UserProfilesMainFilterTypeIds.Active)
                                {
                                    <DxGridDataColumn FieldName="@nameof(UserProfileListItem.IsActive)"
                                                      Width="60px" VisibleIndex="7">
                                        <CellDisplayTemplate>
                                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                                        </CellDisplayTemplate>
                                    </DxGridDataColumn>
                                }
                                <RDxGridEmptyDataColumn/>
                            </Columns>
                        </DxGrid>
                    </div>
                </section>
            </div>
        </div>
    </Content>
</RMainLayoutBody>

<UserProfileCreateDialogView @ref="UserProfileCreateDialogView"
                         OnDialogViewOpened="OnUserProfileCreateDialogView_Opened"
                         OnDialogViewClosed="OnUserProfileCreateDialogView_Closed" />

@code {
    #region METHODS - STATIC
    public static string GetUri()
    {
        return $"/UserProfiles/Management/UserProfiles";
    }
    #endregion
}
