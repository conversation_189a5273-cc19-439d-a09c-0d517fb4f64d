using System;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Klee.Web.App.Components.Pages.VoyageManagement.FindVoyage;

public class FindVoyageViewBase : ComponentBase
{
    #region DI
    [Inject]
    protected NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<FindVoyageViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES
    protected FindVoyageViewModel ViewModel { get; set; }

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        this.ViewModel = new FindVoyageViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            // Check user authorization using the user authentication service
            this.IsUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

            // Load Open Voyages
            await this.ViewModel.LoadOpenVoyagesAsync(forceLoad: true);

            //
            await base.OnParametersSetAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion
        }
    }
    #endregion

    #region METHODS
    protected async Task OnClickRefresh()
    {
        try
        {
            await this.ViewModel.LoadOpenVoyagesAsync(forceLoad: true);
            StateHasChanged();
        }
        catch (Exception exception)
        {
            this.Logger.LogError(exception, "Exception when refreshing open voyage data.");
        }
    }

    protected void HandleSelectVoyage(Guid voyageId)
    {
        // Navigate to operator assignment workflow
        NavigationManager.NavigateTo($"/find-voyage/assign-operator/{voyageId}");
    }
    #endregion
}
