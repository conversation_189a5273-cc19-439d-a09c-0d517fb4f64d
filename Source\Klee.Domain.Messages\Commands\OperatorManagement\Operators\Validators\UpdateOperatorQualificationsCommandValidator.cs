﻿using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;

public class UpdateOperatorQualificationsCommandValidator : AbstractValidator<UpdateOperatorQualificationsCommand> {
    public UpdateOperatorQualificationsCommandValidator() {
        this.RuleFor(_ => _.OperatorId).NotNull();
        this.RuleFor(_ => _.OperatorId).NotEmpty();
        this.RuleFor(_ => _.Qualifications).NotNull();
    }

}