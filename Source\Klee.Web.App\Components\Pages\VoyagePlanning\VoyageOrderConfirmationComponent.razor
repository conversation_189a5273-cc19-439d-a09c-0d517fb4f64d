@using EnumsNET

@inherits VoyageOrderConfirmationViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-bold text-teal-700">Order Summary</h2>
    <Button Type="@ButtonType.Default"
            Class="@TailwindStyleStrings.Button.Outline"
            OnClick="HandleOnClick_Back">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Captain Selection
    </Button>
</div>

@if (IsLoading)
{
    <div class="text-center py-12">
        <Spin Size="SpinSize.Large" />
        <p class="mt-4 text-gray-600">Loading voyage details...</p>
    </div>
}
else
{
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Journey Details Card -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-teal-700 mb-4 pb-3 border-b border-teal-200">
                    <i class="fas fa-route mr-2"></i>
                    Journey Details
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Column 1: Vessel and Voyage Dates -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Vessel</label>
                            <p class="text-gray-900 font-medium">@(ViewModel.VesselInfo?.VehicleNameAndTypeDisplayLabel ?? SelectedVesselId)</p>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Date & Time</label>
                            <p class="text-gray-900 font-medium">@ViewModel.GetVoyageDateRangeDisplay()</p>
                        </div>
                    </div>

                    <!-- Column 2: Duration and Required Qualifications -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Duration</label>
                            <p class="text-gray-900 font-medium">@ViewModel.GetVoyageDurationDisplay()</p>
                        </div>

                        @if (RequiredQualifications?.Any() == true)
                        {
                            <div>
                                <label class="block text-sm font-semibold text-teal-700 mb-2">Required Qualifications</label>
                                <div class="flex flex-wrap gap-2">
                                    @foreach (var qualification in RequiredQualifications)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                            @qualification.AsString(EnumFormat.DisplayName)
                                        </span>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Description))
                {
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <label class="block text-sm font-semibold text-teal-700 mb-2">Description</label>
                        <p class="text-gray-900 font-medium whitespace-pre-wrap">@Description</p>
                    </div>
                }
            </div>
        </Card>

        <!-- Captain Details Card -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-teal-700 mb-4 pb-3 border-b border-teal-200">
                    <i class="fas fa-user-tie mr-2"></i>
                    Selected Captain
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Column 1: Name and Organization -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Name</label>
                            <p class="text-gray-900 font-semibold">@SelectedCaptain.FullName</p>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Organization</label>
                            <p class="text-gray-900 font-medium">@SelectedCaptain.OrganizationName</p>
                        </div>
                    </div>

                    <!-- Column 2: Experience and Hourly Rate -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Experience</label>
                            <p class="text-gray-900 font-medium">@SelectedCaptain.ExperienceDisplay</p>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-teal-700 mb-1">Hourly Rate</label>
                            <p class="text-gray-900 font-semibold">€@SelectedCaptain.HourlyRateInEuros.ToString("N2")</p>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    </div>

    <!-- Order Summary Card -->
    <Card Class=@($"{TailwindStyleStrings.Card.Container} mt-6")>
        <div class="p-6">
            <h3 class="text-lg font-semibold text-teal-700 mb-4 pb-3 border-b border-teal-200">
                <i class="fas fa-calculator mr-2"></i>
                Cost Breakdown
            </h3>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-900 font-medium">Operator Cost (@ViewModel.VoyageDurationHours.ToString("F1") hours × €@SelectedCaptain?.HourlyRateInEuros.ToString("N2"))</span>
                    <span class="text-gray-900 font-semibold">€@ViewModel.OperatorCost.ToString("N2")</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-gray-900 font-medium">Commission (@((ViewModel.CommissionRate * 100).ToString("F0"))%)</span>
                    <span class="text-gray-900 font-semibold">€@ViewModel.CommissionAmount.ToString("N2")</span>
                </div>

                <div class="border-t-2 border-teal-300 pt-3 mt-4">
                    <div class="flex justify-between items-center text-lg font-semibold">
                        <span class="text-teal-700 font-semibold">Total Cost</span>
                        <span class="text-gray-900 font-bold">€@ViewModel.TotalCost.ToString("N2")</span>
                    </div>
                </div>
            </div>
        </div>
    </Card>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-4 mt-6">
        <Button Type="@ButtonType.Default"
                Class="@TailwindStyleStrings.Button.Outline"
                OnClick="HandleOnClick_Back"
                Disabled="@ViewModel.IsCreatingVoyage">
            Back to Captain Selection
        </Button>
        
        <Button Type="@ButtonType.Primary"
                Class="@TailwindStyleStrings.Button.Primary"
                OnClick="HandleConfirmVoyage"
                Loading="@ViewModel.IsCreatingVoyage">
            @if (ViewModel.IsCreatingVoyage)
            {
                <span>Confirming Voyage...</span>
            }
            else
            {
                <span>Confirm Voyage</span>
            }
        </Button>
    </div>
}
