﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.VoyageManagement.Voyages;

namespace Klee.Domain.Services.MessagingService;

public interface IEmailService {
    Task SendEmailAsync(string toEmail, string subject, string htmlContent);
}

public interface IVoyageNotificationService
{
    Task SendVoyageOpportunityNotificationsAsync(Voyage voyage, Vehicle vehicle, Organization bookingOrganization, List<Organization> targetOrganizations);
}