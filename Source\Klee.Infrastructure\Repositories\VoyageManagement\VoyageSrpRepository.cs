using System;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.VoyageManagement
{
    public class VoyageSrpRepository
        : EfDomainEntityRepository<AppSrpDbContext, Voyage, Guid>, IVoyageSrpRepository
    {
        public VoyageSrpRepository(AppSrpDbContext dbContext,
                                   IDbExceptionConverter dbExceptionConverter,
                                   ILogger<VoyageSrpRepository> logger)
            : base(dbContext, dbExceptionConverter, (ILogger)logger)
        {
        }

        protected override void Dispose()
        {
            base.Dispose();
        }
    }
}
