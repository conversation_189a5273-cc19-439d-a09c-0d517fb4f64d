﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators.Helpers;

public static class MemoryCacheExtensions {
    public static void RemoveOperator(this IMemoryCache memoryCache,
        string operatorId)
    {
        memoryCache.Remove(MemoryCacheIds.GetRocListCacheId);
        memoryCache.Remove(MemoryCacheIds.GetRocCacheId(operatorId));
    }
}