using System;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.OrganizationManagement
{
    public class OrganizationSrpRepository
        : EfDomainEntityRepository<AppSrpDbContext, Organization, long>, IOrganizationSrpRepository
    {
        public OrganizationSrpRepository(AppSrpDbContext dbContext,
                                    IDbExceptionConverter dbExceptionConverter,
                                    ILogger<OrganizationSrpRepository> logger)
            : base(dbContext, dbExceptionConverter, (ILogger)logger)
        {
        }

        protected override void Dispose()
        {
            base.Dispose();
        }
    }
} 