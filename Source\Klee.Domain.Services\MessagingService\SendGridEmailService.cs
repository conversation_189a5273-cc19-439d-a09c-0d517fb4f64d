﻿using Klee.Domain.Services.MessagingService;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Threading.Tasks;

public class SendGridEmailService : IEmailService
{
    private readonly SendGridSettings _settings;

    public SendGridEmailService(IOptions<SendGridSettings> settings)
    {
        _settings = settings.Value;
    }

    public async Task SendEmailAsync(string toEmail, string subject, string htmlContent)
    {
        SendGridClient client = new (_settings.ApiKey);
        EmailAddress from = new (_settings.FromEmail, _settings.FromName);
        EmailAddress to = new (toEmail);
        SendGridMessage? msg = MailHelper.CreateSingleEmail(from, to, subject, "", htmlContent);
        
        await client.SendEmailAsync(msg);
    }
}

public class SendGridSettings
{
    public string ApiKey { get; set; }
    public string FromEmail { get; set; }
    public string FromName { get; set; }
}