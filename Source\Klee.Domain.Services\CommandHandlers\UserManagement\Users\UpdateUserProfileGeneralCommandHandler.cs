﻿using Klee.Domain.Entities.Common.Exceptions;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users.Validators;
using Klee.Domain.Services.CommandHandlers.UserManagement.Users.Helpers;
using Klee.Domain.Services.Repositories.UserManagement.Users;

namespace Klee.Domain.Services.CommandHandlers.UserManagement.Users;

public sealed class UpdateUserProfileGeneralCommandHandler
    : RequestHandlerAsync<UpdateUserProfileGeneralCommand> {
    #region PROPERTIES

    private IUserProfileSrpRepository UserProfileSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateUserProfileGeneralCommandHandler(IUserProfileSrpRepository userProfileSrpRepository,
        IMemoryCache memoryCache) {
        UserProfileSrpRepository = userProfileSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateUserProfileGeneralCommandValidator))]
    public override async Task<UpdateUserProfileGeneralCommand> HandleAsync(UpdateUserProfileGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {
        //Get UserProfile (if it exists) 
        if (await UserProfileSrpRepository.ExistsAsync(_ => _.UserId == command.UserId &&
                                                            _.EntityPartitionKey == command.UserId,
                command)) {
            UserProfile userProfile = await UserProfileSrpRepository.FindAsync(_ => _.UserId == command.UserId &&
                    _.EntityPartitionKey == command.UserId,
                command);

            userProfile.FirstName = command.FirstName;
            userProfile.LastName = command.LastName;
            userProfile.DisplayName = command.DisplayName;
            userProfile.Email = command.Email;

            // Update
            await UserProfileSrpRepository.UpdateAsync(userProfile, command);

            // Set Result
            command.Result.EntityId = userProfile.EntityId;

            // Clear Caches
            MemoryCache.RemoveUserProfile(command.UserId);
        }
        else {
            throw new EntityNotFoundException(
                $"UserProfile with UserProfile id '{command.UserId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}