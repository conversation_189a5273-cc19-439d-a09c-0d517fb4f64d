﻿using Microsoft.EntityFrameworkCore;
using Renoir.Application.Domain.Contexts.Containers;
using Renoir.Application.EF.Data;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Security;
using Renoir.Application.SoftwareEnvironments;

namespace Klee.Infrastructure.Data
{
    public partial class AppSrpDbContext
        : AppDbContextBase<AppSrpDbContext>, IAppSrpDbContext, IDomainEntityRepositoryExecuteContextContainer
    {
        #region CONSTANTS
        #endregion

        #region PROPERTIES
        IAuthenticationState IDomainEntityRepositoryExecuteContextContainer.AuthenticationState
        {
            get => this.AuthenticationState;
            set => this.AuthenticationState = value;
        }

        ISoftwareEnvironmentInfo IDomainEntityRepositoryExecuteContextContainer.SoftwareEnvironmentInfo
        {
            get => this.SoftwareEnvironmentInfo;
            set => this.SoftwareEnvironmentInfo = value;
        }
        #endregion

        #region CONSTRUCTORS
        public AppSrpDbContext(DbContextOptions<AppSrpDbContext> options)
            : base(options)
        {
        }
        #endregion

        #region METHODS - SAVE CHANGES
        #endregion
    }
}