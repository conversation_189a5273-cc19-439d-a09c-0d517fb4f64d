﻿using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.StationManagement.Stations;

public class UpdateStationGeneralCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string StationId { get; }
    public string StationName { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public string RocId { get; }
    public JoystickTypeIds JoystickTypeId { get; set; } = JoystickTypeIds.Undefined;

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateStationGeneralCommand(string stationId,
        string rocId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.StationId = stationId;
        this.RocId = rocId;
    }
    #endregion
}