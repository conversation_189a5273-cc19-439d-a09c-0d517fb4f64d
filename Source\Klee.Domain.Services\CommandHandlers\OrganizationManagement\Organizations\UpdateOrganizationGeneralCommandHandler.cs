﻿using System;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations.Helpers;

namespace Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations;

public sealed class UpdateOrganizationGeneralCommandHandler
    : RequestHandlerAsync<UpdateOrganizationGeneralCommand> {
    #region PROPERTIES

    private IOrganizationSrpRepository OrganizationSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOrganizationGeneralCommandHandler(IOrganizationSrpRepository organizationSrpRepository,
        IMemoryCache memoryCache) {
        OrganizationSrpRepository = organizationSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationGeneralCommandValidator))]
    public override async Task<UpdateOrganizationGeneralCommand> HandleAsync(UpdateOrganizationGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        //Get org (if it exist) 
        if (await OrganizationSrpRepository.ExistsAsync(_ => _.OrganizationId == command.OrganizationId &&
                                                              _.EntityPartitionKey == command.OrganizationId,
                command)) {

            Organization organization = await OrganizationSrpRepository.FindAsync(_ => _.OrganizationId == command.OrganizationId &&
                                                                              _.EntityPartitionKey == command.OrganizationId,
                command);

            organization.Name = command.Name;
            organization.Code = command.Code;
            organization.Description = command.Description;
            // Update
            await OrganizationSrpRepository.UpdateAsync(organization, command);

            // Set Result
            command.Result.EntityId = organization.EntityId;

            // Clear Caches
            MemoryCache.RemoveOrganization(command.OrganizationId);
        }
        else {
            throw new EntityNotFoundException(
                $"Organization with organization id '{command.OrganizationId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}