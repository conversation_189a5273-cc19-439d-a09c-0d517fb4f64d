﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.VehicleManagement
{
    public class VehicleEntityTypeConfiguration : IEntityTypeConfiguration<Vehicle>
    {
        public void Configure(EntityTypeBuilder<Vehicle> builder) {
            
            //builder.AddCosmosDbProperties();
            builder.HasIndex(_ => _.VehicleId)
                   .IsUnique();
            builder.Property(_ => _.VehicleId)
                   .IsRequired();
            builder.Property(_ => _.VehicleName)
                   .IsRequired();
            builder.Property(_ => _.OrganizationId)
                .IsRequired();
            builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

            //Set up the relationship with org
            builder
                .HasOne(s => s.Organization)
                .WithMany(o => o.Vehicles)
                .HasForeignKey(s => s.OrganizationId)
                .HasPrincipalKey(r => r.OrganizationId);
        }
    }
}
