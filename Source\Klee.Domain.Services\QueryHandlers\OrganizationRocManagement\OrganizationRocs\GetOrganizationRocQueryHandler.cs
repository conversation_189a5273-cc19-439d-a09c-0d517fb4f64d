﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Darker;
using System.Threading.Tasks;
using System.Threading;
using System;

namespace Klee.Domain.Services.QueryHandlers.OrganizationRocManagement.OrganizationRocs;

public sealed class GetOrganizationRocQueryHandler
    : QueryHandlerAsync<GetOrganizationRocQuery, Roc>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationRocQueryHandler(IRocSrpRepository rocSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.RocSrpRepository = rocSrpRepository;
        this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<Roc> ExecuteAsync(GetOrganizationRocQuery query,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Init
        string rocId = query.RocId;

        // Get Roc
        Roc roc = await this.RocSrpRepository.FindAsync(_ => _.RocId == rocId &&
                                                                         _.EntityPartitionKey == rocId, query);

        //Check if organization of logged in user is the same as Roc organization
   
        string userOrganizationId = await this.UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);
        if (userOrganizationId != roc.OrganizationId)
        {
            throw new UnauthorizedAccessException("You do not have access to this Roc.");
        }
        
        return roc;
    }
    #endregion
}