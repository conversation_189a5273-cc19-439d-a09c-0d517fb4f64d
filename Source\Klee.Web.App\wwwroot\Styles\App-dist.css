/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-200: oklch(92.4% 0.12 95.746);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-teal-50: oklch(98.4% 0.014 180.72);
    --color-teal-100: oklch(95.3% 0.051 180.801);
    --color-teal-200: oklch(91% 0.096 180.426);
    --color-teal-300: oklch(85.5% 0.138 181.071);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-teal-600: oklch(60% 0.118 184.704);
    --color-teal-700: oklch(51.1% 0.096 186.391);
    --color-teal-800: oklch(43.7% 0.078 188.216);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-wider: 0.05em;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none !important;
  }
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border-width: 0 !important;
  }
  .absolute {
    position: absolute !important;
  }
  .fixed {
    position: fixed !important;
  }
  .relative {
    position: relative !important;
  }
  .static {
    position: static !important;
  }
  .-top-1 {
    top: calc(var(--spacing) * -1) !important;
  }
  .-right-1 {
    right: calc(var(--spacing) * -1) !important;
  }
  .col-12 {
    grid-column: 12 !important;
  }
  .container {
    width: 100% !important;
    @media (width >= 40rem) {
      max-width: 40rem !important;
    }
    @media (width >= 48rem) {
      max-width: 48rem !important;
    }
    @media (width >= 64rem) {
      max-width: 64rem !important;
    }
    @media (width >= 80rem) {
      max-width: 80rem !important;
    }
    @media (width >= 96rem) {
      max-width: 96rem !important;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0) !important;
  }
  .mx-auto {
    margin-inline: auto !important;
  }
  .ms-4 {
    margin-inline-start: calc(var(--spacing) * 4) !important;
  }
  .me-1 {
    margin-inline-end: calc(var(--spacing) * 1) !important;
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1) !important;
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2) !important;
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3) !important;
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4) !important;
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5) !important;
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6) !important;
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8) !important;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1) !important;
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2) !important;
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1) !important;
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2) !important;
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3) !important;
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4) !important;
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5) !important;
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6) !important;
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8) !important;
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1) !important;
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2) !important;
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3) !important;
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4) !important;
  }
  .ml-7 {
    margin-left: calc(var(--spacing) * 7) !important;
  }
  .block {
    display: block !important;
  }
  .flex {
    display: flex !important;
  }
  .grid {
    display: grid !important;
  }
  .hidden {
    display: none !important;
  }
  .inline-flex {
    display: inline-flex !important;
  }
  .h-2 {
    height: calc(var(--spacing) * 2) !important;
  }
  .h-3 {
    height: calc(var(--spacing) * 3) !important;
  }
  .h-4 {
    height: calc(var(--spacing) * 4) !important;
  }
  .h-5 {
    height: calc(var(--spacing) * 5) !important;
  }
  .h-6 {
    height: calc(var(--spacing) * 6) !important;
  }
  .h-8 {
    height: calc(var(--spacing) * 8) !important;
  }
  .h-16 {
    height: calc(var(--spacing) * 16) !important;
  }
  .h-20 {
    height: calc(var(--spacing) * 20) !important;
  }
  .h-full {
    height: 100% !important;
  }
  .h-screen {
    height: 100vh !important;
  }
  .max-h-\[80vh\] {
    max-height: 80vh !important;
  }
  .w-2 {
    width: calc(var(--spacing) * 2) !important;
  }
  .w-3 {
    width: calc(var(--spacing) * 3) !important;
  }
  .w-4 {
    width: calc(var(--spacing) * 4) !important;
  }
  .w-5 {
    width: calc(var(--spacing) * 5) !important;
  }
  .w-8 {
    width: calc(var(--spacing) * 8) !important;
  }
  .w-16 {
    width: calc(var(--spacing) * 16) !important;
  }
  .w-20 {
    width: calc(var(--spacing) * 20) !important;
  }
  .w-auto {
    width: auto !important;
  }
  .w-full {
    width: 100% !important;
  }
  .w-px {
    width: 1px !important;
  }
  .min-w-full {
    min-width: 100% !important;
  }
  .flex-1 {
    flex: 1 !important;
  }
  .flex-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-grow-1 {
    flex-grow: 1 !important;
  }
  .cursor-pointer {
    cursor: pointer !important;
  }
  .list-disc {
    list-style-type: disc !important;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .flex-col {
    flex-direction: column !important;
  }
  .flex-wrap {
    flex-wrap: wrap !important;
  }
  .items-center {
    align-items: center !important;
  }
  .justify-between {
    justify-content: space-between !important;
  }
  .justify-center {
    justify-content: center !important;
  }
  .justify-end {
    justify-content: flex-end !important;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1) !important;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2) !important;
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3) !important;
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4) !important;
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6) !important;
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8) !important;
  }
  .gap-12 {
    gap: calc(var(--spacing) * 12) !important;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0 !important;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse)) !important;
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse))) !important;
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0 !important;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse)) !important;
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse))) !important;
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0 !important;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse)) !important;
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse))) !important;
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0 !important;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse)) !important;
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse))) !important;
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0 !important;
      border-bottom-style: var(--tw-border-style) !important;
      border-top-style: var(--tw-border-style) !important;
      border-top-width: calc(1px * var(--tw-divide-y-reverse)) !important;
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse))) !important;
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200) !important;
    }
  }
  .overflow-auto {
    overflow: auto !important;
  }
  .overflow-x-auto {
    overflow-x: auto !important;
  }
  .overflow-y-auto {
    overflow-y: auto !important;
  }
  .rounded {
    border-radius: 0.25rem !important;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px) !important;
  }
  .rounded-lg {
    border-radius: var(--radius-lg) !important;
  }
  .rounded-md {
    border-radius: var(--radius-md) !important;
  }
  .border {
    border-style: var(--tw-border-style) !important;
    border-width: 1px !important;
  }
  .border-0 {
    border-style: var(--tw-border-style) !important;
    border-width: 0px !important;
  }
  .border-t {
    border-top-style: var(--tw-border-style) !important;
    border-top-width: 1px !important;
  }
  .border-t-2 {
    border-top-style: var(--tw-border-style) !important;
    border-top-width: 2px !important;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style) !important;
    border-bottom-width: 1px !important;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style) !important;
    border-bottom-width: 2px !important;
  }
  .border-amber-200 {
    border-color: var(--color-amber-200) !important;
  }
  .border-blue-200 {
    border-color: var(--color-blue-200) !important;
  }
  .border-gray-200 {
    border-color: var(--color-gray-200) !important;
  }
  .border-gray-300 {
    border-color: var(--color-gray-300) !important;
  }
  .border-green-200 {
    border-color: var(--color-green-200) !important;
  }
  .border-red-200 {
    border-color: var(--color-red-200) !important;
  }
  .border-red-600 {
    border-color: var(--color-red-600) !important;
  }
  .border-teal-200 {
    border-color: var(--color-teal-200) !important;
  }
  .border-teal-300 {
    border-color: var(--color-teal-300) !important;
  }
  .border-teal-700 {
    border-color: var(--color-teal-700) !important;
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100) !important;
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100) !important;
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50) !important;
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100) !important;
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200) !important;
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300) !important;
  }
  .bg-green-100 {
    background-color: var(--color-green-100) !important;
  }
  .bg-red-50 {
    background-color: var(--color-red-50) !important;
  }
  .bg-red-100 {
    background-color: var(--color-red-100) !important;
  }
  .bg-red-500 {
    background-color: var(--color-red-500) !important;
  }
  .bg-red-600 {
    background-color: var(--color-red-600) !important;
  }
  .bg-teal-50 {
    background-color: var(--color-teal-50) !important;
  }
  .bg-teal-100 {
    background-color: var(--color-teal-100) !important;
  }
  .bg-teal-500 {
    background-color: var(--color-teal-500) !important;
  }
  .bg-teal-700 {
    background-color: var(--color-teal-700) !important;
  }
  .bg-transparent {
    background-color: transparent !important;
  }
  .bg-white {
    background-color: var(--color-white) !important;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0) !important;
  }
  .p-4 {
    padding: calc(var(--spacing) * 4) !important;
  }
  .p-6 {
    padding: calc(var(--spacing) * 6) !important;
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2) !important;
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5) !important;
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3) !important;
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4) !important;
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6) !important;
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8) !important;
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5) !important;
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1) !important;
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3) !important;
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4) !important;
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6) !important;
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8) !important;
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12) !important;
  }
  .pe-2 {
    padding-inline-end: calc(var(--spacing) * 2) !important;
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2) !important;
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3) !important;
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4) !important;
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2) !important;
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3) !important;
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8) !important;
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5) !important;
  }
  .text-center {
    text-align: center !important;
  }
  .text-end {
    text-align: end !important;
  }
  .text-left {
    text-align: left !important;
  }
  .font-mono {
    font-family: var(--font-mono) !important;
  }
  .text-2xl {
    font-size: var(--text-2xl) !important;
    line-height: var(--tw-leading, var(--text-2xl--line-height)) !important;
  }
  .text-3xl {
    font-size: var(--text-3xl) !important;
    line-height: var(--tw-leading, var(--text-3xl--line-height)) !important;
  }
  .text-4xl {
    font-size: var(--text-4xl) !important;
    line-height: var(--tw-leading, var(--text-4xl--line-height)) !important;
  }
  .text-base {
    font-size: var(--text-base) !important;
    line-height: var(--tw-leading, var(--text-base--line-height)) !important;
  }
  .text-lg {
    font-size: var(--text-lg) !important;
    line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  }
  .text-sm {
    font-size: var(--text-sm) !important;
    line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  }
  .text-xl {
    font-size: var(--text-xl) !important;
    line-height: var(--tw-leading, var(--text-xl--line-height)) !important;
  }
  .text-xs {
    font-size: var(--text-xs) !important;
    line-height: var(--tw-leading, var(--text-xs--line-height)) !important;
  }
  .leading-none {
    --tw-leading: 1 !important;
    line-height: 1 !important;
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed) !important;
    line-height: var(--leading-relaxed) !important;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold) !important;
    font-weight: var(--font-weight-bold) !important;
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium) !important;
    font-weight: var(--font-weight-medium) !important;
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold) !important;
    font-weight: var(--font-weight-semibold) !important;
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight) !important;
    letter-spacing: var(--tracking-tight) !important;
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider) !important;
    letter-spacing: var(--tracking-wider) !important;
  }
  .whitespace-nowrap {
    white-space: nowrap !important;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap !important;
  }
  .text-amber-800 {
    color: var(--color-amber-800) !important;
  }
  .text-blue-600 {
    color: var(--color-blue-600) !important;
  }
  .text-blue-800 {
    color: var(--color-blue-800) !important;
  }
  .text-gray-400 {
    color: var(--color-gray-400) !important;
  }
  .text-gray-500 {
    color: var(--color-gray-500) !important;
  }
  .text-gray-600 {
    color: var(--color-gray-600) !important;
  }
  .text-gray-700 {
    color: var(--color-gray-700) !important;
  }
  .text-gray-800 {
    color: var(--color-gray-800) !important;
  }
  .text-gray-900 {
    color: var(--color-gray-900) !important;
  }
  .text-green-800 {
    color: var(--color-green-800) !important;
  }
  .text-red-400 {
    color: var(--color-red-400) !important;
  }
  .text-red-500 {
    color: var(--color-red-500) !important;
  }
  .text-red-600 {
    color: var(--color-red-600) !important;
  }
  .text-red-700 {
    color: var(--color-red-700) !important;
  }
  .text-red-800 {
    color: var(--color-red-800) !important;
  }
  .text-teal-500 {
    color: var(--color-teal-500) !important;
  }
  .text-teal-600 {
    color: var(--color-teal-600) !important;
  }
  .text-teal-700 {
    color: var(--color-teal-700) !important;
  }
  .text-teal-800 {
    color: var(--color-teal-800) !important;
  }
  .text-white {
    color: var(--color-white) !important;
  }
  .text-yellow-400 {
    color: var(--color-yellow-400) !important;
  }
  .uppercase {
    text-transform: uppercase !important;
  }
  .italic {
    font-style: italic !important;
  }
  .underline-offset-4 {
    text-underline-offset: 4px !important;
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .transition-all {
    transition-property: all !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }
  .duration-200 {
    --tw-duration: 200ms !important;
    transition-duration: 200ms !important;
  }
  .hover\:border-gray-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-400) !important;
      }
    }
  }
  .hover\:border-red-700 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-red-700) !important;
      }
    }
  }
  .hover\:border-teal-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-teal-300) !important;
      }
    }
  }
  .hover\:border-teal-600 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-teal-600) !important;
      }
    }
  }
  .hover\:border-teal-700 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-teal-700) !important;
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50) !important;
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50) !important;
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700) !important;
      }
    }
  }
  .hover\:bg-teal-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-100) !important;
      }
    }
  }
  .hover\:bg-teal-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-600) !important;
      }
    }
  }
  .hover\:text-red-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-700) !important;
      }
    }
  }
  .hover\:text-teal-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-teal-600) !important;
      }
    }
  }
  .hover\:text-teal-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-teal-700) !important;
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline !important;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1)) !important;
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
      }
    }
  }
  .focus\:border-teal-500 {
    &:focus {
      border-color: var(--color-teal-500) !important;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
    }
  }
  .focus\:ring-teal-200 {
    &:focus {
      --tw-ring-color: var(--color-teal-200) !important;
    }
  }
  .focus\:ring-teal-500 {
    &:focus {
      --tw-ring-color: var(--color-teal-500) !important;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row !important;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center !important;
    }
  }
  .sm\:gap-4 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 4) !important;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2 !important;
    }
  }
  .md\:ml-4 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 4) !important;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex !important;
    }
  }
  .md\:inline-block {
    @media (width >= 48rem) {
      display: inline-block !important;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row !important;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center !important;
    }
  }
  .lg\:grid-cols-1 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
  }
}
*, ::after, ::before, ::backdrop, ::file-selector-button {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0 solid;
}
html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
  font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
  font-feature-settings: var(--default-font-feature-settings, normal);
  font-variation-settings: var(--default-font-variation-settings, normal);
  -webkit-tap-highlight-color: transparent;
}
hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}
a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}
b, strong {
  font-weight: bolder;
}
code, kbd, samp, pre {
  font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
  font-feature-settings: var(--default-mono-font-feature-settings, normal);
  font-variation-settings: var(--default-mono-font-variation-settings, normal);
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}
:-moz-focusring {
  outline: auto;
}
progress {
  vertical-align: baseline;
}
summary {
  display: list-item;
}
ol, ul, menu {
  list-style: none;
}
img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}
img, video {
  max-width: 100%;
  height: auto;
}
button, input, select, optgroup, textarea, ::file-selector-button {
  font: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  letter-spacing: inherit;
  color: inherit;
  border-radius: 0;
  background-color: transparent;
  opacity: 1;
}
:where(select:is([multiple], [size])) optgroup {
  font-weight: bolder;
}
:where(select:is([multiple], [size])) optgroup option {
  padding-inline-start: 20px;
}
::file-selector-button {
  margin-inline-end: 4px;
}
::placeholder {
  opacity: 1;
}
@supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
  ::placeholder {
    color: currentcolor;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, currentcolor 50%, transparent);
    }
  }
}
textarea {
  resize: vertical;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-date-and-time-value {
  min-height: 1lh;
  text-align: inherit;
}
::-webkit-datetime-edit {
  display: inline-flex;
}
::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}
::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
  padding-block: 0;
}
:-moz-ui-invalid {
  box-shadow: none;
}
button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
  appearance: button;
}
::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}
[hidden]:where(:not([hidden='until-found'])) {
  display: none !important;
}
@font-face {
  font-family: 'Sofia Pro';
  src: url('../Fonts/SofiaPro Regular Az.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Sofia Pro';
  src: url('../Fonts/SofiaPro Medium Az.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Sofia Pro';
  src: url('../Fonts/SofiaPro Bold Az.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
html {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif;
}
body {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
  background-color: #5eead4 !important;
}
.voyage-steps .ant-steps-item-finish .ant-steps-item-icon {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.voyage-steps .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white !important;
}
.voyage-steps .ant-steps-item-process .ant-steps-item-icon {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.voyage-steps .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon {
  color: white !important;
}
.voyage-steps .ant-steps-item-finish .ant-steps-item-title {
  color: #0f766e !important;
}
.voyage-steps .ant-steps-item-process .ant-steps-item-title {
  color: #0f766e !important;
}
.voyage-steps .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: #0f766e !important;
}
.ant-pagination {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-pagination .ant-pagination-item {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
}
.ant-pagination .ant-pagination-item a {
  color: #6b7280 !important;
  font-weight: 500 !important;
}
.ant-pagination .ant-pagination-item:hover {
  border-color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-pagination .ant-pagination-item:hover a {
  color: white !important;
}
.ant-pagination .ant-pagination-item-active {
  border-color: #0f766e !important;
  background-color: #0f766e !important;
}
.ant-pagination .ant-pagination-item-active a {
  color: #ffffff !important;
  font-weight: 600 !important;
}
.ant-pagination .ant-pagination-item-active:hover {
  border-color: #0d9488 !important;
  background-color: #0d9488 !important;
}
.ant-pagination .ant-pagination-prev, .ant-pagination .ant-pagination-next {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
}
.ant-pagination .ant-pagination-prev:hover, .ant-pagination .ant-pagination-next:hover {
  border-color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-pagination .ant-pagination-prev .ant-pagination-item-link, .ant-pagination .ant-pagination-next .ant-pagination-item-link {
  color: #6b7280 !important;
}
.ant-pagination .ant-pagination-prev:hover .ant-pagination-item-link, .ant-pagination .ant-pagination-next:hover .ant-pagination-item-link {
  color: #0f766e !important;
}
.ant-pagination .ant-pagination-disabled {
  border-color: #e5e7eb !important;
  background-color: #f9fafb !important;
}
.ant-pagination .ant-pagination-disabled .ant-pagination-item-link {
  color: #d1d5db !important;
}
.ant-pagination .ant-pagination-options .ant-select-selector {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
}
.ant-pagination .ant-pagination-options .ant-select-selector:hover {
  border-color: #0f766e !important;
}
.ant-pagination .ant-pagination-options .ant-select-focused .ant-select-selector {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
}
.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input:hover {
  border-color: #0f766e !important;
}
.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input:focus {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-table-thead > tr > th {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  background-color: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
}
.ant-table-thead > tr > th:hover {
  background-color: #f0fdfa !important;
}
.ant-table-thead > tr > th.ant-table-column-has-sorters {
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}
.ant-table-thead > tr > th.ant-table-column-has-sorters:hover {
  background-color: #f0fdfa !important;
}
.ant-table-thead > tr > th.ant-table-column-has-sorters:hover .ant-table-column-title {
  color: #0f766e !important;
}
.ant-table-column-sorter {
  color: #d1d5db !important;
  transition: color 0.2s ease !important;
}
.ant-table-column-sorter:hover {
  color: #0f766e !important;
}
.ant-table-column-sorter-up.active, .ant-table-column-sorter-down.active {
  color: #0f766e !important;
}
.ant-table-column-sorter-inner {
  color: inherit !important;
}
.ant-table-thead > tr > th.ant-table-column-sort {
  background-color: #f0fdfa !important;
}
.ant-table-thead > tr > th.ant-table-column-sort .ant-table-column-title {
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-table-filter-trigger {
  color: #6b7280 !important;
  transition: color 0.2s ease !important;
}
.ant-table-filter-trigger:hover {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-table-filter-trigger.active {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-table-filter-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
  padding: 1rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-input {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
}
.ant-table-filter-dropdown .ant-input:hover {
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-input:focus, .ant-table-filter-dropdown .ant-input-focused {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-table-filter-dropdown .ant-checkbox-wrapper {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 0.25rem 0 !important;
}
.ant-table-filter-dropdown .ant-checkbox-wrapper:hover {
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-checkbox-checked:after {
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-table-filter-dropdown-btns {
  border-top: 1px solid #e5e7eb !important;
  padding-top: 0.75rem !important;
  margin-top: 0.75rem !important;
  display: flex !important;
  justify-content: space-between !important;
  gap: 0.5rem !important;
}
.ant-table-filter-dropdown .ant-btn {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}
.ant-table-filter-dropdown .ant-btn-primary {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
  color: #ffffff !important;
}
.ant-table-filter-dropdown .ant-btn-primary:hover {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
}
.ant-table-filter-dropdown .ant-btn-primary:focus {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-table-filter-dropdown .ant-btn-link {
  color: #6b7280 !important;
  border: none !important;
  background: transparent !important;
}
.ant-table-filter-dropdown .ant-btn-link:hover {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-table-filter-dropdown .ant-dropdown-menu {
  background-color: #ffffff !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
.ant-table-filter-dropdown .ant-dropdown-menu-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-dropdown-menu-item:hover {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-dropdown-menu-item-selected {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-table-tbody > tr:hover > td {
  background-color: #f0fdfa !important;
}
.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #f0fdfa !important;
  border-color: #0f766e !important;
}
.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #e6fffa !important;
}
.ant-table-thead > tr > th.ant-table-column-has-filters.ant-table-filter-column {
  background-color: #f0fdfa !important;
}
.ant-table-thead > tr > th.ant-table-column-has-filters.ant-table-filter-column .ant-table-column-title {
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-table-filter-dropdown-container {
  z-index: 1050 !important;
}
.ant-table {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-tbody > tr > td {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-bottom: 1px solid #f3f4f6 !important;
}
.ant-table-placeholder .ant-empty-description {
  color: #6b7280 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-spin-dot-item {
  background-color: #0f766e !important;
}
.ant-table-expanded-row > td {
  padding: 0 !important;
  background-color: #f3f4f6 !important;
  border-bottom: 1px solid #e2e8f0 !important;
}
.ant-table-expanded-row .ant-table-cell {
  border-bottom: none !important;
}
.ant-table-expanded-row .ant-table-cell > div {
  margin: 0 !important;
}
.ant-table-expanded-row .flex.flex-wrap span {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
.ant-table-expanded-row .flex.flex-wrap span:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
}
.ant-table-expanded-row .text-center {
  background-color: #ffffff !important;
  border-radius: 0.5rem !important;
  border: 1px dashed #d1d5db !important;
  margin: 0.5rem 0 !important;
}
.ant-table-filter-dropdown .ant-select {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-selector {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  border-radius: 0.375rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-selector:hover {
  border-color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-select.ant-select-focused .ant-select-selector, .ant-table-filter-dropdown .ant-select.ant-select-open .ant-select-selector {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-selection-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 500 !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-selection-placeholder {
  color: #9ca3af !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-arrow {
  color: #6b7280 !important;
}
.ant-table-filter-dropdown .ant-select:hover .ant-select-arrow {
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-select.ant-select-focused .ant-select-arrow, .ant-table-filter-dropdown .ant-select.ant-select-open .ant-select-arrow {
  color: #0f766e !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.25rem !important;
  margin: 0.125rem !important;
}
.ant-checkbox-wrapper {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-checkbox-inner {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  border-radius: 0.25rem !important;
}
.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #0f766e !important;
}
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.ant-checkbox-checked:after {
  border-color: #0f766e !important;
}
.ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}
.ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #0f766e !important;
}
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-picker {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
}
.ant-picker:hover {
  border-color: #0f766e !important;
}
.ant-picker-focused {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-picker-input > input {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #374151 !important;
}
.ant-picker-input > input::placeholder {
  color: #9ca3af !important;
}
.ant-picker-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: #0f766e !important;
  color: #ffffff !important;
}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell:hover .ant-picker-time-panel-cell-inner {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-input {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
  color: #374151 !important;
}
.ant-input:hover {
  border-color: #0f766e !important;
}
.ant-input:focus, .ant-input-focused {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input::placeholder {
  color: #9ca3af !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-input-number {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
}
.ant-input-number:hover {
  border-color: #0f766e !important;
}
.ant-input-number-focused {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input-number-input {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #374151 !important;
}
.ant-input-number-handler-wrap {
  border-left-color: #d1d5db !important;
}
.ant-input-number-handler {
  border-color: #d1d5db !important;
  color: #6b7280 !important;
}
.ant-input-number-handler:hover {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-select {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-selector {
  border-color: #d1d5db !important;
  background-color: #ffffff !important;
  border-radius: 0.375rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-selector:hover {
  border-color: #0f766e !important;
}
.ant-select.ant-select-focused .ant-select-selector, .ant-select.ant-select-open .ant-select-selector {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-select .ant-select-selection-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 500 !important;
}
.ant-select .ant-select-selection-placeholder {
  color: #9ca3af !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-arrow {
  color: #6b7280 !important;
}
.ant-select:hover .ant-select-arrow {
  color: #0f766e !important;
}
.ant-select.ant-select-focused .ant-select-arrow, .ant-select.ant-select-open .ant-select-arrow {
  color: #0f766e !important;
}
.ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.25rem !important;
  margin: 0.125rem !important;
}
.ant-select-item:hover {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #0f766e !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled):hover {
  background-color: #0d9488 !important;
  color: #ffffff !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item:hover {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-selected {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-select-item-option-selected {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
  font-weight: 600 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-selected:not(.ant-select-item-option-disabled):hover {
  background-color: #e6fffa !important;
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-active {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-select.ant-select-disabled .ant-select-selector {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
}
.ant-table-filter-dropdown .ant-select.ant-select-disabled .ant-select-arrow {
  color: #d1d5db !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-clear {
  color: #6b7280 !important;
  background-color: #ffffff !important;
  border-radius: 50% !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-clear:hover {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
}
.ant-select-dropdown.ant-table-filter-dropdown-menu {
  z-index: 1060 !important;
}
.ant-table-filter-dropdown .ant-select .ant-select-selection-search-input {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-select-loading .ant-select-arrow .anticon {
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item {
  background-color: #f0fdfa !important;
  border: 1px solid #14b8a6 !important;
  color: #0f766e !important;
  border-radius: 0.25rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item-remove {
  color: #0f766e !important;
}
.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item-remove:hover {
  color: #dc2626 !important;
  background-color: #fef2f2 !important;
}
.ant-table-wrapper {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}
.ant-table {
  min-width: 800px !important;
}
@media (max-width: 768px) {
  .ant-table-wrapper {
    margin: 0 -1rem !important;
    padding: 0 1rem !important;
  }
  .ant-table-thead > tr > th {
    white-space: nowrap !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
  .ant-table-tbody > tr > td {
    white-space: nowrap !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
  .ant-table-filter-dropdown {
    max-width: 280px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
  }
}
.ant-table-fixed-left, .ant-table-fixed-right {
  z-index: 2 !important;
}
.ant-table-fixed-left .ant-table-thead > tr > th, .ant-table-fixed-right .ant-table-thead > tr > th {
  background-color: #f9fafb !important;
}
.ant-table-fixed-left .ant-table-tbody > tr > td, .ant-table-fixed-right .ant-table-tbody > tr > td {
  background-color: #ffffff !important;
}
.ant-table-wrapper::-webkit-scrollbar {
  height: 8px !important;
}
.ant-table-wrapper::-webkit-scrollbar-track {
  background-color: #f3f4f6 !important;
  border-radius: 4px !important;
}
.ant-table-wrapper::-webkit-scrollbar-thumb {
  background-color: #d1d5db !important;
  border-radius: 4px !important;
}
.ant-table-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #0f766e !important;
}
.ant-input {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-input::placeholder {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-input-number {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-input-number .ant-input-number-input {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #374151 !important;
}
.ant-input-number .ant-input-number-handler {
  border-color: #d1d5db !important;
  color: #6b7280 !important;
  transition: all 0.2s ease !important;
}
.ant-input-number .ant-input-number-handler:hover {
  color: #0f766e !important;
  background-color: #f0fdfa !important;
  border-color: #0f766e !important;
}
.ant-select {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-selector {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-selection-item {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 500 !important;
}
.ant-select .ant-select-selection-placeholder {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select .ant-select-arrow {
  color: #6b7280 !important;
}
.ant-select:hover .ant-select-arrow, .ant-select.ant-select-focused .ant-select-arrow, .ant-select.ant-select-open .ant-select-arrow {
  color: #0f766e !important;
}
.ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-select-dropdown .ant-select-item {
  color: #374151 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.25rem !important;
  margin: 0.125rem !important;
}
.ant-select-dropdown .ant-select-item:hover {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
  font-weight: 600 !important;
}
.ant-select-dropdown .ant-select-item-option-active {
  background-color: #f0fdfa !important;
  color: #0f766e !important;
}
.ant-form-item-has-error .ant-input, .ant-form-item-has-error .ant-input-number, .ant-form-item-has-error .ant-select .ant-select-selector {
  border-color: #dc2626 !important;
}
.ant-form-item-has-error .ant-input:focus, .ant-form-item-has-error .ant-input-focused, .ant-form-item-has-error .ant-input-number:focus-within, .ant-form-item-has-error .ant-input-number-focused, .ant-form-item-has-error .ant-select.ant-select-focused .ant-select-selector {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1) !important;
}
.ant-input:disabled, .ant-input-number-disabled, .ant-select-disabled .ant-select-selector {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
}
.ant-form-item-label > label {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #374151 !important;
  font-weight: 600 !important;
}
.ant-form-item-label > label.ant-form-item-required::before {
  color: #dc2626 !important;
}
.ant-btn {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.ant-btn-primary {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
  color: #ffffff !important;
}
.ant-btn-primary:hover {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
}
.ant-btn-primary:focus {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-btn-default {
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
  color: #374151 !important;
}
.ant-btn-default:hover {
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
  color: #374151 !important;
}
.validation-message {
  color: #dc2626 !important;
  font-size: 0.875rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  margin-top: 0.25rem !important;
  display: block !important;
}
.ant-form-item {
  margin-bottom: 1.5rem !important;
}
.ant-form-item-label {
  padding-bottom: 0.5rem !important;
}
.ant-btn-loading {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}
.ant-modal .ant-modal-header {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-bottom: 1px solid #e5e7eb !important;
}
.ant-modal .ant-modal-title {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 600 !important;
  color: #374151 !important;
}
.ant-modal .ant-modal-body {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #6b7280 !important;
}
.ant-modal .ant-modal-footer {
  border-top: 1px solid #e5e7eb !important;
  padding: 1rem 1.5rem !important;
}
.ant-modal-confirm .ant-modal-confirm-title {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 600 !important;
  color: #374151 !important;
}
.ant-modal-confirm .ant-modal-confirm-content {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #6b7280 !important;
  margin-top: 0.5rem !important;
}
.ant-modal-confirm .ant-modal-confirm-content p {
  margin-bottom: 0.5rem !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-modal-confirm .ant-modal-confirm-content strong {
  color: #374151 !important;
  font-weight: 600 !important;
}
.ant-modal .ant-btn-primary.ant-btn-dangerous {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
  color: #ffffff !important;
}
.ant-modal .ant-btn-primary.ant-btn-dangerous:hover {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
}
.ant-modal .ant-btn-primary.ant-btn-dangerous:focus {
  background-color: #0d9488 !important;
  border-color: #0d9488 !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input.w-full.border.border-gray-300:focus {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
  outline: none !important;
}
.ant-input.w-full.border.border-gray-300:hover {
  border-color: #0f766e !important;
}
.ant-collapse {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  background-color: transparent !important;
  border: none !important;
}
.ant-collapse > .ant-collapse-item {
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  margin-bottom: 0.5rem !important;
  background-color: #ffffff !important;
}
.ant-collapse > .ant-collapse-item:last-child {
  margin-bottom: 0 !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 1rem 1.5rem !important;
  background-color: #ffffff !important;
  border-radius: 0.5rem !important;
  color: #374151 !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header:hover {
  background-color: #f3f4f6 !important;
  color: #0f766e !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  transition: color 0.2s ease !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header:hover .ant-collapse-arrow {
  color: #0f766e !important;
}
.ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-header {
  background-color: #f3f4f6 !important;
  color: #0f766e !important;
  border-bottom: 1px solid #e5e7eb !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-header .ant-collapse-arrow {
  color: #0f766e !important;
}
.ant-collapse-content {
  background-color: #ffffff !important;
  border-top: none !important;
  border-bottom-left-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}
.ant-collapse-content > .ant-collapse-content-box {
  padding: 0 !important;
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  background-color: #ffffff !important;
}
.ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-content {
  background-color: #ffffff !important;
  border-top: 1px solid #e5e7eb !important;
}
.ant-collapse-content .voyage-details-content {
  background-color: #ffffff !important;
}
.voyage-section-header {
  border-bottom: 2px solid #f0fdfa !important;
  padding-bottom: 0.75rem !important;
  margin-bottom: 1rem !important;
}
.voyage-data-row {
  border-bottom: 1px solid #f9fafb !important;
  padding: 0.75rem 0 !important;
}
.voyage-data-row:last-child {
  border-bottom: none !important;
}
.ant-collapse.ant-collapse-ghost {
  background-color: transparent !important;
}
.ant-collapse.ant-collapse-ghost > .ant-collapse-item {
  border: 1px solid #e5e7eb !important;
  background-color: #ffffff !important;
}
.ant-collapse.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content {
  background-color: #ffffff !important;
}
.ant-collapse > .ant-collapse-item:hover {
  background-color: #ffffff !important;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header:hover {
  background-color: #ffffff !important;
}
.ant-card {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-radius: 0.5rem !important;
  border: 1px solid #e5e7eb !important;
  background-color: #ffffff !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease !important;
}
.ant-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
}
.ant-card-head {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  border-bottom: 1px solid #f3f4f6 !important;
  padding: 1rem 1.5rem !important;
  background-color: #ffffff !important;
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
}
.ant-card-head-title {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 600 !important;
  color: #374151 !important;
}
.ant-card-body {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  padding: 1.5rem !important;
  color: #6b7280 !important;
}
.ant-card-actions {
  border-top: 1px solid #f3f4f6 !important;
  background-color: #f9fafb !important;
  border-bottom-left-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}
.ant-card-actions > li {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}
.ant-card-actions > li > span {
  color: #6b7280 !important;
  transition: color 0.2s ease !important;
}
.ant-card-actions > li > span:hover {
  color: #0f766e !important;
}
.ant-card-meta-title {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  font-weight: 600 !important;
  color: #374151 !important;
}
.ant-card-meta-description {
  font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
  color: #6b7280 !important;
}
.ant-input-number.w-full.border.border-gray-300:focus-within, .ant-input-number.w-full.border.border-gray-300.ant-input-number-focused {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input-number.w-full.border.border-gray-300:hover {
  border-color: #0f766e !important;
}
.ant-input-number.w-full.border.border-gray-300.rounded-md.bg-white:focus-within {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input-number.w-full .ant-input-number-input:focus {
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}
.ant-input-number.w-full:has(.ant-input-number-input:focus) {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-select.w-full.border.border-gray-300.ant-select-focused .ant-select-selector, .ant-select.w-full.border.border-gray-300.ant-select-open .ant-select-selector {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-select.w-full.border.border-gray-300:hover .ant-select-selector {
  border-color: #0f766e !important;
}
.ant-input-number.w-full {
  width: 100% !important;
  display: block !important;
}
.ant-input-number.w-full .ant-input-number-input-wrap {
  width: 100% !important;
}
.ant-input-number[class*="w-full"][class*="border"][class*="border-gray-300"]:focus-within {
  border-color: #0f766e !important;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}
.ant-input:focus-visible, .ant-input-number:focus-visible, .ant-select:focus-visible .ant-select-selector {
  outline: 2px solid #0f766e !important;
  outline-offset: 2px !important;
}
.ant-input-number-handler:hover .ant-input-number-handler-up-inner, .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: #0f766e !important;
}
.ant-table-row-expand-icon:focus, .ant-table-row-expand-icon:hover, .ant-table-row-expand-icon:active {
  color: #0f766e !important;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
    }
  }
}
