﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.OperatorManagement.Operators;

public sealed class GetOperatorQueryHandler
    : QueryHandlerAsync<GetOperatorQuery, Operator>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOperatorQueryHandler(IOperatorSrpRepository operatorSrpRepository,
        IMemoryCache memoryCache)
    {
        this.OperatorSrpRepository = operatorSrpRepository;
        this.MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS

    public override async Task<Operator> ExecuteAsync(GetOperatorQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        // Init
        string operatorId = query.OperatorId;

        // Get Operator from cache
        var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetOperatorCacheId(operatorId), out Operator cachedOperator);

        // Init
        Operator operatorObj = cachedOperator;

        // Get Operator from DB (if needed)
        if (query.AllowCached == false  ||
            !isCached) {

            // Get Operator 
            operatorObj = await this.OperatorSrpRepository.FindAsync(_ => _.OperatorId == operatorId &&
                                                                     _.EntityPartitionKey == operatorId, query);
            // Cache Operator
            this.MemoryCache.Set(MemoryCacheIds.GetOperatorCacheId(operatorId),
                operatorObj, new MemoryCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                    Size = 1
                });
        }

        return operatorObj;
    }
    #endregion
}