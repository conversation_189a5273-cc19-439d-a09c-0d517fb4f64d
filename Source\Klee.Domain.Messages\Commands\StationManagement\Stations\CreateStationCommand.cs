﻿using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.StationManagement.Stations;

public class CreateStationCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; }

    public string StationId { get; }
    public string RocId { get; }
    public JoystickTypeIds JoystickTypeId { get; }

    public string StationName { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateStationCommand(SoftwareEnvironmentIds softwareEnvironmentId,
        string stationId,
        string rocId,
        JoystickTypeIds joystickTypeId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.SoftwareEnvironmentId = softwareEnvironmentId;
        this.StationId = stationId;
        this.RocId = rocId;
        this.JoystickTypeId = joystickTypeId;
    }
    #endregion
}