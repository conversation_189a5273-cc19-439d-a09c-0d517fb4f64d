using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Renoir.Application.Messages.Queries.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Domain.Messages.Queries.OrganizationManagement.Organizations
{
    public class GetOrganizationQuery
        : QueryBase<Organization>
    {
        #region PROPERTIES
        public string OrganizationId { get; }

        public bool AllowCached { get; set; } = false;
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationQuery(string organizationId,
                               IQueryContext context)
            : base(context)
        {
            this.OrganizationId = organizationId;
        }
        #endregion
    }
} 