﻿using System;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations.Helpers;
using Monet.Helpers;

namespace Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations;

public sealed class UpdateOrganizationContactCommandHandler
    : RequestHandlerAsync<UpdateOrganizationContactCommand> {
    #region PROPERTIES

    private IOrganizationSrpRepository OrganizationSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOrganizationContactCommandHandler(IOrganizationSrpRepository organizationSrpRepository,
        IMemoryCache memoryCache) {
        OrganizationSrpRepository = organizationSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationContactCommandValidator))]
    public override async Task<UpdateOrganizationContactCommand> HandleAsync(UpdateOrganizationContactCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        //Get org (if it exist) 
        if (await OrganizationSrpRepository.ExistsAsync(_ => _.OrganizationId == command.OrganizationId &&
                                                              _.EntityPartitionKey == command.OrganizationId,
                command)) {

            Organization organization = await OrganizationSrpRepository.FindAsync(_ => _.OrganizationId == command.OrganizationId &&
                                                                              _.EntityPartitionKey == command.OrganizationId,
                command);

            organization.Address = command.Address;
            organization.ContactEmail = command.Email;
            organization.ContactPhone = command.Phone;
            // Update
            await OrganizationSrpRepository.UpdateAsync(organization, command);

            // Set Result
            command.Result.EntityId = organization.EntityId;

            // Clear Caches
            MemoryCache.RemoveOrganization(command.OrganizationId);
        }
        else {
            throw new EntityNotFoundException(
                $"Organization with organization id '{command.OrganizationId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}