﻿using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators;
using EnumsNET;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Monet.Helpers;
using Renoir.Application.Validations.Helpers;
using Renoir.SoftwareEnvironments;
using Renoir.Srp.Portal.Web.Pages.Common;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;

namespace Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleCreate
{
    public class VehicleCreateDialogViewModel
        : ViewModelBase<VehicleCreateDialogViewModel>, IValidatableObject
    {
        #region FIELDS
        private string _softwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
        private SoftwareEnvironmentIds _softwareEnvironmentId = SoftwareEnvironmentIds.Prod;

        private string _vehicleTypeDisplayName = VehicleTypeIds.WaterVessel.GetDisplayName();
        private VehicleTypeIds _vehicleTypeId = VehicleTypeIds.WaterVessel;
        #endregion

        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableSoftwareEnvironmentDisplayNames { get; } = Enums.GetMembers<SoftwareEnvironmentIds>()
                                                                                              .Where(_ => _.Value != SoftwareEnvironmentIds.None)
                                                                                              .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                              .OrderBy(_ => _).ToList();
        public static IList<string> SelectableVehicleTypeDisplayNames { get; } = Enums.GetMembers<VehicleTypeIds>()
                                                                                      .Where(_ => _.Value != VehicleTypeIds.None)
                                                                                      .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                      .OrderBy(_ => _).ToList();
        #endregion

        #region PROPERTIES

        public IReadOnlyList<OrganizationNameListItem> OrganizationNames { get; set; } = new List<OrganizationNameListItem>();

        [Required]
        [Display(Name = "Software Environment")]
        public string SoftwareEnvironmentDisplayName
        {
            get => this._softwareEnvironmentDisplayName;
            set => this.SetSoftwareEnvironmentDisplayName(value);
        }

        [Required]
        [Display(Name = "Vehicle Type")]
        public string VehicleTypeDisplayName
        {
            get => this._vehicleTypeDisplayName;
            set => this.SetVehicleTypeDisplayName(value);
        }

        [Required]
        [Display(Name = "Vehicle Name")]
        public string VehicleName { get; set; } = "";

        [Display(Name = "ENI")]
        public string ENI { get; set; } = "";

        [Display(Name = "Hourly rate (Euro)")]
        public double HourlyRateInEuros { get; set; } = 0.0;

        [Required]
        [Display(Name = "Organization Name")]
        public string SelectedOrganizationId { get; set; } = "";
        #endregion

        #region PROPERTIES - CONVERTED
        public SoftwareEnvironmentIds SoftwareEnvironmentId => this._softwareEnvironmentId;
        public VehicleTypeIds VehicleTypeId => this._vehicleTypeId;
        #endregion

        #region CONSTRUCTORS
        public VehicleCreateDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.SoftwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
            this.VehicleTypeDisplayName = VehicleTypeIds.None.GetDisplayName();
            this.VehicleName = "";
            this.ENI = "";
            this.HourlyRateInEuros = 0.0;
            this.OrganizationNames = new List<OrganizationNameListItem>();
            this.SelectedOrganizationId = "";
        }

        private void SetSoftwareEnvironmentDisplayName(string softwareEnvironmentDisplayName)
        {
            // Init
            bool isSoftwareEnvironmentChanged = softwareEnvironmentDisplayName != this.SoftwareEnvironmentDisplayName;

            //
            if (Enums.TryParse(softwareEnvironmentDisplayName, true, out SoftwareEnvironmentIds enumValue, EnumFormat.DisplayName))
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = enumValue;
            }
            else
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = SoftwareEnvironmentIds.None;
            }

            // Handle changed software environment type
            if (isSoftwareEnvironmentChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }

        private void SetVehicleTypeDisplayName(string vehicleTypeDisplayName)
        {
            // Init
            bool isVehicleTypeChanged = vehicleTypeDisplayName != this.VehicleTypeDisplayName;

            //
            if (Enums.TryParse(vehicleTypeDisplayName, true, out VehicleTypeIds enumValue, EnumFormat.DisplayName))
            {
                this._vehicleTypeDisplayName = vehicleTypeDisplayName;
                this._vehicleTypeId = enumValue;
            }
            else
            {
                this._vehicleTypeDisplayName = vehicleTypeDisplayName;
                this._vehicleTypeId = VehicleTypeIds.None;
            }

            // Handle changed vehicle type
            if (isVehicleTypeChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }
        #endregion

        #region METHODS - VALIDATE
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateVehicleCommandValidator createVehicleCommandValidator = new CreateVehicleCommandValidator();
            createVehicleCommandValidator.Validate(this.NewCreateVehicleCommand())
                                         .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationNamesAsync()
        {
            // Load organizations
            IReadOnlyList<OrganizationNameListItem>? organizationNameList = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationNameListQuery(this.SrpQueryContext)
            {
                IsActive = false
            });
            
            this.OrganizationNames = organizationNameList
                .OrderBy(orgListItem => orgListItem.Name)
                .ToList();

            // Notify
            await this.InvokeStateHasChangedOnHostAsync();

            return true;
        }
        private CreateVehicleCommand NewCreateVehicleCommand()
        {
            
            return new CreateVehicleCommand(this.SoftwareEnvironmentId,
                                            this.VehicleTypeId,
                                            this.SelectedOrganizationId,
                                            this.SrpCommandContext)
                   {
                       VehicleName = this.VehicleName,
                       ENI = this.ENI,
                       HourlyRateInEuros = this.HourlyRateInEuros
            };
        }

        public async Task CreateVehicleAsync()
        {
            // Init
            CreateVehicleCommand createVehicleCommand = this.NewCreateVehicleCommand();

            // Create UserProfile
            await this.SrpCommandProcessor
                      .SendAsync(createVehicleCommand, false);
        }
        #endregion
    }
}