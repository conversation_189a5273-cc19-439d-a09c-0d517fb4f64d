using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;
using Monet.Helpers;
using Renoir.Application.Validations.Helpers;
using Renoir.SoftwareEnvironments;
using Renoir.Srp.Portal.Web.Pages.Common;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationCreate
{
    public class OrganizationCreateDialogViewModel
        : ViewModelBase<OrganizationCreateDialogViewModel>, IValidatableObject
    {
        #region FIELDS
        private string _softwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
        private SoftwareEnvironmentIds _softwareEnvironmentId = SoftwareEnvironmentIds.Prod;
        #endregion

        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableSoftwareEnvironmentDisplayNames { get; } = Enums.GetMembers<SoftwareEnvironmentIds>()
                                                                                             .Where(_ => _.Value != SoftwareEnvironmentIds.None)
                                                                                             .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                             .OrderBy(_ => _).ToList();
        #endregion

        #region PROPERTIES
        [Required]
        [Display(Name = "Software Environment")]
        public string SoftwareEnvironmentDisplayName
        {
            get => this._softwareEnvironmentDisplayName;
            set => this.SetSoftwareEnvironmentDisplayName(value);
        }

        [Required]
        [Display(Name = "Organization Name")]
        public string Name { get; set; } = "";

        [Display(Name = "Code")]
        [MaxLength(10)]
        public string Code { get; set; } = "";

        [Display(Name = "Description")]
        public string Description { get; set; } = "";

        [Display(Name = "Contact Email")]
        [EmailAddress]
        public string ContactEmail { get; set; } = "";

        [Display(Name = "Contact Phone")]
        [Phone]
        public string ContactPhone { get; set; } = "";

        [Display(Name = "Address")]
        public string Address { get; set; } = "";
        #endregion

        #region PROPERTIES - CONVERTED
        public SoftwareEnvironmentIds SoftwareEnvironmentId => this._softwareEnvironmentId;
        #endregion

        #region CONSTRUCTORS
        public OrganizationCreateDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        { }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.SoftwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
            this.Name = "";
            this.Code = "";
            this.Description = "";
            this.ContactEmail = "";
            this.ContactPhone = "";
            this.Address = "";
        }

        private void SetSoftwareEnvironmentDisplayName(string softwareEnvironmentDisplayName)
        {
            // Init
            bool isSoftwareEnvironmentChanged = softwareEnvironmentDisplayName != this.SoftwareEnvironmentDisplayName;

            //
            if (Enums.TryParse(softwareEnvironmentDisplayName, true, out SoftwareEnvironmentIds enumValue, EnumFormat.DisplayName))
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = enumValue;
            }
            else
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = SoftwareEnvironmentIds.None;
            }

            // Handle changed software environment type
            if (isSoftwareEnvironmentChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }
        #endregion

        #region METHODS - VALIDATE
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateOrganizationCommandValidator createOrganizationCommandValidator = new CreateOrganizationCommandValidator();
            createOrganizationCommandValidator.Validate(this.NewCreateOrganizationCommand())
                                             .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        private CreateOrganizationCommand NewCreateOrganizationCommand()
        {
            return new CreateOrganizationCommand(this.SoftwareEnvironmentId,
                                                this.Name,
                                                this.SrpCommandContext)
                   {
                       Code = this.Code,
                       Description = this.Description,
                       ContactEmail = this.ContactEmail,
                       ContactPhone = this.ContactPhone,
                       Address = this.Address,
                   };
        }

        public async Task CreateOrganizationAsync()
        {
            // Init
            CreateOrganizationCommand createOrganizationCommand = this.NewCreateOrganizationCommand();

            // Create Organization
            await this.SrpCommandProcessor
                      .SendAsync(createOrganizationCommand, false);
        }
        #endregion
    }
}