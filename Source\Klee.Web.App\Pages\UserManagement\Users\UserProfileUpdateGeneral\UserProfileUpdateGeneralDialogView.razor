﻿@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits UserProfileUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="@ViewModelEditContext"
                 Title="Update User Profile">
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="First Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.FirstName"/>
                <ValidationMessage For="@(() => ViewModel.FirstName)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Last Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.LastName"/>
                <ValidationMessage For="@(() => ViewModel.LastName)" />
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Display Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.DisplayName"/>
                <ValidationMessage For="@(() => ViewModel.DisplayName)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Email" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.Email" ReadOnly="true"/>
                <ValidationMessage For="@(() => ViewModel.Email)" />
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}
