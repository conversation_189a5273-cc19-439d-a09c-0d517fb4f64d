﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.VehicleManagement.Vehicles
{
    public class DeleteVehicleCommand : CommandBase
    {
        #region RESULT CLASS
        public class CommandResult
        {
        }
        #endregion

        #region PROPERTIES
        public string VehicleId { get; }

        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public DeleteVehicleCommand(string vehicleId,
                                 ICommandContext commandContext)
            : base(commandContext)
        {
            this.VehicleId = vehicleId;
        }
        #endregion
    }
}
