﻿using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails.Data;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails;

public class OperatorDetailsViewModel 
    : ViewModelBase<OperatorDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
{
    #region PROPERTIES - ENTITY
    public Operator Operator { get; private set; }
    #endregion

    #region PROPERTIES - VIEW
    public string OperatorId { get; private set; } = "";

    public string OperatorFirstName { get; private set; } = "";
    public string OperatorLastName { get; private set; } = "";
    public string Email { get; private set; } = "";
    public double HourlyRateInEuros { get; private set; } = 0.0;
    public List<QualificationTypeIds> Qualifications { get; private set; } = new ();

    public string SoftwareEnvironmentDisplayName { get; private set; } = "";
    public bool IsActive { get; private set; }

    public string CreatedBy { get; private set; } = "";
    public string CreatedByName { get; private set; } = "";
    public string CreatedDateTimeRstAsString { get; private set; } = "";
    public string ModifiedBy { get; private set; } = "";
    public string ModifiedByName { get; private set; } = "";
    public string ModifiedDateTimeRstAsString { get; private set; } = "";
    #endregion

    #region CONSTRUCTORS
    public OperatorDetailsViewModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - USER SESSION DATA
    public void SetUserSessionData(UserSessionData userSessionData)
    {
    }

    public UserSessionData GetUserSessionData()
    {
        return new UserSessionData()
               {
               };
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadOperatorAsync(string operatorId,
                                             bool forceLoad = false)
    {
        if(this.OperatorId != operatorId ||
           forceLoad)
        {
            // Get Operator
            Operator operatorObj = await this.SrpQueryProcessor.ExecuteAsync(new GetOperatorQuery(operatorId, this.SrpQueryContext)) ?? new Operator();
            
            // Set Operator
            this.OperatorId = operatorObj.OperatorId;
            this.OperatorFirstName = operatorObj.FirstName;
            this.OperatorLastName = operatorObj.LastName;
            this.Email = operatorObj.OperatorEmail;
            this.HourlyRateInEuros = operatorObj.HourlyRateInEuros;
            this.Qualifications = operatorObj.Qualifications.ToList();
            this.SoftwareEnvironmentDisplayName = operatorObj.SoftwareEnvironmentId.AsString(EnumFormat.DisplayName);
            this.IsActive = operatorObj.IsActive ?? false;
            this.CreatedBy = operatorObj.CreatedBy;
            this.CreatedByName = operatorObj.GetCreatedByName();
            this.CreatedDateTimeRstAsString = operatorObj.GetCreatedDateTimeRstAsString();
            this.ModifiedBy = operatorObj.ModifiedBy;
            this.ModifiedByName = operatorObj.GetModifiedByName();
            this.ModifiedDateTimeRstAsString = operatorObj.GetModifiedDateTimeRstAsString();

            // Set Operator
            this.Operator = operatorObj;

            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOperatorAsync(string operatorId)
    {
        try
        {
            // Delete Operator
            await this.SrpCommandProcessor.SendAsync(new DeleteOperatorCommand(operatorId, 
                                                                        this.SrpCommandContext));
        }
        catch
        {
            throw;
        }
    }
    #endregion
}