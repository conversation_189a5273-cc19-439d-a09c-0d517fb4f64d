﻿using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.StationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Threading.Tasks;
using System.Threading;
using System;
using Klee.Domain.Entities.StationManagement.Stations;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.StationManagement.Stations;

    public sealed class GetStationQueryHandler
        : QueryHandlerAsync<GetStationQuery, Station>
    {
        #region PROPERTIES
        private IStationSrpRepository StationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetStationQueryHandler(IStationSrpRepository StationSrpRepository,
                                      IMemoryCache memoryCache)
        {
            this.StationSrpRepository = StationSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<Station> ExecuteAsync(GetStationQuery query,
                                                         CancellationToken cancellationToken = new CancellationToken())
        {
            // Init
            string stationId = query.StationId;

            // Get Station from cache
            bool isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetStationCacheId(stationId), out Station? cachedStation);

            // Init
            Station? station = cachedStation;

            // Get Station from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get Station
                station = await this.StationSrpRepository.Entities(query)
                    .Include(_ => _.Roc)
                    .FirstOrDefaultAsync(_ => _.StationId == stationId &&
                                              _.EntityPartitionKey == stationId, cancellationToken: cancellationToken);

                // Cache Station
                this.MemoryCache.Set(MemoryCacheIds.GetStationCacheId(stationId),
                                     station, new MemoryCacheEntryOptions()
                                              {
                                                  AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                  Size = 1
                                              });
            }

            return station ?? new Station();
        }
        #endregion
    }