using System;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;

public class UpdateVoyageInvoiceStatusCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public Guid VoyageInvoiceId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public Guid VoyageInvoiceId { get; }
    public VoyageInvoiceStatus Status { get; set; }
    public DateTime? PaymentDate { get; set; }
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateVoyageInvoiceStatusCommand(Guid voyageInvoiceId, ICommandContext commandContext)
        : base(commandContext)
    {
        this.VoyageInvoiceId = voyageInvoiceId;
    }
    #endregion
}
