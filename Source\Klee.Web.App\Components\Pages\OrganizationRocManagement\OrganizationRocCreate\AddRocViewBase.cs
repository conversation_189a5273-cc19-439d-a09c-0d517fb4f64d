using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Services;
using Klee.Domain.Services.UserContextService;
using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocList;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleCreate;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Renoir.Srp.Portal.Web.Pages.Common.Views;
using Renoir.Web.Razor.Services.UserNotifications;

namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocCreate;

public class AddRocViewBase : ComponentBase
{
    #region PROPERTIES - INJECTED
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    [Inject] 
    protected ILogger<AddRocViewBase> Logger { get; set; }
    [Inject]
    private INotificationService NotificationService { get; set; }
    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES - STATE
    protected AddRocViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;
    protected List<string> ValidationErrors { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();
            
        if (!isUserOrganizationAdmin)
        {
            // Redirect to operators list if not admin
            NavigationManager.NavigateTo(Rocs.GetUri());
            return;
        }
        // Init
        this.ViewModel = new AddRocViewModel(SrpProcessors);
        this.IsLoading = false;
        await base.OnInitializedAsync();
    }
    #endregion

    #region METHODS
    protected void NavigateToRocList()
    {
        // Clear ViewModel
        this.ViewModel.Clear();
        NavigationManager.NavigateTo(Rocs.GetUri());
    }

    #endregion

    #region METHODS - FORM HANDLING
    protected async Task HandleSubmit()
    {
        if (this.IsSubmitting)
            return;

        try
        {
            this.IsSubmitting = true;
            StateHasChanged();

            ValidationErrors.Clear();
            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Create Roc
                await this.ViewModel.CreateRocAsync();

                //Notify - don't await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Created",
                    Description = $"Roc '{ViewModel.RocName}' has been successfully created.",
                    Duration = 4.5
                });

                NavigateToRocList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when creating roc.");
            #endregion

            // Show error notification
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to create roc. Please check your input and try again.",
                Duration = 6.0
            });

            ValidationErrors.Add($"An error occurred while creating the roc: {exception.Message}");
        }
        finally
        {
            this.IsSubmitting = false;
            StateHasChanged();
        }
    }
    #endregion

}
