﻿using System.Collections.Generic;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators;

public class CreateOperatorCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; }

    public string OrganizationId { get; }
    public string OperatorFirstName { get; set; } = "";
    public string OperatorLastName { get; set; } = "";
    public string OperatorEmail { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public List<QualificationTypeIds> Qualifications { get; set; } = new ();
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateOperatorCommand(SoftwareEnvironmentIds softwareEnvironmentId,
        string organizationId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.SoftwareEnvironmentId = softwareEnvironmentId;
        this.OrganizationId = organizationId;
    }
    #endregion
}