﻿using EnumsNET;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Vehicles;

    public class OrganizationDetailsViewVehicleComponentModel
        : ViewModelBase<OrganizationDetailsViewVehicleComponentModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - STATIC
        // Selectable
        public static IList<VehiclesMainFilterTypeIds> SelectableVehiclesMainFilterTypeIds { get; }
            = Enums.GetMembers<VehiclesMainFilterTypeIds>()
                   .Where(_ => _.Value != VehiclesMainFilterTypeIds.None)
                   .Select(_ => _.Value).ToList();
        #endregion

        #region FIELDS
        private VehiclesMainFilterTypeIds _VehiclesMainFilterTypeId = VehiclesMainFilterTypeIds.None;
        #endregion

        #region PROPERTIES
        public VehiclesMainFilterTypeIds VehiclesMainFilterTypeId
        {
            get => this._VehiclesMainFilterTypeId;
            set => this.RaiseAndSetIfChanged(ref this._VehiclesMainFilterTypeId, value);
        }

        // Actual Loaded
        public VehiclesMainFilterTypeIds LoadedVehiclesMainFilterTypeId { get; private set; }

        //
        public IReadOnlyList<VehicleListItem> Vehicles { get; private set; } = new List<VehicleListItem>();
        #endregion

        #region CONSTRUCTORS
        public OrganizationDetailsViewVehicleComponentModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDES
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
            this.VehiclesMainFilterTypeId = userSessionData.VehiclesMainFilterTypeId;
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                       VehiclesMainFilterTypeId = this.LoadedVehiclesMainFilterTypeId
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationVehiclesAsync(string organizationId, bool forceLoad = false)
        {
            // Adjust Properties
            this.VehiclesMainFilterTypeId = this.VehiclesMainFilterTypeId != VehiclesMainFilterTypeIds.None ? this.VehiclesMainFilterTypeId : VehiclesMainFilterTypeIds.All;

            // Load Vehicles
            if (this.LoadedVehiclesMainFilterTypeId != this.VehiclesMainFilterTypeId ||
                forceLoad)
            {
                // Load Vehicles
                switch (this.VehiclesMainFilterTypeId)
                {
                    case VehiclesMainFilterTypeIds.Active:
                        // Load Vehicles
                        this.Vehicles = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationVehiclesQuery(organizationId, this.SrpQueryContext)
                                                                               {
                                                                                   IsActive = true
                                                                               });
                        break;
                    case VehiclesMainFilterTypeIds.All:
                    default:
                        // Load Vehicles
                        this.Vehicles = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationVehiclesQuery(organizationId, this.SrpQueryContext));
                        break;
                }

                // Set
                this.LoadedVehiclesMainFilterTypeId = this.VehiclesMainFilterTypeId;

                // Notify
                await this.InvokeStateHasChangedOnHostAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteVehicleAsync(string vehicleId, string organizationId)
        {
            try
            {
                // Delete Vehicle
                await this.SrpCommandProcessor.SendAsync(new DeleteVehicleCommand(vehicleId,
                                                                              this.SrpCommandContext));

                // Load Vehicles
                await this.LoadOrganizationVehiclesAsync(organizationId, forceLoad: true);
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }