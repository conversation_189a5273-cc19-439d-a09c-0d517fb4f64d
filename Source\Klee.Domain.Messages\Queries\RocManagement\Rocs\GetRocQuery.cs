﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.RocManagement.Rocs;

public class GetRocQuery: QueryBase<Roc> {
    #region PROPERTIES
    public string RocId { get; }
    public bool AllowCached { get; set; } = false;
    public bool IncludeRelations { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetRocQuery(string rocId,
        IQueryContext context)
        : base(context)
    {
        this.RocId = rocId;
    }
    #endregion
}