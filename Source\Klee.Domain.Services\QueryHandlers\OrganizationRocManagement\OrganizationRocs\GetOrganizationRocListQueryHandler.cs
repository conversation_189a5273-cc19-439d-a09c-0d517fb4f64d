﻿using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs.Data;
using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Darker;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using EnumsNET;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.OrganizationRocManagement.OrganizationRocs;

public sealed class GetOrganizationRocListQueryHandler
    : QueryHandlerAsync<GetOrganizationRocListQuery, IReadOnlyList<OrganizationRocListItem>>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationRocListQueryHandler(IRocSrpRepository organizationRocSrpRepository,
        IUserContextHelperService userContextHelperService)
        {
            this.RocSrpRepository = organizationRocSrpRepository;
            this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<OrganizationRocListItem>> ExecuteAsync(GetOrganizationRocListQuery query,
                                                                            CancellationToken cancellationToken = new CancellationToken())
    {
        // Get Organization Rocs from DB
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        List<OrganizationRocListItem> organizationRocListItems =
            await this.RocSrpRepository.Entities(query)
                      .Where(_ => _.OrganizationId == organizationId)
                      .Where(_ => _.IsActive == true)
                      .OrderBy(_ => _.RocId)
                      .Select(_ => new OrganizationRocListItem()
                                   {
                                       EntityId = _.EntityId,
                                       RocId = _.RocId,
                                       RocName = _.RocName,
                                       Address = _.Address,
                                       Location = _.Location,
                                       PostalCode = _.PostalCode,
                                       Country = _.Country,
                                       CountryDisplayName = _.Country.AsString(EnumFormat.DisplayName),
                      })
                      .ToListAsync(cancellationToken: cancellationToken);

        return organizationRocListItems;
    }
    #endregion
}