using System;
using System.Collections.Generic;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.VoyageManagement.Voyages;

public class CreateVoyageCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public Guid VoyageId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string BookingOrganizationId { get; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public string Description { get; set; } = "";
    public List<QualificationTypeIds> RequiredQualifications { get; set; } = new();
    public string VehicleId { get; set; } = "";
    public string? OperatorId { get; set; } = null;
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateVoyageCommand(string bookingOrganizationId, ICommandContext commandContext)
        : base(commandContext)
    {
        this.BookingOrganizationId = bookingOrganizationId;
    }
    #endregion
}
