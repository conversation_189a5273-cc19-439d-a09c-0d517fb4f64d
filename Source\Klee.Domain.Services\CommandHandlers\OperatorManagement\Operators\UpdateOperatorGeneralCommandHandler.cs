﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators.Helpers;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators;

public sealed class UpdateOperatorGeneralCommandHandler
    : RequestHandlerAsync<UpdateOperatorGeneralCommand> {
    #region PROPERTIES

    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOperatorGeneralCommandHandler(IOperatorSrpRepository operatorSrpRepository,
        IMemoryCache memoryCache) {
        OperatorSrpRepository = operatorSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOperatorGeneralCommandValidator))]
    public override async Task<UpdateOperatorGeneralCommand> HandleAsync(UpdateOperatorGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {
        //Get Operator (if it exists) 
        if (await OperatorSrpRepository.ExistsAsync(_ => _.OperatorId == command.OperatorId &&
                                                        _.EntityPartitionKey == command.OperatorId,
                command)) {
            Operator operatorObj = await OperatorSrpRepository.FindAsync(_ => _.OperatorId == command.OperatorId &&
                                                                        _.EntityPartitionKey == command.OperatorId,
                command);

            operatorObj.FirstName = command.OperatorFirstName;
            operatorObj.LastName = command.OperatorLastName;
            operatorObj.OperatorEmail = command.OperatorEmail;
            operatorObj.HourlyRateInEuros = command.HourlyRateInEuros;
            operatorObj.OrganizationId = command.OrganizationId;
            // Update
            await OperatorSrpRepository.UpdateAsync(operatorObj, command);

            // Set Result
            command.Result.EntityId = operatorObj.EntityId;

            // Clear Caches
            MemoryCache.RemoveOperator(command.OperatorId);
        }
        else {
            throw new EntityNotFoundException(
                $"Operator with Operator id '{command.OperatorId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}