using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Services;
using Klee.Domain.Services.UserContextService;
using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorEdit;
using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocList;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views;
using Renoir.Web.Razor.Services.UserNotifications;

namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocEdit;

public class EditRocViewBase : ComponentBase
{
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }


    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<EditOperatorViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    
    #region PARAMETERS
    [Parameter]
    public string RocIdEncoded { get; set; } = "";
    public string RocId => Base64UrlEncoder.Decode(this.RocIdEncoded);
    #endregion

    #region PROPERTIES - STATE
    protected EditRocViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;
    protected List<string> ValidationErrors { get; set; } = new();
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {

        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!isUserOrganizationAdmin)
        {
            // Redirect to rocs list if not admin
            NavigateToRocList();
            return;
        }

        await base.OnInitializedAsync();

    }

    protected override async Task OnParametersSetAsync()
    {

        if (string.IsNullOrEmpty(RocId))
        {
            NavigateToRocList();
            return;
        }

        // Initialize ViewModel and load roc data
        this.ViewModel = new EditRocViewModel(this.SrpProcessors);
        await this.ViewModel.LoadRocAsync(RocId);

        this.IsLoading = false;

        StateHasChanged();

        await base.OnParametersSetAsync();
    }
    #endregion

     #region EVENT HANDLERS
    protected void NavigateToRocList()
    {
        this.NavigationManager.NavigateTo(Rocs.GetUri());
    }

    protected async Task HandleSubmit()
    {
        if (this.IsSubmitting)
            return;

        try
        {
            this.IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();


            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Update the roc
                await this.ViewModel.UpdateRocAsync();

                // Notify - dont await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Success",
                    Description = $"Roc '{this.ViewModel.RocName}' has been updated successfully",
                    Duration = 4.0
                });

                // Navigate back to roc list
                NavigateToRocList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when updating roc with id '{RocId}'.", RocId);
            #endregion

            // Show error notification
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to update ROC. Please check your input and try again.",
                Duration = 6.0
            });
        }
        finally
        {
            this.IsSubmitting = false;
            StateHasChanged();
        }
    }
    #endregion
}
