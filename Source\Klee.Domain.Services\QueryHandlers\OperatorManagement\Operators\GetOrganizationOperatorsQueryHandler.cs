﻿using Paramore.Darker;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.OperatorManagement.Operators;

public class GetOrganizationOperatorsQueryHandler : QueryHandlerAsync<GetOrganizationOperatorsQuery, IReadOnlyList<OperatorListItem>> {
    #region PROPERTIES

    private IOperatorSrpRepository OperatorSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public GetOrganizationOperatorsQueryHandler(IOperatorSrpRepository operatorSrpRepository) {
        this.OperatorSrpRepository = operatorSrpRepository;
    }

    #endregion

    #region METHODS

    public override async Task<IReadOnlyList<OperatorListItem>> ExecuteAsync(GetOrganizationOperatorsQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        string organizationId = query.OrganizationId;

        // Init
        List<OperatorListItem> operatorListItems =
            // Get OperatorListItems from DB
            await this.OperatorSrpRepository.Entities(query)
                .Where(_ => _.OrganizationId == organizationId)
                .OrderBy(_ => _.OperatorId)
                .Select(_ => new OperatorListItem() {
                    EntityId = _.EntityId,
                    OperatorId = _.OperatorId,
                    OperatorFirstName = _.FirstName,
                    OperatorLastName = _.LastName,
                    Email = _.OperatorEmail,
                    HourlyRateInEuros = _.HourlyRateInEuros,
                    OrganizationId = _.OrganizationId,
                    IsActive = _.IsActive ?? false
                })
                .ToListAsync(cancellationToken: cancellationToken);

        // Filter "IsActive" (if needed)
        if (query.IsActive != null) {
            operatorListItems = operatorListItems.Where(_ => _.IsActive == query.IsActive).ToList();
        }

        return operatorListItems;
    }
    #endregion
}