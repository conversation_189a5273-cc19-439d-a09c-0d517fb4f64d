using System;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.InvoiceManagement.Invoices;

public class InvoicesViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    #endregion

    #region PROPERTIES
    protected InvoicesViewModel ViewModel { get; private set; }
    protected bool ShowMarkAsPaidModal { get; set; } = false;
    protected Guid SelectedInvoiceId { get; set; } = Guid.Empty;
    protected string SelectedInvoiceDescription { get; set; } = "";
    #endregion

    #region LIFECYCLE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new InvoicesViewModel(SrpProcessors, UserAuthenticationService);
        await ViewModel.InitializeAsync();
        StateHasChanged();
    }
    #endregion

    #region EVENT HANDLERS
    protected void ShowMarkAsPaidConfirmation(Guid invoiceId, string vesselName)
    {
        SelectedInvoiceId = invoiceId;
        SelectedInvoiceDescription = $"Mark invoice for vessel '{vesselName}' as paid? This confirms that your organization has received payment for the specified voyage.";
        ShowMarkAsPaidModal = true;
    }

    protected async Task ConfirmMarkAsPaid()
    {
        try
        {
            ShowMarkAsPaidModal = false;

            await ViewModel.MarkInvoiceAsPaidAsync(SelectedInvoiceId);

            NotificationService.Success(new NotificationConfig()
            {
                Message = "Invoice Updated",
                Description = "Invoice has been successfully marked as paid.",
                Duration = 4.5
            });
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = $"Failed to update invoice: {ex.Message}",
                Duration = 6
            });
        }
        finally
        {
            SelectedInvoiceId = Guid.Empty;
            SelectedInvoiceDescription = "";
        }
    }

    protected void CancelMarkAsPaid()
    {
        ShowMarkAsPaidModal = false;
        SelectedInvoiceId = Guid.Empty;
        SelectedInvoiceDescription = "";
    }
    #endregion

}
