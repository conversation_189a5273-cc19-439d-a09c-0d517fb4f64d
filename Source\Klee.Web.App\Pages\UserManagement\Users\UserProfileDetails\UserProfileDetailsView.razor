﻿@page "/UserProfiles/Management/UserProfile/{UserProfileIdEncoded}"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.IdentityModel.Tokens
@using Renoir.Srp.Portal.Web.Application.Security
@using Klee.Web.App.Pages.UserManagement.Users.UserProfileUpdateGeneral

@inherits UserProfileDetailsViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 CanShowButtonDelete=true
                 OnClickDelete="@OnClickDelete">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="User Profiles" />
        <RMainLayoutBodyBreadcrumbListItem Text="Details" />
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
    </ToolbarRightMenuItems>
    <Content>
        <div class="row">
            <div class="col-lg-6 col-xl-4 mb-4">
                <section class="card">
                    <header class="card-header">
                        <h2 class="card-title">Identification</h2>
                        <div class="card-actions">
                            <RCardActionEdit OnClickEdit="@OnClickEdit_OpenUserProfileUpdateGeneralDialogView"
                                             CanShow=@AuthenticatedUser.IsAdminApp() />
                            <RCardActionToggle />
                        </div>
                    </header>
                    <div class="card-body">
                        <RControlTextValueDisplay Label="First Name" Value="@ViewModel?.FirstName" />
                        <RControlTextValueDisplay Label="Last Name" Value="@ViewModel?.LastName" />
                        <RControlTextValueDisplay Label="Display Name" Value="@ViewModel?.DisplayName" />
                        <RControlTextValueDisplay Label="Email" Value="@ViewModel?.Email" />
                    </div>
                    <footer class="card-footer text-end">
                    </footer>
                </section>
            </div>
        </div>
    </Content>
</RMainLayoutBody>

<UserProfileUpdateGeneralDialogView @ref="UserProfileUpdateGeneralDialogView"
                                OnDialogViewOpened="OnUserProfileUpdateGeneralDialogView_Opened"
                                OnDialogViewClosed="OnUserProfileUpdateGeneralDialogView_Closed" />
@code {

    #region METHODS - STATIC
    public static string GetUri(string userProfileId)
    {
        return $"/UserProfiles/Management/UserProfile/{Base64UrlEncoder.Encode(userProfileId)}";
    }
    #endregion

}
