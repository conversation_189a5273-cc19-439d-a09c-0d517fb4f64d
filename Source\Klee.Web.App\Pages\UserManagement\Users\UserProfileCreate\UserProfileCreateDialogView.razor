﻿@using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data
@using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data
@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits UserProfileCreateDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Create UserProfile">
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="First Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.FirstName" />
                <ValidationMessage For="@(() => ViewModel.FirstName)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Last Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.LastName" />
                <ValidationMessage For="@(() => ViewModel.LastName)" />
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Display Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.DisplayName" />
                <ValidationMessage For="@(() => ViewModel.DisplayName)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Email" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.Email" />
                <ValidationMessage For="@(() => ViewModel.Email)" />
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Organization Name" IsRequired="true">
                <DxComboBox Data="this.ViewModel.OrganizationNames"
                            TextFieldName="@nameof(OrganizationNameListItem.Name)"
                            ValueFieldName="@nameof(OrganizationNameListItem.OrganizationId)"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.SelectedOrganizationId" />
                <ValidationMessage For="@(() => ViewModel.SelectedOrganizationId)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
        </div>
    </div>
</RDialogEditForm>

@code {
}