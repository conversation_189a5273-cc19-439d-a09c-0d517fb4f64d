﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.RocManagement.Rocs.Data;
using Klee.Domain.Entities.StationManagement.Stations;
using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Entities.RocManagement.Rocs;

public class Roc : DomainEntityAggregateRootBase<long>
{
    #region FIELDS
    #endregion

    #region PROPERTIES - IDENTIFICATION
    /// <summary>
    /// The Seafar internal id of the ROC
    /// </summary>
    [Required]
    public string RocId { get; internal set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 
    /// </summary>
    public string RocName { get; internal set; } = "";

    /// <summary>
    /// The physical address of the organization
    /// </summary>
    public string Address { get; internal set; } = "";

    /// <summary>
    /// The city where the ROC is located
    /// </summary>
    public string Location { get; internal set; } = "";

    /// <summary>
    /// The postal/zip code of the ROC location
    /// </summary>
    public string PostalCode { get; internal set; } = "";

    /// <summary>
    /// The country where the ROC is located
    /// </summary>
    public CountryIds Country { get; internal set; } = CountryIds.None;
    #endregion

    #region PROPERTIES - SYSTEM
    /// <summary>
    /// The software environment on which the Organization is registered
    /// </summary>
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

    /// <summary>
    /// Is active when the organization is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;
    #endregion

    #region PROPERTIES - RELATIONS
    /// <summary>
    /// List of stations that are part of this ROC
    /// </summary>
    //public List<string> StationIds { get; internal set; } = new();
    public List<Station> Stations { get; internal set; } = new();

    /// <summary>
    /// Organization that this ROC is part of
    /// </summary>
    [Required]
    public string OrganizationId { get; internal set; } = "";
    public Organization Organization { get; internal set; }
    #endregion

    #region CONSTRUCTORS
    public Roc()
    {
    }

    public Roc(string rocId)
    {
        RocId = rocId;
    }
    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return RocId;
    }

    public override string GetEntityId2()
    {
        return RocId;
    }

    public override string GetEntityTypeName()
    {
        return "Roc";
    }
    #endregion
}