using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Components.Pages.VoyagePlanning;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyageManagement.OperatorAssignment;

public class SelectOperatorForVoyageViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public DateTime VoyageStartDateTime { get; set; }
    [Parameter] public DateTime VoyageEndDateTime { get; set; }
    [Parameter] public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    [Parameter] public EventCallback OnClick_Back { get; set; }
    [Parameter] public EventCallback<AvailableCaptainListItem> OnSelectOperator { get; set; }
    #endregion

    #region PROPERTIES
    protected SelectCaptainViewModel ViewModel { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new SelectCaptainViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ViewModel != null)
        {
            // Set voyage criteria and load available operators
            ViewModel.SetVoyageCriteria(VoyageStartDateTime, VoyageEndDateTime, RequiredQualifications);
            await ViewModel.LoadAvailableCaptainsAsync();
            StateHasChanged();
        }

        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - EVENT HANDLERS
    protected async Task HandleOnClick_Back()
    {
        await OnClick_Back.InvokeAsync();
    }

    protected async Task HandleSelectOperator(AvailableCaptainListItem selectedOperator)
    {
        await OnSelectOperator.InvokeAsync(selectedOperator);
    }
    #endregion
}
