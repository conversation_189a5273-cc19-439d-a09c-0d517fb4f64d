﻿using Microsoft.AspNetCore.Components;
using Renoir.Web.Razor.Components.Common;
using Renoir.Web.Razor.Services.UserNotifications;

namespace Klee.Web.App.Shared.Layout.Main
{
    public class MainNavMenuLeftBase : RComponentBase
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        [Inject]
        protected IRUserNotificationService UserNotificationService { get; set; }
        #endregion

        #region METHODS - EVENTS
        #endregion
    }
}
