﻿using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.StationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using EnumsNET;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.StationManagement.Stations;

    public sealed class GetStationListQueryHandler
        : QueryHandlerAsync<GetStationListQuery, IReadOnlyList<StationListItem>>
    {
        #region PROPERTIES
        private IStationSrpRepository StationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetStationListQueryHandler(IStationSrpRepository stationSrpRepository,
                                          IMemoryCache memoryCache)
        {
            this.StationSrpRepository = stationSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<StationListItem>> ExecuteAsync(GetStationListQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            // Get StationListItems from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetStationListCacheId, out List<StationListItem> cachedStationListItems);

            // Init
            List<StationListItem> stationListItems = cachedStationListItems ?? new List<StationListItem>();

            // Get StationListItems from DB (if needed)
            if (query.AllowCached == false ||
                !isCached)
            {
                // Get StationListItems
                stationListItems =
                    await this.StationSrpRepository.Entities(query)
                              .OrderBy(_ => _.StationId)
                              .Select(_ => new StationListItem()
                                           {
                                               EntityId = _.EntityId,
                                               JoystickTypeDisplayName = _.JoystickTypeId.AsString(EnumFormat.DisplayName),
                                               StationId = _.StationId,
                                               StationName = _.StationName,
                                               RocId = _.RocId,
                                               HourlyRateInEuros = _.HourlyRateInEuros,
                                               IsActive = _.IsActive ?? false
                                           })
                              .ToListAsync(cancellationToken: cancellationToken);

                // Cache StationListItems
                this.MemoryCache.Set(MemoryCacheIds.GetStationListCacheId,
                                     stationListItems, new MemoryCacheEntryOptions()
                                                       {
                                                           AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                                           Size = 1
                                                       });
            }

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                stationListItems = stationListItems.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return stationListItems;
        }
        #endregion
    }