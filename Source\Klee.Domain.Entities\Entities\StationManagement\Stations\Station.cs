﻿using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;
using Klee.Domain.Entities.RocManagement.Rocs;
using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Entities.StationManagement.Stations;

public class Station : DomainEntityAggregateRootBase<long>
{

    #region FIELDS
    #endregion

    #region PROPERTIES - IDENTIFICATION
    /// <summary>
    /// The Seafar internal id of the station
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string StationId { get; internal set; } = "";

    /// <summary>
    /// 
    /// </summary>
    public string StationName { get; internal set; } = "";

    /// <summary>
    /// Type of joysticks that are installed on the station
    /// </summary>
    public JoystickTypeIds JoystickTypeId { get; internal set; } = JoystickTypeIds.Undefined;

    /// <summary>
    /// 
    /// </summary>
    public double HourlyRateInEuros { get; internal set; } = 0.0;
    #endregion

    #region PROPERTIES - SYSTEM
    /// <summary>
    /// The software environment on which the Vehicle is used
    /// </summary>
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

    /// <summary>
    /// Is active when the station is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;

    #endregion

    #region PROPERTIES - RELATIONS
    /// <summary>
    /// ID of team that is assigned to this station
    /// </summary>
    //[Required]
    //public string TeamId { get; internal set; } = "";
    //public Team Team { get; internal set; }

    /// <summary>
    /// ID of ROC that this station is part of
    /// </summary>
    [Required]
    public string RocId { get; internal set; } = "";

    public Roc Roc { get; internal set; }

    #endregion

    #region CONSTRUCTORS
    public Station()
    {
    }

    public Station(string stationId)
    {
        StationId = stationId;
    }
    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return StationId;
    }

    public override string GetEntityId2()
    {
        return StationId;
    }

    public override string GetEntityTypeName()
    {
        return "Station";
    }
    #endregion
}