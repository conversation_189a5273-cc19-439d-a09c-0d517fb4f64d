﻿using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleUpdateGeneral
{
    public class VehicleUpdateGeneralDialogViewModel
        : ViewModelBase<VehicleUpdateGeneralDialogViewModel>
    {
        #region PROPERTIES
        [Required]
        [Display(Name = "Vehicle Id")]
        public string VehicleId { get; set; } = "";

        [Required]
        [Display(Name = "Vehicle Name")]
        public string VehicleName { get; set; } = "";

        [Display(Name = "ENI")]
        public string ENI { get; set; } = "";
        
        [Display(Name = "Hourly Rate (Euro)")]
        public double HourlyRateInEuros { get; set; } = 0.0;

        public string OrganizationId { get; set; } = "";
        #endregion

        #region CONSTRUCTORS
        public VehicleUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.VehicleId = "";
            this.VehicleName = "";
            this.ENI = "";
        }

        public async Task LoadVehicleAsync(string vehicleId)
        {
            // Get Vehicle
            Vehicle vehicle = await this.SrpQueryProcessor.ExecuteAsync(new GetVehicleQuery(vehicleId, this.SrpQueryContext)) ?? new Vehicle();

            // Set Vehicle
            this.VehicleId = vehicle.VehicleId;
            this.VehicleName = vehicle.VehicleName;
            this.ENI = vehicle.ENI;
            this.HourlyRateInEuros = vehicle.HourlyRateInEuros;
            this.OrganizationId = vehicle.OrganizationId;
        }
        #endregion

        #region METHODS
        public async Task UpdateVehicleAsync()
        {
            // Init
            UpdateVehicleGeneralCommand updateVehicleGeneralCommand = new UpdateVehicleGeneralCommand(this.VehicleId,
                                                                                                     this.OrganizationId,
                                                                                                      this.SrpCommandContext)
                                                                      {
                                                                          VehicleName = this.VehicleName,
                                                                          ENI = this.ENI,
                                                                          HourlyRateInEuros = this.HourlyRateInEuros
                                                                      };

            // Create Vehicle
            await this.SrpCommandProcessor
                      .SendAsync(updateVehicleGeneralCommand, false);
        }
        #endregion
    }
}