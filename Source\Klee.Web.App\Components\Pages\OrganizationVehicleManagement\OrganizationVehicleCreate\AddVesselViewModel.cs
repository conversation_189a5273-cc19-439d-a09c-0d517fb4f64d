using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;
using Klee.Domain.Services;
using Microsoft.AspNetCore.Components;
using Monet.Helpers;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Validations.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleCreate
{
    public class AddVesselViewModel
    {
        #region FIELDS
        private ISrpProcessors _srpProcessors;
        #endregion


        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableVesselTypeDisplayNames { get; } = Enums.GetMembers<VesselTypeIds>()
            .Where(_ => _.Value != VesselTypeIds.None)
            .Select(_ => _.AsString(EnumFormat.DisplayName))
            .OrderBy(_ => _).ToList();
        #endregion


        #region PROPERTIES

        [Required]
        [Display(Name = "Vehicle Name")]
        public string VehicleName { get; set; } = "";

        [Display(Name = "ENI")]
        public string ENI { get; set; } = "";

        [Display(Name = "Hourly rate (euro)")]
        public double HourlyRateInEuros { get; set; } = 0.0;

        [Display(Name = "Length (meters)")]
        public double Length { get; set; } = 0.0;

        [Display(Name = "Beam (meters)")]
        public double Beam { get; set; } = 0.0;

        [Required]
        [Display(Name = "Vessel Type")]
        public VesselTypeIds VesselType { get; set; } = VesselTypeIds.None;
        #endregion

        #region CONSTRUCTORS
        public AddVesselViewModel(ISrpProcessors srpProcessors)
        {
            this._srpProcessors = srpProcessors;
        }
        #endregion


        #region METHODS
        public void Clear()
        {
            this.VehicleName = "";
            this.ENI = "";
            this.HourlyRateInEuros = 0.0;
        }


        #endregion

        #region METHODS - VALIDATE
        public async Task<IEnumerable<ValidationResult>> Validate()
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateOrganizationVehicleCommandValidator createOrganizationVehicleCommandValidator = new CreateOrganizationVehicleCommandValidator();
            (await createOrganizationVehicleCommandValidator.ValidateAsync(await this.NewCreateOrganizationVehicleCommandAsync()))
                .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        public async Task<CreateOrganizationVehicleCommand> NewCreateOrganizationVehicleCommandAsync()
        {
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            
            return new CreateOrganizationVehicleCommand(VehicleTypeIds.WaterVessel,
                commandContext)
            {
                VehicleName = this.VehicleName,
                ENI = this.ENI,
                HourlyRateInEuros = this.HourlyRateInEuros,
                Length = this.Length,
                Beam = this.Beam,
                VesselType = this.VesselType,
            };
        }

        public async Task CreateOrganizationVehicleAsync()
        {
            // Init
            CreateOrganizationVehicleCommand createOrganizationVehicleCommand = await this.NewCreateOrganizationVehicleCommandAsync();
            
            // Create UserProfile
            await this._srpProcessors
                .CommandProcessor.SendAsync(createOrganizationVehicleCommand, false);
        }
        #endregion
    }
}