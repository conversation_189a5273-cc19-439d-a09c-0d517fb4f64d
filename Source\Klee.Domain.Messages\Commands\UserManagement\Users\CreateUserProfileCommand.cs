﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.UserManagement.Users;

public class CreateUserProfileCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string UserId => this.Email;
    public string FirstName { get; set; } = "";
    public string LastName { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string Email { get; }
    public string OrganizationId  { get; }

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateUserProfileCommand(string email,
        string organizationId,
        ICommandContext commandContext)
        : base(commandContext) {
        this.OrganizationId = organizationId;
        this.Email = email;
    }
    #endregion
}