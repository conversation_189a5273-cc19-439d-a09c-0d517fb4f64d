﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails.Data;
using EnumsNET;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleUpdateGeneral;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleDetails
{
    public class VehicleDetailsViewModel 
        : ViewModelBase<VehicleDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - ENTITY
        public Vehicle Vehicle { get; private set; }
        #endregion

        #region PROPERTIES - VIEW
        public string VehicleTypeDisplayName { get; private set; } = "";
        public string VehicleId { get; private set; } = "";

        public string VehicleName { get; private set; } = "";
        public string ENI { get; private set; } = "";
        public double HourlyRateInEuros { get; private set; } = 0.0;
        public string SoftwareEnvironmentDisplayName { get; private set; } = "";
        public bool IsActive { get; private set; }

        public string CreatedBy { get; private set; } = "";
        public string CreatedByName { get; private set; } = "";
        public string CreatedDateTimeRstAsString { get; private set; } = "";
        public string ModifiedBy { get; private set; } = "";
        public string ModifiedByName { get; private set; } = "";
        public string ModifiedDateTimeRstAsString { get; private set; } = "";
        #endregion

        #region CONSTRUCTORS
        public VehicleDetailsViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadVehicleAsync(string vehicleId,
                                                 bool forceLoad = false)
        {
            if(this.VehicleId != vehicleId ||
               forceLoad)
            {
                // Get Vehicle
                Vehicle vehicle = await this.SrpQueryProcessor.ExecuteAsync(new GetVehicleQuery(vehicleId, this.SrpQueryContext)) ?? new Vehicle();
                
                // Set Vehicle
                this.VehicleTypeDisplayName = vehicle.VehicleTypeId.AsString(EnumFormat.DisplayName);
                this.VehicleId = vehicle.VehicleId;
                this.VehicleName = vehicle.VehicleName;
                this.ENI = vehicle.ENI;
                this.HourlyRateInEuros = vehicle.HourlyRateInEuros;
                this.SoftwareEnvironmentDisplayName = vehicle.SoftwareEnvironmentId.AsString(EnumFormat.DisplayName);
                this.IsActive = vehicle.IsActive ?? false;
                this.CreatedBy = vehicle.CreatedBy;
                this.CreatedByName = vehicle.GetCreatedByName();
                this.CreatedDateTimeRstAsString = vehicle.GetCreatedDateTimeRstAsString();
                this.ModifiedBy = vehicle.ModifiedBy;
                this.ModifiedByName = vehicle.GetModifiedByName();
                this.ModifiedDateTimeRstAsString = vehicle.GetModifiedDateTimeRstAsString();

                // Set Vehicle
                this.Vehicle = vehicle;

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteVehicleAsync(string vehicleId)
        {
            try
            {
                // Delete Vehicle
                await this.SrpCommandProcessor.SendAsync(new DeleteVehicleCommand(vehicleId, 
                                                                            this.SrpCommandContext));
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }
}