﻿using System.Security.Claims;
using System.Threading.Tasks;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.UserManagement.Users;

namespace Klee.Domain.Services.UserContextService;

public interface IUserContextHelperService {
    Task<UserProfile?> GetUserProfileByClaimsAsync(ClaimsPrincipal userClaimsPrincipal);
    Task<string> GetUserOrganizationIdByClaimsAsync(ClaimsPrincipal userClaimsPrincipal);
    public bool IsUserOrganizationAdmin(ClaimsPrincipal userClaimsPrincipal);
    Task<bool> IsUserOrganizationAdminForOrganizationAsync(ClaimsPrincipal userClaimsPrincipal, string orgId);
}