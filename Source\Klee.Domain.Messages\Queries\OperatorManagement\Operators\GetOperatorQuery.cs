﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OperatorManagement.Operators;

public class GetOperatorQuery
    : QueryBase<Operator>
{
    #region PROPERTIES
    public string OperatorId { get; }

    public bool AllowCached { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetOperatorQuery(string operatorId,
        IQueryContext context)
        : base(context)
    {
        this.OperatorId = operatorId;
    }
    #endregion
}