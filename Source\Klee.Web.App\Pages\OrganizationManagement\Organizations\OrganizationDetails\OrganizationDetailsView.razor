@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Data
@using Microsoft.IdentityModel.Tokens
@using DevExpress.Blazor
@using DevExpress.Blazor.Navigation
@using Microsoft.AspNetCore.Authorization
@using Renoir.Application.Security.Helpers
@using Renoir.Srp.Portal.Web.Application.Security
@using Microsoft.AspNetCore.Components.Authorization
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateGeneral
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationUpdateContact
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Vehicles
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Rocs
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleCreate
@using Klee.Web.App.Pages.RocManagement.Rocs.RocCreate
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorCreate
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Operators

@page "/Organizations/Management/Organization/{OrganizationIdEncoded}"
@inherits OrganizationDetailsViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody @ref="MainLayoutBody"
                 PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 OnClickDelete="@OnClickDelete"
                 CanShowButtonDelete="true"
                 OnActiveTabChanged="@OnMainLayoutBody_ActiveTabChanged"
                 ActiveTabId="@DetailsViewProperties.ActiveTabId">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Organizations"/>
        <RMainLayoutBodyBreadcrumbListItem Text="Details"/>
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
        <DxMenuItem Text="Add ROC" IconCssClass="fas fa-plus" Click="OnClickAddRoc_OpenRocCreateDialogView"/>
        <DxMenuItem Text="Add Vehicle" IconCssClass="fas fa-plus" Click="OnClickAddVehicle_OpenVehicleCreateDialogView"/>
        <DxMenuItem Text="Add Operator" IconCssClass="fas fa-plus" Click="OnClickAddOperator_OpenOperatorCreateDialogView" />
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarRightMenuItems>
    <TabNavItems>
        <RTabNavItem TabId="@OrganizationDetailsViewTabIds.General" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemGeneral_IsActiveChanged">GENERAL</RTabNavItem>
        <RTabNavItem TabId="@OrganizationDetailsViewTabIds.Vehicles" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemRealtime_IsActiveChanged">VEHICLES</RTabNavItem>
        <RTabNavItem TabId="@OrganizationDetailsViewTabIds.Rocs" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemRealtime_IsActiveChanged">ROCS</RTabNavItem>
        <RTabNavItem TabId="@OrganizationDetailsViewTabIds.Operators" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemRealtime_IsActiveChanged">OPERATORS</RTabNavItem>
    </TabNavItems>
    <TabContentItems>
        <RTabContentItem @ref="@TabContentItemGeneral"
                         TabId="@OrganizationDetailsViewTabIds.General" MinHeight="200">
            <div class="row">
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Identification</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenOrganizationUpdateGeneralDialogView"
                                                         CanShow=@AuthenticatedUser.IsAdminApp() />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="Organization Id" Value="@ViewModel.OrganizationId" />
                                    <RControlTextValueDisplay Label="Organization Name" Value="@ViewModel.OrganizationName" />
                                    <RControlTextValueDisplay Label="Code" Value="@ViewModel.Code" />
                                    <RControlTextValueDisplay Label="Description" Value="@ViewModel.Description" />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Contact</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenOrganizationUpdateContactDialogView"
                                                         CanShow=true />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="Email" Value="@ViewModel.ContactEmail" />
                                    <RControlTextValueDisplay Label="Phone" Value="@ViewModel.ContactPhone" />
                                    <RControlTextValueDisplay Label="Address" Value="@ViewModel.Address" />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                                <section class="card">
                                    <header class="card-header">
                                        <h2 class="card-title">System</h2>
                                        <div class="card-actions">
                                            <RCardActionToggle />
                                        </div>
                                    </header>
                                    <div class="card-body">
                                        <RControlTextValueDisplay Label="Environment" Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                                        <RControlCheckBoxValueDisplay Label="Active" Value="@ViewModel.IsActive" />
                                    </div>
                                </section>
                            </AuthorizeView>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
        <RTabContentItem @ref="@TabContentItemVehicles"
                         TabId="@OrganizationDetailsViewTabIds.Vehicles" MinHeight="200">
            <div class="row">
                <div class="">
                    <div class="row">
                        <div class="">
                            <OrganizationDetailsViewVehiclesComponent @ref="OrganizationDetailsViewVehiclesComponentRef" OrganizationId="@this.OrganizationId"/>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
        
        <RTabContentItem @ref="@TabContentItemRocs"
                         TabId="@OrganizationDetailsViewTabIds.Rocs" MinHeight="200">
            <div class="row">
                <div class="">
                    <div class="row">
                        <div class="">
                            <OrganizationDetailsViewRocsComponent @ref="OrganizationDetailsViewRocsComponentRef" OrganizationId="@this.OrganizationId" />
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
        
        <RTabContentItem @ref="@TabContentItemOperators"
                         TabId="@OrganizationDetailsViewTabIds.Operators" MinHeight="200">
            <div class="row">
                <div class="">
                    <div class="row">
                        <div class="">
                            <OrganizationDetailsViewOperatorsComponent @ref="OrganizationDetailsViewOperatorsComponentRef" OrganizationId="@this.OrganizationId" />
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
    </TabContentItems>
    <Content>
    </Content>
</RMainLayoutBody>

<OrganizationUpdateGeneralDialogView @ref="OrganizationUpdateGeneralDialogView"
                                     OnDialogViewOpened="OnOrganizationUpdateGeneralDialogView_Opened"
                                     OnDialogViewClosed="OnOrganizationUpdateGeneralDialogView_Closed" />

<OrganizationUpdateContactDialogView @ref="OrganizationUpdateContactDialogView"
                                     OnDialogViewOpened="OnOrganizationUpdateContactDialogView_Opened"
                                     OnDialogViewClosed="OnOrganizationUpdateContactDialogView_Closed" />

<VehicleCreateDialogView @ref="VehicleCreateDialogView"
                         OnDialogViewOpened="OnVehicleCreateDialogView_Opened"
                         OnDialogViewClosed="OnVehicleCreateDialogView_Closed" />

<RocCreateDialogView @ref="RocCreateDialogView"
                     OnDialogViewOpened="OnRocCreateDialogView_Opened"
                     OnDialogViewClosed="OnRocCreateDialogView_Closed" />

<OperatorCreateDialogView @ref="OperatorCreateDialogView"
                     OnDialogViewOpened="OnOperatorCreateDialogView_Opened"
                     OnDialogViewClosed="OnOperatorCreateDialogView_Closed" />

@code {

    #region METHODS - STATIC
    public static string GetUri(string organizationId)
    {
        return $"/Organizations/Management/Organization/{Base64UrlEncoder.Encode(organizationId)}";
    }
    #endregion

}