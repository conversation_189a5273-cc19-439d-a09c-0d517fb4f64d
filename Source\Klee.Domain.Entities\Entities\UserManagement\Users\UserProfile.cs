﻿using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Monet.Helpers;
using Renoir.Application.Domain;

namespace Klee.Domain.Entities.UserManagement.Users;

public class UserProfile
    : DomainEntityAggregateRootBase<long> {
    #region FIELDS

    private string _displayName = "";

    #endregion

    #region PROPERTIES - IDENTIFICATION

    /// <summary>
    /// User Id (which equals the user email)
    /// </summary>
    [Required]
    public string UserId { get; internal set; } = "";
    public string FirstName { get; internal set; } = "";
    public string LastName { get; internal set; } = "";

    public string DisplayName {
        get => this.GetDisplayName();
        internal set => this._displayName = value;
    }

    public string Email { get; internal set; } = "";

    /// <summary>
    /// Is active when the user profile is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;

    /// <summary>
    /// Last time user logged in
    /// </summary>
    //public DateTime? LoggedInLastDateTimeUtc { get; internal set; } = DateTime.MinValue;

    /// <summary>
    /// Number of times user logged in
    /// </summary>
    //public long? LoggedInCount { get; internal set; } = 0;

    #endregion

    #region PROPERTIES - RELATIONS

    /// <summary>
    /// ID of the organization this user belongs to
    /// </summary>
    [Required]
    public string OrganizationId { get; internal set; } = "";
    public Organization Organization { get; internal set; }

    #endregion
    #region CONSTRUCTORS

    public UserProfile() { }

    #endregion

    #region METHODS

    //internal void UpdateForUserLoggedIn() {
    //    this.LoggedInCount = (this.LoggedInCount ?? 0) + 1;
    //    this.LoggedInLastDateTimeUtc = DateTime.UtcNow;
    //}

    #endregion

    #region METHODS

    public override string CreateEntityPartitionKey() {
        return this.UserId;
    }

    public override string GetEntityId2() {
        return this.UserId;
    }

    public override string GetEntityTypeName() {
        return "User Profile";
    }

    #endregion

    #region METHODS

    private string GetDisplayName() {
        if (this._displayName.IsNotNullOrWhiteSpace()) {
            return this._displayName;
        }
        else {
            // Init
            string displayName = this.FirstName + " " + this.LastName;

            //
            return displayName.IsNotNullOrWhiteSpace() ? displayName : "Undefined";
        }
    }

    #endregion
}