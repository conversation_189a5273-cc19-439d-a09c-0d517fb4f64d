using System.ComponentModel.DataAnnotations;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using AntDesign;
using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList;
using Microsoft.IdentityModel.Tokens;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorEdit;

public class EditOperatorViewBase : ComponentBase
{
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }


    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<EditOperatorViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter]
    public string OperatorIdEncoded { get; set; } = "";

    public string OperatorId => Base64UrlEncoder.Decode(this.OperatorIdEncoded);
    #endregion

    #region PROPERTIES
    protected EditOperatorViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {

        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!isUserOrganizationAdmin)
        {
            // Redirect to operators list if not admin
            NavigateToOperatorList();
            return;
        }

        await base.OnInitializedAsync();

    }

    protected override async Task OnParametersSetAsync()
    {

        if (string.IsNullOrEmpty(OperatorId))
        {
            NavigateToOperatorList();
            return;
        }

        // Initialize ViewModel and load operator data
        this.ViewModel = new EditOperatorViewModel(this.SrpProcessors);
        await this.ViewModel.LoadOperatorAsync(OperatorId);

        this.IsLoading = false;

        StateHasChanged();

        await base.OnParametersSetAsync();
    }
    #endregion

    #region EVENT HANDLERS
    protected void NavigateToOperatorList()
    {
        this.NavigationManager.NavigateTo(Operators.GetUri());
    }

    protected async Task HandleSubmit()
    {
        if (this.IsSubmitting)
            return;

        try
        {
            this.IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();


            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Update the operator
                await this.ViewModel.UpdateOperatorAsync();

                // Notify - dont await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Success",
                    Description = $"Operator '{this.ViewModel.FirstName} {this.ViewModel.LastName}' has been updated successfully",
                    Duration = 4.0
                });

                // Navigate back to vessels list
                NavigateToOperatorList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when updating operator with id '{OperatorId}'.", OperatorId);
            #endregion

            // Show error notification
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to update operator. Please check your input and try again.",
                Duration = 6.0
            });
        }
        finally
        {
            this.IsSubmitting = false;
            StateHasChanged();
        }
    }
    #endregion
}
