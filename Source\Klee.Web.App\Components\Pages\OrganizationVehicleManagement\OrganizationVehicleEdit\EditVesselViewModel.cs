using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Validations.Helpers;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleEdit;

public class EditVesselViewModel
{
    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES - STATIC
    // Selectable
    public static IList<string> SelectableVesselTypeDisplayNames { get; } = Enums.GetMembers<VesselTypeIds>()
        .Where(_ => _.Value != VesselTypeIds.None)
        .Select(_ => _.AsString(EnumFormat.DisplayName))
        .OrderBy(_ => _).ToList();
    #endregion

    #region PROPERTIES
    [Required]
    public string VehicleId { get; set; } = "";

    public string VehicleName { get; set; } = "";

    public string ENI { get; set; } = "";

    [Range(0, double.MaxValue)]
    public double HourlyRateInEuros { get; set; } = 0.0;

    [Range(0, double.MaxValue)]
    public double Length { get; set; } = 0.0;

    [Range(0, double.MaxValue)]
    public double Beam { get; set; } = 0.0;

    [Required]
    public VesselTypeIds VesselType { get; set; } = VesselTypeIds.None;
    #endregion


    #region CONSTRUCTORS
    public EditVesselViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task LoadOrganizationVehicleAsync(string vesselId)
    {
      
        IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
        
        // Load vessel data
        Vehicle vessel = await _srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationVehicleQuery(vesselId, queryContext));
 
        this.VehicleId = vessel.VehicleId;
        this.VehicleName = vessel.VehicleName;
        this.ENI = vessel.ENI ?? "";
        this.HourlyRateInEuros = vessel.HourlyRateInEuros;
        this.Length = vessel.Length;
        this.Beam = vessel.Beam;
        this.VesselType = vessel.VesselType;
    }

    private async Task<UpdateOrganizationVehicleGeneralCommand> NewUpdateOrganizationVehicleGeneralCommandAsync()
    {
        ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
        
        return new UpdateOrganizationVehicleGeneralCommand(this.VehicleId, commandContext)
        {
            VehicleName = this.VehicleName,
            ENI = this.ENI,
            HourlyRateInEuros = this.HourlyRateInEuros,
            Length = this.Length,
            Beam = this.Beam,
            VesselType = this.VesselType,
        };
    }

    public async Task UpdateOrganizationVehicleAsync()
    {
        // Init
        UpdateOrganizationVehicleGeneralCommand updateCommand = await this.NewUpdateOrganizationVehicleGeneralCommandAsync();
        
        // Update vessel
        await this._srpProcessors.CommandProcessor.SendAsync(updateCommand, false);
        
    }
    #endregion

    #region METHODS - VALIDATE
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        UpdateOrganizationVehicleGeneralCommandValidator commandValidator = new UpdateOrganizationVehicleGeneralCommandValidator();
        (await commandValidator.ValidateAsync(await this.NewUpdateOrganizationVehicleGeneralCommandAsync()))
            .AddTo(validationResults);

        return validationResults;
    }
    #endregion
}
