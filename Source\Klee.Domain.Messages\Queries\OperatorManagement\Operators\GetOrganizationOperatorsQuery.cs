﻿using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;

namespace Klee.Domain.Messages.Queries.OperatorManagement.Operators;

public class GetOrganizationOperatorsQuery
    : QueryBase<IReadOnlyList<OperatorListItem>>
{
    #region PROPERTIES
    public string OrganizationId { get; }
    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationOperatorsQuery(string organizationId, IQueryContext context)
        : base(context)
    {
        this.OrganizationId = organizationId;
    }
    #endregion
}