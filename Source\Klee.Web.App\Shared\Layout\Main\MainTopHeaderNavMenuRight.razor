﻿@using Microsoft.AspNetCore.Components
@using Renoir.Web.Razor.Shared.Layouts.Main

<RMainTopHeaderNavMenuRight >
    <UserBoxDropDownListItems>
        <li class="divider"></li>
        <li>
            <a role="menuitem" tabindex="-1" 
               href="/Users/<USER>"><i class="fas fa-user"></i> My User Profile</a>
        </li>
        <li>
            <a role="menuitem" tabindex="-1" 
               href="/MicrosoftIdentity/Account/SignOut"><i class="fas fa-power-off"></i> Logout</a>
        </li>
    </UserBoxDropDownListItems>
</RMainTopHeaderNavMenuRight>
