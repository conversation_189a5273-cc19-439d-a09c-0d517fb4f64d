﻿using FluentValidation;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;

namespace Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;

public class CreateOrganizationVehicleCommandValidator : AbstractValidator<CreateOrganizationVehicleCommand>
{
    public CreateOrganizationVehicleCommandValidator()
    {
        this.RuleFor(_ => _.VehicleTypeId).Must(this.VehicleTypeIdMustBeValid).WithMessage("Vehicle Type is not valid.");
        this.RuleFor(_ => _.VesselType).Must(this.VesselTypeMustBeValid).WithMessage("Vessel Type is not valid.");
    }


    private bool VehicleTypeIdMustBeValid(VehicleTypeIds vehicleTypeId)
    {
        return vehicleTypeId != VehicleTypeIds.None;
    }

    private bool VesselTypeMustBeValid(VesselTypeIds vesselType)
    {
        return vesselType != VesselTypeIds.None;
    }
}