﻿@using Klee.Web.App.Pages.StationManagement.Stations.StationCreate
@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits StationUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="@ViewModelEditContext"
                 Title="Update Station">
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Station Id" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.StationId" ReadOnly="true"/>
                <ValidationMessage For="@(() => ViewModel.StationId)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Station Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.StationName"/>
                <ValidationMessage For="@(() => ViewModel.StationName)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="ROC Id">
                <DxTextBox @bind-Text="ViewModel.RocId" />
                <ValidationMessage For="@(() => ViewModel.RocId)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Joysticks" IsRequired="true">
                <DxComboBox Data="StationUpdateGeneralDialogViewModel.SelectableJoystickTypeDisplayNames"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.StationJoystickTypeDisplayName"/>
                <ValidationMessage For="@(() => ViewModel.StationJoystickTypeDisplayName)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Hour Rate (Euros)">
                <DxSpinEdit @bind-Value="@ViewModel.HourlyRateInEuros"
                            DisplayFormat="€ #,##0.00" 
                            Increment="0.5"
                            MinValue="0" />
                <ValidationMessage For="@(() => ViewModel.HourlyRateInEuros)"/>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}