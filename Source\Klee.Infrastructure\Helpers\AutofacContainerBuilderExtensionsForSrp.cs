﻿using System.ComponentModel;
using System.Reflection;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Klee.Domain.Services.Application;
using Microsoft.Extensions.DependencyInjection;
using Paramore.Brighter;
using Paramore.Darker;
using Paramore.Darker.Builder;
using Polly;
using Polly.Registry;
using IContainer = System.ComponentModel.IContainer;

namespace Klee.Infrastructure.Helpers
{
    public static class AutofacContainerBuilderExtensionsForSrp
    {
        public static void RegisterContainerSelf(this ContainerBuilder builder)
        {
            Container container = null;
            builder.Register(c => container).AsSelf().SingleInstance();
            builder.Register(c => container).As<IContainer>().SingleInstance();
            builder.RegisterBuildCallback(c => container = (Container)c);
        }

        public static void AddSeafarServicesOfSrpAutofacModules(this ContainerBuilder containerBuilder)
        {
            // Get assemblies
            Assembly appAssembly = Assembly.GetExecutingAssembly();
            Assembly srpDomainEntitiesAssembly = Assembly.GetAssembly(typeof(Klee.Domain.Entities.VehicleManagement.Vehicles.Vehicle));
            Assembly srpDomainServicesAssembly = Assembly.GetAssembly(typeof(Klee.Domain.Services.Application.AutofacModule));
            Assembly srpDomainMessagesAssembly = Assembly.GetAssembly(typeof(Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.GetVehicleListQuery));
            Assembly srpInfrastructureAssembly = Assembly.GetAssembly(typeof(Klee.Infrastructure.Application.AutofacModule));

            // Register autofac modules
            containerBuilder.RegisterAssemblyModules(appAssembly,
                srpDomainEntitiesAssembly, srpDomainMessagesAssembly, srpDomainServicesAssembly, srpInfrastructureAssembly);
        }
    }
}