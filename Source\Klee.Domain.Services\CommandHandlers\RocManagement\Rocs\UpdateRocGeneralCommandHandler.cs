﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Messages.Commands.RocManagement.Rocs.Validators;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Services.CommandHandlers.RocManagement.Rocs.Helpers;
using Klee.Domain.Services.Repositories.RocManagement;

namespace Klee.Domain.Services.CommandHandlers.RocManagement.Rocs;

public sealed class UpdateRocGeneralCommandHandler
    : RequestHandlerAsync<UpdateRocGeneralCommand> {
    #region PROPERTIES

    private IRocSrpRepository RocSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateRocGeneralCommandHandler(IRocSrpRepository rocSrpRepository,
        IMemoryCache memoryCache) {
        RocSrpRepository = rocSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateRocGeneralCommandValidator))]
    public override async Task<UpdateRocGeneralCommand> HandleAsync(UpdateRocGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {
        //Get Roc (if it exists) 
        if (await RocSrpRepository.ExistsAsync(_ => _.RocId == command.RocId &&
                                                        _.EntityPartitionKey == command.RocId,
                command)) {
            Roc roc = await RocSrpRepository.FindAsync(_ => _.RocId == command.RocId &&
                                                                        _.EntityPartitionKey == command.RocId,
                command);

            roc.RocName = command.RocName;
            roc.OrganizationId = command.OrganizationId;
            roc.Address = command.Address;
            // Update
            await RocSrpRepository.UpdateAsync(roc, command);

            // Set Result
            command.Result.EntityId = roc.EntityId;

            // Clear Caches
            MemoryCache.RemoveRoc(command.RocId);
        }
        else {
            throw new EntityNotFoundException(
                $"Roc with Roc id '{command.RocId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}