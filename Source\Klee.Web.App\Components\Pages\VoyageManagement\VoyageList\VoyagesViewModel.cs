using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Services;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.VoyageManagement.VoyageList;

public class VoyagesViewModel
{
    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    public List<VoyageManagementListItem> Voyages { get; private set; } = new();
    #endregion

    #region CONSTRUCTORS
    public VoyagesViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadVoyagesAsync(bool forceLoad = false)
    {
        // Load Voyages
        if (forceLoad)
        {
            // Load Voyages
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            this.Voyages = (await this._srpProcessors.QueryProcessor.ExecuteAsync(new GetVoyageManagementListQuery(queryContext))).ToList();
            return true;
        }
        else
        {
            return false;
        }
    }
    #endregion
}
