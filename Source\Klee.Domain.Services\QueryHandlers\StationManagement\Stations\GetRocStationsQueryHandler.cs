﻿using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Paramore.Darker;
using System.Collections.Generic;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.StationManagement;
using Microsoft.Extensions.Caching.Memory;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System;
using EnumsNET;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.StationManagement.Stations;

public class GetRocStationsQueryHandler: QueryHandlerAsync<GetRocStationsQuery, IReadOnlyList<StationListItem>> {
            #region PROPERTIES
        private IStationSrpRepository StationSrpRepository { get; }
        #endregion

        #region CONSTRUCTORS
        public GetRocStationsQueryHandler(IStationSrpRepository stationSrpRepository)
        {
            this.StationSrpRepository = stationSrpRepository;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<StationListItem>> ExecuteAsync(GetRocStationsQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            string rocId = query.rocId;
          
            // Init
            List<StationListItem> stationListItems =
                // Get StationListItems from DB
                await this.StationSrpRepository.Entities(query)
                .Where(_ => _.RocId == rocId)
                .OrderBy(_ => _.StationId)
                .Select(_ => new StationListItem()
                {
                    EntityId = _.EntityId,
                    JoystickTypeDisplayName = _.JoystickTypeId.AsString(EnumFormat.DisplayName),
                    StationId = _.StationId,
                    StationName = _.StationName,
                    RocId = _.RocId,
                    HourlyRateInEuros = _.HourlyRateInEuros,
                    IsActive = _.IsActive ?? false
                })
                .ToListAsync(cancellationToken: cancellationToken);

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                stationListItems = stationListItems.Where(_ => _.IsActive == query.IsActive).ToList();
            }

            return stationListItems;
        }
        #endregion
}