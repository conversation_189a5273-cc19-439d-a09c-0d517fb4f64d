﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Newtonsoft.Json;
using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Entities.VehicleManagement.Vehicles
{
    public class Vehicle
        : DomainEntityAggregateRootBase<long>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES - IDENTIFICATION
        /// <summary>
        ///
        /// </summary>
        [JsonProperty]
        public VehicleTypeIds VehicleTypeId { get; internal set; } = VehicleTypeIds.None;

        /// <summary>
        /// The Seafar internal id of the vehicle
        /// </summary>
        [Required]
        public string VehicleId { get; internal set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 
        /// </summary>
        public string VehicleName { get; internal set; } = "";

        /// <summary>
        /// 
        /// </summary>
        public string ENI { get; internal set; } = "";

        /// <summary>
        ///
        /// </summary>
        public double HourlyRateInEuros { get; internal set; } = 0.0;

        /// <summary>
        /// Length of the vehicle in meters
        /// </summary>
        public double Length { get; internal set; } = 0.0;

        /// <summary>
        /// Beam (width) of the vehicle in meters
        /// </summary>
        public double Beam { get; internal set; } = 0.0;

        /// <summary>
        /// The type of vessel (enum value)
        /// </summary>
        public VesselTypeIds VesselType { get; internal set; } = VesselTypeIds.None;
        #endregion

        #region PROPERTIES - SYSTEM
        /// <summary>
        /// The software environment on which the Vehicle is used
        /// </summary>
        public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

        /// <summary>
        /// Is active when the vehicle is still in active use
        /// </summary>
        public bool? IsActive { get; internal set; } = true;
        #endregion

        #region PROPERTIES - RELATIONS 

        /// <summary>
        /// The ID of the organization that owns this vehicle
        /// </summary>
        [Required]
        public string OrganizationId { get; internal set; } = "";
        public Organization Organization { get; internal set; }
        #endregion

        #region CONSTRUCTORS
        public Vehicle()
        {
        }

        public Vehicle(string vehicleId)
        {
            this.VehicleId = vehicleId;
        }
        #endregion

        #region METHODS - ENTITY
        public override string CreateEntityPartitionKey()
        {
            return this.VehicleId;
        }

        public override string GetEntityId2()
        {
            return this.VehicleId;
        }

        public override string GetEntityTypeName()
        {
            return "Vehicle";
        }
        #endregion
    }
}
