﻿@using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList.Data
@using Microsoft.AspNetCore.Authorization
@using Renoir.Srp.Portal.Web.Application.Security
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorCreate

@page "/Operators/Management/Operators"

@inherits OperatorListViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

<RMainLayoutBody PageHeaderTitle="Operators"
                 ParentView="this"
                 CanShowButtonNew=true
                 CanShowToolbarRightContent="@IsUserSessionDataRestored"
                 OnClickNew="@OnClickNew">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Operators" />
        <RMainLayoutBodyBreadcrumbListItem Text="Overview" />
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
    </ToolbarRightMenuItems>
    <Content>
        <div class="row">
            <div class="col">
                <section class="card">
                    <header class="card-header">
                        <h2 class="card-title">Overview</h2>
                        <div class="card-actions">
                            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
                            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
                            <RCardActionToggle />
                        </div>
                    </header>
                    <div class="card-body">
                        <DxToolbar>
                            <Items>
                                <DxToolbarItem>
                                    <Template>
                                        <div class="control-value pe-2" style="width: 100px">
                                            <DxComboBox Data="@OperatorListViewModel.SelectableOperatorsMainFilterTypeIds"
                                                        ListRenderMode="@ListRenderMode.Entire"
                                                        FilteringMode="@DataGridFilteringMode.None"
                                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                                        Value="@ViewModel.OperatorsMainFilterTypeId"
                                                        ValueChanged="@((OperatorsMainFilterTypeIds operatorsMainFilterTypeId) => OnValueChanged_OperatorsMainFilterTypeId(operatorsMainFilterTypeId))"
                                                        SizeMode="SizeMode.Small" />
                                        </div>
                                    </Template>
                                </DxToolbarItem>
                            </Items>
                        </DxToolbar>

                        <DxGrid Data="@ViewModel.Operators"
                                @ref="@DxGrid"
                                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                                SelectionMode="GridSelectionMode.Multiple"
                                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                PageSize="15"
                                PageSizeSelectorVisible="true"
                                PageSizeSelectorAllRowsItemVisible="true"
                                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                                PagerNavigationMode="PagerNavigationMode.InputBox"
                                PageIndex="@ListViewProperties.DxGridPageIndex"
                                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                                SizeMode="SizeMode.Small">
                            <Columns>
                                <DxGridSelectionColumn Width="40px"
                                                       VisibleIndex="1" 
                                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                                           OnClickDelete="OnDxGrid_ClickDelete"
                                                           CanShowButtonDelete=true
                                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                                           VisibleIndex="2" />
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.OperatorId)" Caption="Operator Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="3"/>
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.OperatorFirstName)" Caption="Operator First Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4"/>
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.OperatorLastName)" Caption="Operator Last Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5"/>
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.Email)" Caption="Email" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="6"/>
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.HourlyRateInEuros)" Caption="Hourly Rate (Euros)" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="7"/>
                                <DxGridDataColumn FieldName="@nameof(OperatorListItem.OrganizationId)" Caption="Organization Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="8" />
                                @if (this.ViewModel.OperatorsMainFilterTypeId != OperatorsMainFilterTypeIds.Active)
                                {
                                    <DxGridDataColumn FieldName="@nameof(OperatorListItem.IsActive)"
                                                      Width="60px" VisibleIndex="8">
                                        <CellDisplayTemplate>
                                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                                        </CellDisplayTemplate>
                                    </DxGridDataColumn>
                                }
                                <RDxGridEmptyDataColumn/>
                            </Columns>
                        </DxGrid>
                    </div>
                </section>
            </div>
        </div>
    </Content>
</RMainLayoutBody>


<OperatorCreateDialogView @ref="OperatorCreateDialogView"
                         OnDialogViewOpened="OnOperatorCreateDialogView_Opened"
                         OnDialogViewClosed="OnOperatorCreateDialogView_Closed" />

@code {
    #region METHODS - STATIC
    public static string GetUri()
    {
        return $"/Operators/Management/Operators";
    }
    #endregion
}
