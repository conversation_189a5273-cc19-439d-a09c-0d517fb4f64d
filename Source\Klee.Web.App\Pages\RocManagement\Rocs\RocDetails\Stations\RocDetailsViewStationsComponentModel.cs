﻿using EnumsNET;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Klee.Web.App.Pages.StationManagement.Stations.StationList.Data;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Stations;

public class RocDetailsViewStationComponentModel
    : ViewModelBase<RocDetailsViewStationComponentModel>, IRViewModelWithUserSessionData<UserSessionData>
{
    #region PROPERTIES - STATIC
    // Selectable
    public static IList<StationsMainFilterTypeIds> SelectableStationsMainFilterTypeIds { get; }
        = Enums.GetMembers<StationsMainFilterTypeIds>()
               .Where(_ => _.Value != StationsMainFilterTypeIds.None)
               .Select(_ => _.Value).ToList();
    #endregion

    #region FIELDS
    private StationsMainFilterTypeIds _stationsMainFilterTypeId = StationsMainFilterTypeIds.None;
    #endregion

    #region PROPERTIES
    public StationsMainFilterTypeIds StationsMainFilterTypeId
    {
        get => this._stationsMainFilterTypeId;
        set => this.RaiseAndSetIfChanged(ref this._stationsMainFilterTypeId, value);
    }

    // Actual Loaded
    public StationsMainFilterTypeIds LoadedStationsMainFilterTypeId { get; private set; }

    //
    public IReadOnlyList<StationListItem> Stations { get; private set; } = new List<StationListItem>();
    #endregion

    #region CONSTRUCTORS
    public RocDetailsViewStationComponentModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS - OVERRIDES
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - USER SESSION DATA
    public void SetUserSessionData(UserSessionData userSessionData)
    {
        this.StationsMainFilterTypeId = userSessionData.StationsMainFilterTypeId;
    }

    public UserSessionData GetUserSessionData()
    {
        return new UserSessionData()
               {
                   StationsMainFilterTypeId = this.LoadedStationsMainFilterTypeId
               };
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadRocStationsAsync(string rocId, bool forceLoad = false)
    {
        // Adjust Properties
        this.StationsMainFilterTypeId = this.StationsMainFilterTypeId != StationsMainFilterTypeIds.None ? this.StationsMainFilterTypeId : StationsMainFilterTypeIds.All;

        // Load Stations
        if (this.LoadedStationsMainFilterTypeId != this.StationsMainFilterTypeId ||
            forceLoad)
        {
            // Load Stations
            switch (this.StationsMainFilterTypeId)
            {
                case StationsMainFilterTypeIds.Active:
                    // Load Stations
                    this.Stations = await this.SrpQueryProcessor.ExecuteAsync(new GetRocStationsQuery(rocId, this.SrpQueryContext)
                                                                           {
                                                                               IsActive = true
                                                                           });
                    break;
                case StationsMainFilterTypeIds.All:
                default:
                    // Load Stations
                    this.Stations = await this.SrpQueryProcessor.ExecuteAsync(new GetRocStationsQuery(rocId, this.SrpQueryContext));
                    break;
            }

            // Set
            this.LoadedStationsMainFilterTypeId = this.StationsMainFilterTypeId;

            // Notify
            await this.InvokeStateHasChangedOnHostAsync();

            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteStationAsync(string stationId, string rocId)
    {
        try
        {
            // Delete Station
            await this.SrpCommandProcessor.SendAsync(new DeleteStationCommand(stationId,
                                                                          this.SrpCommandContext));

            // Load Stations
            await this.LoadRocStationsAsync(rocId, forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion
}