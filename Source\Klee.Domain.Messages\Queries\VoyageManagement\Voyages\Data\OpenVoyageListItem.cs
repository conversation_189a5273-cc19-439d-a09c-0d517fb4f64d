using System;
using System.Collections.Generic;
using System.Linq;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;

namespace Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;

public class OpenVoyageListItem
{
    #region PROPERTIES - CORE
    public Guid VoyageId { get; set; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public string Description { get; set; } = "";
    public List<QualificationTypeIds> RequiredQualifications { get; set; } = new();
    #endregion

    #region PROPERTIES - VESSEL INFORMATION
    public string VehicleId { get; set; } = "";
    public string VehicleName { get; set; } = "";
    public string VehicleENI { get; set; } = "";
    public VehicleTypeIds VehicleType { get; set; } = VehicleTypeIds.None;
    public string VehicleTypeDisplayName { get; set; } = "";
    public double VehicleLength { get; set; } = 0.0;
    public double VehicleBeam { get; set; } = 0.0;
    public double VehicleHourlyRateInEuros { get; set; } = 0.0;
    public string VehicleOrganizationId { get; set; } = "";
    public string VehicleOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - BOOKING ORGANIZATION
    public string BookingOrganizationId { get; set; } = "";
    public string BookingOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - COMPUTED
    public string VesselDisplayName => $"{VehicleName} ({VehicleOrganizationName})";
    public string DateRangeDisplay => StartDateTime.Date == EndDateTime.Date 
        ? $"{StartDateTime:MMM dd, yyyy} ({StartDateTime:HH:mm} - {EndDateTime:HH:mm})"
        : $"{StartDateTime:MMM dd, yyyy HH:mm} - {EndDateTime:MMM dd, yyyy HH:mm}";
    public TimeSpan Duration => EndDateTime - StartDateTime;
    public string DurationDisplay => Duration.TotalDays >= 1 
        ? $"{Duration.Days}d {Duration.Hours}h {Duration.Minutes}m"
        : $"{Duration.Hours}h {Duration.Minutes}m";
    public string QualificationsDisplay => RequiredQualifications.Any() 
        ? string.Join(", ", RequiredQualifications.Select(q => q.ToString().Replace("_", " ")))
        : "No specific qualifications required";
    #endregion
}
