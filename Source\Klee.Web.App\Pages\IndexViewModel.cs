﻿using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Pages
{
    public class IndexViewModel
        : ViewModelBase<IndexViewModel>
    {
        #region CONSTRUCTORS
        public IndexViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            //
            await base.OnParametersSetAsync();
        }
        #endregion
    }
}
