﻿using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.RocManagement.Rocs {
    public class CreateRocCommand : CommandBase {
        #region RESULT CLASS

        public class CommandResult {
            public long EntityId { get; set; }
        }

        #endregion

        #region PROPERTIES
        public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;
        public string RocName { get; set; } = "";
        public string RocAddress { get; set; } = "";
        public string OrganizationId { get; }

        // Result
        public CommandResult Result { get; } = new CommandResult();

        #endregion

        #region CONSTRUCTORS

        public CreateRocCommand(SoftwareEnvironmentIds softwareEnvironmentId,
            string organizationId,
            ICommandContext commandContext)
            : base(commandContext) {
            this.SoftwareEnvironmentId = softwareEnvironmentId;
            this.OrganizationId = organizationId;
        }

        #endregion
    }
}