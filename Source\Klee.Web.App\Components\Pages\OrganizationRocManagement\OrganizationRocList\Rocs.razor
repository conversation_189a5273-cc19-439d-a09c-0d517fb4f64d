@layout OrganizationViewLayout
@using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocCreate
@using Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocEdit
@using AntDesign
@using System.Linq.Expressions
@using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs.Data;
@using Klee.Web.App.Components.UI
@using EnumsNET

@page "/my-assets/rocs"

@inherits RocsViewBase

<Modal Title="Delete ROC"
       Visible="@this.DeleteModalVisible"
       OnOk="@this.OnConfirmDelete"
       OnCancel="@this.OnCancelDelete"
       OkText="@("Delete")"
       CancelText="@("Cancel")"
       OkButtonProps="@(new ButtonProps { Danger = true })">
    <div>
        <p>Are you sure you want to delete ROC '<strong>@CurrentRocName</strong>'?</p>
    </div>
</Modal>

<div class="container py-3">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">My ROCs</h1>

        @if(this.IsUserOrganizationAdmin){

            <Button Type="@ButtonType.Primary"
                    Class="@TailwindStyleStrings.Button.Primary"
                    OnClick="OnClick_AddRoc">
                <i class="fas fa-plus h-4 w-4 mr-2"></i>
                Add ROC
            </Button>
        }
   
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="overflow-x-auto">
            <Table TItem="OrganizationRocListItem"
                    DataSource="@ViewModel.Rocs"
                    Class="@TailwindStyleStrings.Table.Container"
                    ScrollX="1100">
                <PropertyColumn Property="c => c.RocName" Title="Name" Sortable Filterable>
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-building h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span class="font-medium">@context.RocName</span>
                        </div>
                    </Template>
                </PropertyColumn>
                <PropertyColumn Property="c => c.Address" Title="Address" Filterable Width="200" />
                <PropertyColumn Property="c => c.Location" Title="Location" Sortable Filterable Width="150" />
                <PropertyColumn Property="c => c.PostalCode" Title="Postal Code" Sortable Width="120" />
                <PropertyColumn Property="c => c.CountryDisplayName" Title="Country" Sortable Filterable Width="150">
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-flag h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span>@context.CountryDisplayName</span>
                        </div>
                    </Template>
                </PropertyColumn>
                <ActionColumn Title="Actions" Width="120" Fixed="ColumnFixPlacement.Right">
                    @if(this.IsUserOrganizationAdmin){
                        <Space>
                            <SpaceItem>
                                <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                            OnClick="@(() => this.OnClick_EditRoc(context.RocId))">
                                        <i class="@($"fas fa-pencil-alt h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <span class="sr-only">Edit</span>
                                </Button>
                            </SpaceItem>
                            <SpaceItem>
                                <Button Type="@ButtonType.Link"
                                            Class="@($"{TailwindStyleStrings.Button.Ghost} h-8 w-8 p-0")"
                                            OnClick="@(() => OnClick_DeleteRoc(context.RocId, context.RocName))">
                                        <i class="@($"fas fa-trash h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                        <span class="sr-only">Delete</span>
                                </Button>
                            </SpaceItem>
                        </Space>
                    }
                </ActionColumn>
            </Table>
           
        </div>
    </Card>
</div>

@code {

    #region METHODS - STATIC
    public static string GetUri()
    {
        return "/my-assets/rocs";
    }
    #endregion

}
