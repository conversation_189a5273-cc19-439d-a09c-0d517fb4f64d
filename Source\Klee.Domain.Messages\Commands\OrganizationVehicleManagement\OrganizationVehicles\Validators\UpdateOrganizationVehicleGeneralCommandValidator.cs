﻿using FluentValidation;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;

namespace Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;

public class UpdateOrganizationVehicleGeneralCommandValidator : AbstractValidator<UpdateOrganizationVehicleGeneralCommand>
{
    public UpdateOrganizationVehicleGeneralCommandValidator()
    {
        this.RuleFor(_ => _.VehicleId).NotNull();
        this.RuleFor(_ => _.VehicleId).NotEmpty();
        this.RuleFor(_ => _.VesselType).Must(this.VesselTypeMustBeValid).WithMessage("Vessel Type is not valid.");
    }

    private bool VesselTypeMustBeValid(VesselTypeIds vesselType)
    {
        return vesselType != VesselTypeIds.None;
    }
}