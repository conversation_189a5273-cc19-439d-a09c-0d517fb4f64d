﻿using Klee.Domain.Entities.RocManagement.Rocs.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs;

public class CreateOrganizationRocCommand : CommandBase {
    #region RESULT CLASS

    public class CommandResult {
        public long EntityId { get; set; }
    }

    #endregion

    #region PROPERTIES
    public string RocName { get; set; } = "";
    public string Address { get; set; } = "";
    public string Location { get; set; } = "";
    public string PostalCode { get; set; } = "";
    public CountryIds Country { get; set; } = CountryIds.None;

    // Result
    public CommandResult Result { get; } = new ();

    #endregion

    #region CONSTRUCTORS

    public CreateOrganizationRocCommand(ICommandContext commandContext)
        : base(commandContext) {
    }

    #endregion
}