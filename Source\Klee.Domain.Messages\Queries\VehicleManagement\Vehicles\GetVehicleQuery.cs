﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Renoir.Application.Messages.Queries.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Domain.Messages.Queries.VehicleManagement.Vehicles
{
    public class GetVehicleQuery
        : QueryBase<Vehicle>
    {
        #region PROPERTIES
        public string VehicleId { get; }

        public bool AllowCached { get; set; } = false;
        #endregion

        #region CONSTRUCTORS
        public GetVehicleQuery(string vehicleId,
                               IQueryContext context)
            : base(context)
        {
            this.VehicleId = vehicleId;
        }
        #endregion
    }
}
