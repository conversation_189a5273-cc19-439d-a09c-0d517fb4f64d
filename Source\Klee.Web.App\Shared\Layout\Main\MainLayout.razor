﻿@using Microsoft.AspNetCore.Components
@using Renoir.Web.Razor.Shared.Layouts.Main
@using Microsoft.AspNetCore.Components.Authorization
@inherits MainLayoutBase

<AuthorizeView>
    <Authorized Context="AuthorizedContextMain">
        <RMainLayout>
            <MainTopHeaderNavMenuLeftContent>
                <MainTopHeaderNavMenuLeft />
            </MainTopHeaderNavMenuLeftContent>

            <MainTopHeaderNavMenuRightContent>
                <MainTopHeaderNavMenuRight />
            </MainTopHeaderNavMenuRightContent>

            <MainNavMenuLeftContent>
                <MainNavMenuLeft />
            </MainNavMenuLeftContent>

            <MainNavMenuRightContent>
                <MainNavMenuRight />
            </MainNavMenuRightContent>

            <MainBodyContent>
                <ErrorBoundary @ref="_errorBoundary" >
                    <ChildContent>
                        @Body
                    </ChildContent>
                    <ErrorContent>
                        <section class="body-error error-inside">
                            <div class="center-error">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="main-error mb-3">
                                            <h2 class="error-code text-dark text-center font-weight-semibold m-0">500 <i class="fas fa-file"></i></h2>
                                            <p class="error-explanation text-center">We're sorry, something went wrong. <br />If this issue persists, <NAME_EMAIL></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </ErrorContent>
                </ErrorBoundary>
            </MainBodyContent>
        </RMainLayout>
    </Authorized>
    <NotAuthorized>
        <section class="body-error error-outside">
            <div class="center-error">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="main-error mb-3">
                            <h2 class="error-code text-dark text-center font-weight-semibold m-0">401 <i class="fas fa-exclamation-triangle"></i></h2>
                            <p class="error-explanation text-center">We're sorry, but the user is not authorized to access the Seafar portal!</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </NotAuthorized>
</AuthorizeView>

