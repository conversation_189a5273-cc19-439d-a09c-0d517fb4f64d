﻿@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails.Data
@using Microsoft.AspNetCore.Authorization
@using Microsoft.IdentityModel.Tokens
@using Renoir.Srp.Portal.Web.Application.Security
@using Microsoft.AspNetCore.Components.Authorization
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateGeneral
@using Monet.Helpers
@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateQualifications

@inherits OperatorDetailsViewBase
@attribute [Authorize(Policy = @PolicyNames.AdminsApp)]

@page "/Operators/Management/Operator/{OperatorIdEncoded}"

<RMainLayoutBody @ref="MainLayoutBody"
                 PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 OnClickDelete="@OnClickDelete"
                 CanShowButtonDelete=true
                 OnActiveTabChanged="@OnMainLayoutBody_ActiveTabChanged"
                 ActiveTabId="@DetailsViewProperties.ActiveTabId">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Operators"/>
        <RMainLayoutBodyBreadcrumbListItem Text="Details"/>
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarRightMenuItems>
    <TabNavItems>
        <RTabNavItem TabId="@OperatorDetailsViewTabIds.General" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemGeneral_IsActiveChanged">GENERAL</RTabNavItem>
    </TabNavItems>
    <TabContentItems>
        <RTabContentItem @ref="@TabContentItemGeneral"
                         TabId="@OperatorDetailsViewTabIds.General" MinHeight="200">
            <div class="row">
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Identification</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenOperatorUpdateGeneralDialogView"
                                                         CanShow=true />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="Operator Id" Value="@ViewModel.OperatorId" />
                                    <RControlTextValueDisplay Label="Operator First Name" Value="@ViewModel.OperatorFirstName" />
                                    <RControlTextValueDisplay Label="Operator Last Name" Value="@ViewModel.OperatorLastName" />
                                    <RControlTextValueDisplay Label="Email" Value="@ViewModel.Email" />
                                    <RControlTextValueDisplay Label="Hourly Rate (Euros)" Value="@ViewModel.HourlyRateInEuros" />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Qualifications</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenOperatorUpdateQualificationsDialogView"
                                                         CanShow=true />
                                        <RCardActionToggle/>
                                    </div>
                                </header>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        @foreach (QualificationTypeIds qualification in this.ViewModel.Qualifications) {
                                            <li class="list-group-item">@qualification.GetDisplayName()</li>
                                        }
                                    </ul>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                                <section class="card">
                                    <header class="card-header">
                                        <h2 class="card-title">System</h2>
                                        <div class="card-actions">
                                            <RCardActionToggle />
                                        </div>
                                    </header>
                                    <div class="card-body">
                                        <RControlTextValueDisplay Label="Environment" Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                                        <RControlCheckBoxValueDisplay Label="Active" Value="@ViewModel.IsActive" />
                                    </div>
                                </section>
                            </AuthorizeView>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
    </TabContentItems>
    <Content>
    </Content>
</RMainLayoutBody>

<OperatorUpdateGeneralDialogView @ref="OperatorUpdateGeneralDialogView"
                                 OnDialogViewOpened="OnOperatorUpdateGeneralDialogView_Opened"
                                 OnDialogViewClosed="OnOperatorUpdateGeneralDialogView_Closed" />

<OperatorUpdateQualificationsDialogView @ref="OperatorUpdateQualificationsDialogView"
                                 OnDialogViewOpened="OnOperatorUpdateQualificationsDialogView_Opened"
                                 OnDialogViewClosed="OnOperatorUpdateQualificationsDialogView_Closed" />
@code {

    #region METHODS - STATIC
    public static string GetUri(string operatorId)
    {
        return $"/Operators/Management/Operator/{Base64UrlEncoder.Encode(operatorId)}";
    }
    #endregion

}
