using System;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations
{
    public class CreateOrganizationCommand : CommandBase
    {
        #region RESULT CLASS
        public class CommandResult
        {
            public long EntityId { get; set; }
        }
        #endregion

        #region PROPERTIES
        public SoftwareEnvironmentIds SoftwareEnvironmentId { get; }
        public string Name { get; }
        public string Code { get; set; } = "";
        public string Description { get; set; } = "";
        public string ContactEmail { get; set; } = "";
        public string ContactPhone { get; set; } = "";
        public string Address { get; set; } = "";

        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public CreateOrganizationCommand(SoftwareEnvironmentIds softwareEnvironmentId,
                                    string organizationName,
                                    ICommandContext commandContext)
            : base(commandContext)
        {
            this.SoftwareEnvironmentId = softwareEnvironmentId;
            this.Name = organizationName;
        }
        #endregion
    }
} 