﻿using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails;
using Klee.Web.App.Pages.RocManagement.Rocs.RocList;
using Klee.Web.App.Pages.RocManagement.Rocs.RocList.Data;
using Microsoft.AspNetCore.Components;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails.Rocs;

    public partial class OrganizationDetailsViewRocsComponentBase 
        : LayoutBodyListDxGridViewBase<OrganizationDetailsViewRocComponentModel, RocListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        [Parameter]
        public string OrganizationId { get; set; } = "";

        #endregion

        #region CONSTRUCTORS
        protected OrganizationDetailsViewRocsComponentBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            //
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new OrganizationDetailsViewRocComponentModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Rocs
                if (await this.ViewModel.LoadOrganizationRocsAsync(this.OrganizationId))
                {
                    //...
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            RocListItem rocListItem = e.GetDataItem() as RocListItem ?? new RocListItem();
            string rocId = rocListItem.RocId ?? "";

            try
            {
                // View Roc
                await this.NavigateToAsync(RocDetailsView.GetUri(rocId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when going to view details of Roc '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            RocListItem rocListItem = e.GetDataItem() as RocListItem ?? new RocListItem();
            string rocId = rocListItem.RocId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Roc?",
                                                                        $"Delete Roc with id '{rocId}'?") == RDialogResult.Ok)
                {
                    // Delete Roc
                    await this.ViewModel.DeleteRocAsync(rocId, this.OrganizationId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Roc with id '{rocId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting Roc with id '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        #endregion

        #region EVENT HANDLERS
        //protected async Task OnClick_MainFilterNavItemAllRocs(MouseEventArgs e)
        //{
        //    // Set
        //    this.ViewModel.RocsMainFilterTypeId = RocsMainFilterTypeIds.All;

        //    // Load
        //    // Todo: Could load be based on property change of RocsMainFilterType?
        //    await this.ViewModel.LoadRocsAsync();
        //}

        public async void OnValueChanged_RocsMainFilterTypeId(RocsMainFilterTypeIds rocsMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.RocsMainFilterTypeId = rocsMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of RocsMainFilterType?
                await this.ViewModel.LoadOrganizationRocsAsync(this.OrganizationId);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(RocListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion
    }