using System;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;

namespace Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;

public class InvoiceListItem
{
    #region PROPERTIES - IDENTIFICATION
    public Guid VoyageInvoiceId { get; set; }
    public Guid VoyageId { get; set; }
    public string BookingOrganizationId { get; set; } = "";
    public string OperatorOrganizationId { get; set; } = "";
    #endregion

    #region PROPERTIES - VOYAGE DETAILS
    public string VesselName { get; set; } = "";
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public TimeSpan Duration => EndDateTime - StartDateTime;
    #endregion

    #region PROPERTIES - OPERATOR DETAILS
    public string OperatorId { get; set; } = "";
    public string OperatorFirstName { get; set; } = "";
    public string OperatorLastName { get; set; } = "";
    public string OperatorFullName => $"{OperatorFirstName} {OperatorLastName}".Trim();
    public string OperatorOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - BOOKING ORGANIZATION
    public string BookingOrganizationName { get; set; } = "";
    #endregion

    #region PROPERTIES - INVOICE DETAILS
    public double TotalAmountInEuros { get; set; }
    public VoyageInvoiceStatus Status { get; set; }
    public DateTime? PaymentDate { get; set; }
    public DateTime CreatedDate { get; set; }
    #endregion

    #region PROPERTIES - DIRECTION
    /// <summary>
    /// Determines if this is an incoming or outgoing invoice for the current user's organization
    /// </summary>
    public string GetInvoiceDirection(string currentUserOrganizationId)
    {
        return currentUserOrganizationId == BookingOrganizationId ? "Outgoing" : "Incoming";
    }

    public bool IsIncoming(string currentUserOrganizationId)
    {
        return currentUserOrganizationId == OperatorOrganizationId;
    }

    public bool IsOutgoing(string currentUserOrganizationId)
    {
        return currentUserOrganizationId == BookingOrganizationId;
    }
    #endregion
}
