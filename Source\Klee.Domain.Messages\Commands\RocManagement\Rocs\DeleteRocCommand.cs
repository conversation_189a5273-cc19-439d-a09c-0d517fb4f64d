﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.RocManagement.Rocs;

public class DeleteRocCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
    }
    #endregion

    #region PROPERTIES
    public string RocId { get; }

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public DeleteRocCommand(string rociD,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.RocId = rociD;
    }
    #endregion
}