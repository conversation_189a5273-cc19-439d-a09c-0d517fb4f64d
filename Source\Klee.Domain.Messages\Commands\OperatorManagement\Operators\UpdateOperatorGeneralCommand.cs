﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators;

public class UpdateOperatorGeneralCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string OperatorId { get; }

    public string OperatorFirstName { get; set; } = "";
    public string OperatorLastName { get; set; } = "";
    public string OperatorEmail { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public string OrganizationId { get; }
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateOperatorGeneralCommand(string operatorId,
        string organizationId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.OperatorId = operatorId;
        this.OrganizationId = organizationId;
    }
    #endregion
}