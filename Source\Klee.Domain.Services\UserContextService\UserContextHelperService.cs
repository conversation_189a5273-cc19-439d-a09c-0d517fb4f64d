﻿using System;
using Klee.Domain.Entities.UserManagement.Users;
using System.Security.Claims;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.UserManagement.Users;
using Monet.Helpers;
using Renoir.Application.Security.Helpers;


namespace Klee.Domain.Services.UserContextService;

public class UserContextHelperService : IUserContextHelperService
{
    private readonly ISrpProcessors _srpProcessors;

    public UserContextHelperService(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }



    public async Task<UserProfile?> GetUserProfileByClaimsAsync(ClaimsPrincipal userClaimsPrincipal)
    {

        string? email = userClaimsPrincipal.FindFirst("preferred_username")?.Value;
        if (string.IsNullOrEmpty(email))
            return null;

        // Get profile
        UserProfile profile = await _srpProcessors.QueryProcessor.ExecuteAsync(new GetUserProfileQuery(email, _srpProcessors.QueryContext));
        return profile;
    }


    public async Task<string> GetUserOrganizationIdByClaimsAsync(ClaimsPrincipal userClaimsPrincipal)
    {
        UserProfile? profile = await GetUserProfileByClaimsAsync(userClaimsPrincipal);
        return profile == null ? "" : profile.OrganizationId;
    }

    public bool IsUserOrganizationAdmin(ClaimsPrincipal userClaimsPrincipal)
    {
        return userClaimsPrincipal.IsUserInternal() || userClaimsPrincipal.IsAdminApp() || userClaimsPrincipal.IsAdminSystem();
    }

    public async Task<bool> IsUserOrganizationAdminForOrganizationAsync(ClaimsPrincipal userClaimsPrincipal,
        string orgId) {
        string userOrgId = await GetUserOrganizationIdByClaimsAsync(userClaimsPrincipal);
        return (userOrgId.IsNotNullOrEmpty() && userClaimsPrincipal.IsUserInternal() && userOrgId == orgId)
               ||userClaimsPrincipal.IsAdminApp()
               || userClaimsPrincipal.IsAdminSystem();
}
}