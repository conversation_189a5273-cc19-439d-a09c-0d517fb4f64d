﻿using Klee.Domain.Entities.Entities.StationManagement.Stations;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.StationManagement;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.StationManagement;

public class StationSrpRepository
    : EfDomainEntityRepository<AppSrpDbContext, Station, long>, IStationSrpRepository
{
    public StationSrpRepository(AppSrpDbContext dbContext,
        IDbExceptionConverter dbExceptionConverter,
        ILogger<StationSrpRepository> logger)
        : base(dbContext, dbExceptionConverter, (ILogger)logger)
    {
    }

    protected override void Dispose()
    {
        base.Dispose();
    }
}