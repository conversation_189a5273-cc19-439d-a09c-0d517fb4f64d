﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations.Helpers
{
    public static class IMemoryCacheExtensions
    {
        public static void RemoveOrganization(this IMemoryCache memoryCache,
                                         string organizationId)
        {
            memoryCache.Remove(MemoryCacheIds.GetOrganizationListCacheId);
            memoryCache.Remove(MemoryCacheIds.GetOrganizationCacheId(organizationId));
        }
    }
}
