﻿@using Klee.Domain.Entities.StationManagement.Stations
@using Klee.Domain.Messages.Queries.StationManagement.Stations.Data
@using Klee.Web.App.Pages.StationManagement.Stations.StationList.Data
@using Klee.Web.App.Pages.StationManagement.Stations.StationList


@inherits RocDetailsViewStationsComponentBase

<section class="card">
    <header class="card-header">
        <h2 class="card-title">Overview</h2>
        <div class="card-actions">
            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
            <RCardActionToggle />
        </div>
    </header>
    <div class="card-body">
        <DxToolbar>
            <Items>
                <DxToolbarItem>
                    <Template>
                        <div class="control-value pe-2" style="width: 100px">
                            <DxComboBox Data="@RocDetailsViewStationComponentModel.SelectableStationsMainFilterTypeIds"
                                        ListRenderMode="@ListRenderMode.Entire"
                                        FilteringMode="@DataGridFilteringMode.None"
                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                        Value="@ViewModel.StationsMainFilterTypeId"
                                        ValueChanged="@((StationsMainFilterTypeIds stationsMainFilterTypeId) => OnValueChanged_StationsMainFilterTypeId(stationsMainFilterTypeId))"
                                        SizeMode="SizeMode.Small" />
                        </div>
                    </Template>
                </DxToolbarItem>
            </Items>
        </DxToolbar>

        <DxGrid Data="@this.ViewModel.Stations"
                @ref="@DxGrid"
                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                SelectionMode="GridSelectionMode.Multiple"
                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                PageSize="15"
                PageSizeSelectorVisible="true"
                PageSizeSelectorAllRowsItemVisible="true"
                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                PagerNavigationMode="PagerNavigationMode.InputBox"
                PageIndex="@ListViewProperties.DxGridPageIndex"
                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                SizeMode="SizeMode.Small">
            <Columns>
                <DxGridSelectionColumn Width="40px"
                                       VisibleIndex="1"
                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                           OnClickDelete="OnDxGrid_ClickDelete"
                                           CanShowButtonDelete=true
                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                           VisibleIndex="2" />
                <DxGridDataColumn FieldName="@nameof(StationListItem.StationId)" Caption="Station Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="3" />
                <DxGridDataColumn FieldName="@nameof(StationListItem.StationName)" Caption="Station Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4" />
                <DxGridDataColumn FieldName="@nameof(StationListItem.RocId)" Caption="Roc Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5" />
                <DxGridDataColumn FieldName="@nameof(StationListItem.JoystickTypeDisplayName)" Caption="Joystick Type" Width="100px" VisibleIndex="6" />
                <DxGridDataColumn FieldName="@nameof(StationListItem.HourlyRateInEuros)" Caption="Hourly Rate (Euro)" Width="100px" VisibleIndex="7" />

                @if (this.ViewModel.StationsMainFilterTypeId != StationsMainFilterTypeIds.Active)
                {
                    <DxGridDataColumn FieldName="@nameof(StationListItem.IsActive)"
                                      Width="60px" VisibleIndex="8">
                        <CellDisplayTemplate>
                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                }
                <RDxGridEmptyDataColumn />
            </Columns>
        </DxGrid>

    </div>
</section>

@code {
}
