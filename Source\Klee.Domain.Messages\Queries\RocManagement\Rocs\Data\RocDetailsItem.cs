﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;

namespace Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;

public class RocDetailsItem {
    #region PROPERTIES
    public string RocId { get; set; } = "";
    public string RocName { get;  set; } = "";
    public string SoftwareEnvironmentDisplayName { get;  set; } = "";
    public List<StationListItem> Stations { get;  set; } = new();
    public bool IsActive { get;  set; }
    public string CreatedBy { get;  set; } = "";
    public string CreatedByName { get;  set; } = "";
    public string CreatedDateTimeRstAsString { get;  set; } = "";
    public string ModifiedBy { get;  set; } = "";
    public string ModifiedByName { get;  set; } = "";
    public string ModifiedDateTimeRstAsString { get;  set; } = "";
    #endregion
}