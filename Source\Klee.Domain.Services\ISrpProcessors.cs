﻿using System.Threading.Tasks;
using Monet.Common;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.General;

namespace Klee.Domain.Services
{
    public interface ISrpProcessors
        : IDisposableObject
    {
        #region PROPERTIES
        IAmACommandProcessor CommandProcessor { get; }
        IQueryProcessor QueryProcessor { get; }

        IMessageContextProvider MessageContextProvider { get; }

        Renoir.Application.Messages.Events.Common.IEventContext EventContext { get; }
        Renoir.Application.Messages.Commands.Common.ICommandContext CommandContext { get; }
        Renoir.Application.Messages.Queries.Common.IQueryContext QueryContext { get; }
        #endregion

        #region METHODS
        Task<Renoir.Application.Messages.Events.Common.IEventContext> GetEventContextAsync();
        Task<Renoir.Application.Messages.Commands.Common.ICommandContext> GetCommandContextAsync();
        Task<Renoir.Application.Messages.Queries.Common.IQueryContext> GetQueryContextAsync();
        #endregion
    }
}
