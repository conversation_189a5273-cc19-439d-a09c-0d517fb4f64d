﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;

namespace Klee.Domain.Services.CommandHandlers.OrganizationRocManagement.OrganizationRocs;

public sealed class UpdateOrganizationRocGeneralCommandHandler
    : RequestHandlerAsync<UpdateOrganizationRocGeneralCommand> {
    #region PROPERTIES

    private IRocSrpRepository RocSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOrganizationRocGeneralCommandHandler(IRocSrpRepository rocSrpRepository) {
        RocSrpRepository =rocSrpRepository;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationRocGeneralCommandValidator))]
    public override async Task<UpdateOrganizationRocGeneralCommand> HandleAsync(UpdateOrganizationRocGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        //Get Roc (if it exists) 
        if (await RocSrpRepository.ExistsAsync(_ => _.RocId == command.RocId &&
                                                        _.EntityPartitionKey == command.RocId,
                command)) {

            Roc roc = await RocSrpRepository.FindAsync(_ => _.RocId == command.RocId &&
                                                                        _.EntityPartitionKey == command.RocId,
                command);

            roc.RocName = command.RocName;
            roc.Address = command.Address;
            roc.Location = command.Location;
            roc.PostalCode = command.PostalCode;
            roc.Country = command.Country;
            // Update
            await RocSrpRepository.UpdateAsync(roc, command);

            // Set Result
            command.Result.EntityId = roc.EntityId;

        }
        else {
            throw new EntityNotFoundException(
                $"Roc with Roc id '{command.RocId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}