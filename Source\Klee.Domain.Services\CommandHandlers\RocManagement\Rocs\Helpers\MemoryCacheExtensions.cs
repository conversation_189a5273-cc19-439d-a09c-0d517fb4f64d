﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.RocManagement.Rocs.Helpers;

public static class MemoryCacheExtensions {
    public static void RemoveRoc(this IMemoryCache memoryCache,
        string rocId)
    {
        memoryCache.Remove(MemoryCacheIds.GetRocListCacheId);
        memoryCache.Remove(MemoryCacheIds.GetRocCacheId(rocId));
    }
}