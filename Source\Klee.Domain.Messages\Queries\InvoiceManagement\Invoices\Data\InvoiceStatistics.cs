namespace Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;

public class InvoiceStatistics
{
    #region PROPERTIES
    /// <summary>
    /// Number of voyages posted by the organization this month
    /// </summary>
    public int VoyagesPostedThisMonth { get; set; }

    /// <summary>
    /// Total hours of voyages this month for the organization
    /// </summary>
    public double TotalHoursThisMonth { get; set; }

    /// <summary>
    /// Money earned this month for the organization (incoming invoices)
    /// </summary>
    public double MoneyEarnedThisMonth { get; set; }

    /// <summary>
    /// Total money earned (all time) for the organization (incoming invoices)
    /// </summary>
    public double TotalMoneyEarned { get; set; }
    #endregion
}
