﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators;

public class DeleteOperatorCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
    }
    #endregion

    #region PROPERTIES
    public string OperatorId { get; }

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public DeleteOperatorCommand(string operatorId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.OperatorId = operatorId;
    }
    #endregion
}