using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Security;


namespace Klee.Domain.Services.CommandHandlers.InvoiceManagement.Invoices
{
    public sealed class UpdateVoyageInvoiceStatusCommandHandler
        : RequestHandlerAsync<UpdateVoyageInvoiceStatusCommand>
    {
        #region PROPERTIES
        private IVoyageInvoiceSrpRepository VoyageInvoiceSrpRepository { get; }
        private IUserContextHelperService UserContextHelperService { get; }
        #endregion

        #region CONSTRUCTORS
        public UpdateVoyageInvoiceStatusCommandHandler(IVoyageInvoiceSrpRepository voyageInvoiceSrpRepository
        , IUserContextHelperService userContextHelperService)
        {
            VoyageInvoiceSrpRepository = voyageInvoiceSrpRepository;
            UserContextHelperService = userContextHelperService;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<UpdateVoyageInvoiceStatusCommand> HandleAsync(UpdateVoyageInvoiceStatusCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
            // Find VoyageInvoice
            VoyageInvoice voyageInvoice = await VoyageInvoiceSrpRepository.FindAsync(_ => _.VoyageInvoiceId == command.VoyageInvoiceId &&
                                                                                          _.EntityPartitionKey == command.VoyageInvoiceId.ToString(), command);

            if (voyageInvoice != null)
            {
                // Authorization check: Only operator organization can mark invoices as paid
                string currentUserOrganizationId =
                    await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(command.Context.User);

                if (string.IsNullOrEmpty(currentUserOrganizationId))
                {
                    throw new UnauthorizedAccessException("User organization not found.");
                }

                if (voyageInvoice.OperatorOrganizationId != currentUserOrganizationId)
                {
                    throw new UnauthorizedAccessException("This organization is not authorized to update this invoice status.");
                }

                // Update status and payment date
                voyageInvoice.Status = command.Status;
                voyageInvoice.PaymentDate = command.PaymentDate?.ToUniversalTime();

                // Update
                await VoyageInvoiceSrpRepository.UpdateAsync(voyageInvoice, command);

                // Set Result
                command.Result.VoyageInvoiceId = voyageInvoice.VoyageInvoiceId;
            }
            else
            {
                throw new EntityNotFoundException(
                    $"Voyage Invoice with id '{command.VoyageInvoiceId}' not found.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}
