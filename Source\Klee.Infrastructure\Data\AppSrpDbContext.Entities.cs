﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Infrastructure.Data.EntityTypeConfigurations.VehicleManagement;
using Klee.Infrastructure.Data.EntityTypeConfigurations.OrganizationManagement;
using Klee.Infrastructure.Data.EntityTypeConfigurations.RocManagement;
using Klee.Infrastructure.Data.EntityTypeConfigurations.StationManagement;
using Microsoft.EntityFrameworkCore;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Infrastructure.Data.EntityTypeConfigurations.OperatorManagement;
using Klee.Infrastructure.Data.EntityTypeConfigurations.UserManagement.Users;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Klee.Infrastructure.Data.EntityTypeConfigurations.VoyageManagement;
using Klee.Infrastructure.Data.EntityTypeConfigurations.InvoiceManagement;

namespace Klee.Infrastructure.Data
{
    public partial class AppSrpDbContext
    {
        #region PROPERTIES - ENTITIES
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<Organization> Organizations { get; set; }
        public DbSet<Roc> Rocs { get; set; }
        public DbSet<Station> Stations { get; set; }
        public DbSet<UserProfile> Users { get; set; }
        public DbSet<Operator> Operators { get; set; }
        public DbSet<Voyage> Voyages { get; set; }
        public DbSet<VoyageInvoice> VoyageInvoices { get; set; }
        #endregion

        #region METHODS - OVERRIDE
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableSensitiveDataLogging()
                          //.LogTo(Console.WriteLine)
                          .EnableDetailedErrors();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply Configurations
            modelBuilder.ApplyConfiguration(new VehicleEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new RocEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new StationEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new UserProfileEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new OperatorEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new VoyageEntityTypeConfiguration());
            modelBuilder.ApplyConfiguration(new VoyageInvoiceEntityTypeConfiguration());
        }
        #endregion
    }
}
