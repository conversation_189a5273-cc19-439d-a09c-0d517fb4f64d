﻿using Klee.Domain.Entities.Entities.StationManagement.Stations;
using Klee.Domain.Entities.StationManagement.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.StationManagement;

public class StationEntityTypeConfiguration : IEntityTypeConfiguration<Station>
{
    public void Configure(EntityTypeBuilder<Station> builder)
    {
        //builder.AddCosmosDbProperties();
        builder.HasIndex(_ => _.StationId)
            .IsUnique();
        builder.Property(_ => _.StationId)
            .IsRequired();
        builder.Property(_ => _.StationName)
            .IsRequired();
        builder.Property(_ => _.RocId)
            .IsRequired();
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        //Set up the relationship with Roc
        builder
            .HasOne(s => s.Roc)
            .WithMany(r => r.Stations)
            .HasForeignKey(s => s.RocId)
            .HasPrincipalKey(r => r.RocId);
    }
}