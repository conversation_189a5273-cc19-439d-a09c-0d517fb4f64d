using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using AntDesign;
using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList;
using Microsoft.IdentityModel.Tokens;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleEdit;

public class EditVesselViewBase : ComponentBase
{
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<EditVesselViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES
    [Parameter]
    public string VehicleIdEncoded { get; set; }
    public string VehicleId => Base64UrlEncoder.Decode(this.VehicleIdEncoded);
    protected EditVesselViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;

    //User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Check authorization
        IsUserOrganizationAdmin = await UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!IsUserOrganizationAdmin)
        {
            // Redirect unauthorized users
            NavigationManager.NavigateTo(Vessels.GetUri());
            return;
        }

        // Init
        this.ViewModel = new EditVesselViewModel(this.SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(this.VehicleId) && this.IsUserOrganizationAdmin)
        {
            await LoadOrganizationVehicleAsync(this.VehicleId);
        }
        else {
            // Redirect
            NavigationManager.NavigateTo(Vessels.GetUri());
        }
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS
    private async Task LoadOrganizationVehicleAsync(string vehicleId)
    {
        try
        {
            this.IsLoading = true;
            StateHasChanged();

            await this.ViewModel.LoadOrganizationVehicleAsync(vehicleId);
            this.IsLoading = false;
            StateHasChanged();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when loading vessel with id '{VehicleId}'", this.VehicleId);
            #endregion

            this.IsLoading = false;
            StateHasChanged();

            // Notify - dont await this call to not block the UI
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load vehicle data. Please try again.",
                Duration = 6.0
            });
        }
    }

    protected async Task HandleSubmit()
    {
        if (IsSubmitting) return;

        try
        {
            IsSubmitting = true;
            ValidationErrors.Clear();
            StateHasChanged();

            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Update vessel
                await this.ViewModel.UpdateOrganizationVehicleAsync();

                // Notify - dont await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Updated",
                    Description = $"Vessel '{ViewModel.VehicleName}' has been updated successfully.",
                    Duration = 4.5
                });

                // Navigate back to vessels list
                NavigateToVesselList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            Logger.LogError(exception, "Exception when updating vehicle with id '{VehicleId}'", VehicleId);
            #endregion

            ValidationErrors.Add(exception.Message);

            // Notify - dont await this call to not block the UI
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to update vehicle. Please check the form and try again.",
                Duration = 6.0
            });
        }
        finally
        {
            IsSubmitting = false;
            StateHasChanged();
        }
    }

    protected void NavigateToVesselList()
    {
        NavigationManager.NavigateTo(Vessels.GetUri());
    }
    #endregion
}
