﻿using Paramore.Darker;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using EnumsNET;
using Microsoft.EntityFrameworkCore;
using global::Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;
using global::Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using global::Klee.Domain.Services.Repositories.OperatorManagement;
using global::Klee.Domain.Services.UserContextService;

namespace Klee.Domain.Services.QueryHandlers.OrganizationOperatorManagement.OrganizationOperators;

public sealed class GetOrganizationOperatorListQueryHandler
    : QueryHandlerAsync<GetOrganizationOperatorListQuery, IReadOnlyList<OrganizationOperatorListItem>>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationOperatorListQueryHandler(IOperatorSrpRepository organizationOperatorSrpRepository,
        IUserContextHelperService userContextHelperService)
        {
            this.OperatorSrpRepository = organizationOperatorSrpRepository;
            this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<OrganizationOperatorListItem>> ExecuteAsync(GetOrganizationOperatorListQuery query,
                                                                            CancellationToken cancellationToken = new CancellationToken())
    {
        // Get Organization Operators from DB
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        List<OrganizationOperatorListItem> organizationOperatorListItems =
            (await this.OperatorSrpRepository.Entities(query)
                      .Where(_ => _.OrganizationId == organizationId)
                      .Where(_ => _.IsActive == true)
                      .ToListAsync(cancellationToken: cancellationToken))
                      .OrderBy(_ => _.OperatorId)
                      .Select(_ => new OrganizationOperatorListItem()
                                   {
                                       EntityId = _.EntityId,
                                       OperatorId = _.OperatorId,
                                       OperatorFirstName = _.FirstName,
                                       OperatorLastName = _.LastName,
                                       OperatorDisplayName = _.DisplayName,
                                       Email = _.OperatorEmail,
                                       HourlyRateInEuros = _.HourlyRateInEuros,
                                       Qualifications = _.Qualifications,
                                       YearsOfExperience = _.YearsOfExperience,
                                       YearsOfRemoteExperience = _.YearsOfRemoteExperience,
                                       WeekDays = _.WorkingDays,
                                       RegularStartTime = _.RegularStartTime.ToLocalTime(),
                                       RegularEndTime = _.RegularEndTime.ToLocalTime(),
                                       Biography = _.Biography
                                   })
                      .ToList();

        return organizationOperatorListItems;
    }
    #endregion
}