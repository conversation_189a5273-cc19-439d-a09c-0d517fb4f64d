﻿using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;

namespace Klee.Domain.Messages.Queries.StationManagement.Stations;

public class GetRocStationsQuery
    : QueryBase<IReadOnlyList<StationListItem>>
{
    #region PROPERTIES
    public string rocId { get; }
    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetRocStationsQuery(string rocId, IQueryContext context)
        : base(context)
    {
        this.rocId = rocId;
    }
    #endregion
}