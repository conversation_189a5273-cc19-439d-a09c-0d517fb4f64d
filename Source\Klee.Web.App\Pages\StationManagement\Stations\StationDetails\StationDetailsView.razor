﻿@page "/Stations/Management/Station/{StationIdEncoded}"

@using Klee.Web.App.Pages.StationManagement.Stations.StationDetails.Data
@using Microsoft.IdentityModel.Tokens
@using Renoir.Srp.Portal.Web.Application.Security
@using Microsoft.AspNetCore.Components.Authorization
@using Klee.Web.App.Pages.StationManagement.Stations.StationUpdateGeneral

@inherits StationDetailsViewBase

<RMainLayoutBody @ref="MainLayoutBody"
                 PageHeaderTitle="@PageHeaderTitle"
                 ParentView="this"
                 OnClickDelete="@OnClickDelete"
                 CanShowButtonDelete="@this.IsUserOrganizationAdmin"
                 OnActiveTabChanged="@OnMainLayoutBody_ActiveTabChanged"
                 ActiveTabId="@DetailsViewProperties.ActiveTabId">
    <BreadcrumbsListItems>
        <RMainLayoutBodyBreadcrumbListItem Text="Stations"/>
        <RMainLayoutBodyBreadcrumbListItem Text="Details"/>
    </BreadcrumbsListItems>
    <ToolbarLeftMenuItems>
    </ToolbarLeftMenuItems>
    <ToolbarRightMenuItems>
        <DxMenuItem Text="More" IconCssClass="fas fa-info" Position="ItemPosition.End"
                    Visible="@AuthenticatedUser.IsUserInternal()">
            <Items>
                <DxMenuItem Text="Entity" Click="OnClick_ShowSysEntityInfoDetailsView" />
            </Items>
        </DxMenuItem>
    </ToolbarRightMenuItems>
    <TabNavItems>
        <RTabNavItem TabId="@StationDetailsViewTabIds.General" IsVisible="true"
                     OnIsActiveChanged="OnTabNavItemGeneral_IsActiveChanged">GENERAL</RTabNavItem>
    </TabNavItems>
    <TabContentItems>
        <RTabContentItem @ref="@TabContentItemGeneral"
                         TabId="@StationDetailsViewTabIds.General" MinHeight="200">
            <div class="row">
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row mb-4">
                        <div class="col-12">
                            <section class="card">
                                <header class="card-header">
                                    <h2 class="card-title">Identification</h2>
                                    <div class="card-actions">
                                        <RCardActionEdit OnClickEdit="@OnClickEdit_OpenStationUpdateGeneralDialogView"
                                                         CanShow="@this.IsUserOrganizationAdmin" />
                                        <RCardActionToggle />
                                    </div>
                                </header>
                                <div class="card-body">
                                    <RControlTextValueDisplay Label="Station Id" Value="@ViewModel.StationId" />
                                    <RControlTextValueDisplay Label="Station Name" Value="@ViewModel.StationName" />
                                    <RControlTextValueDisplay Label="ROC Id" Value="@ViewModel.RocId" />
                                    <RControlTextValueDisplay Label="Joystick Type" Value="@ViewModel.StationJoystickTypeDisplayName" LabelTooltip="Type of joysticks installed on the station"/>
                                    <RControlTextValueDisplay Label="Hourly Rate (Euro)" Value="@ViewModel.HourlyRateInEuros" LabelTooltip="Type of joysticks installed on the station"/>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-xl-4 col-xxl-4">
                    <div class="row">
                        <div class="col-12">
                            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                                <section class="card">
                                    <header class="card-header">
                                        <h2 class="card-title">System</h2>
                                        <div class="card-actions">
                                            <RCardActionToggle />
                                        </div>
                                    </header>
                                    <div class="card-body">
                                        <RControlTextValueDisplay Label="Environment" Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                                        <RControlCheckBoxValueDisplay Label="Active" Value="@ViewModel.IsActive" />
                                    </div>
                                </section>
                            </AuthorizeView>
                        </div>
                    </div>
                </div>
            </div>
        </RTabContentItem>
    </TabContentItems>
    <Content>
    </Content>
</RMainLayoutBody>

<StationUpdateGeneralDialogView @ref="StationUpdateGeneralDialogView"
                                OnDialogViewOpened="OnStationUpdateGeneralDialogView_Opened"
                                OnDialogViewClosed="OnStationUpdateGeneralDialogView_Closed" />
@code {

    #region METHODS - STATIC
    public static string GetUri(string stationId)
    {
        return $"/Stations/Management/Station/{Base64UrlEncoder.Encode(stationId)}";
    }
    #endregion

}
