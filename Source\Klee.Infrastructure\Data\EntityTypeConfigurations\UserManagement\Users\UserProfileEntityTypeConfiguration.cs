﻿using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.UserManagement.Users;

public class UserProfileEntityTypeConfiguration : IEntityTypeConfiguration<UserProfile>{
    public void Configure(EntityTypeBuilder<UserProfile> builder)
    {
        //builder.AddCosmosDbProperties();
        builder.HasIndex(_ => _.UserId)
            .IsUnique();
        builder.Property(_ => _.UserId)
            .IsRequired();
        builder.Property(_ => _.OrganizationId)
            .IsRequired();
        builder.HasQueryFilter(_ => _.EntityIsDeleted == false);

        //Set up the relationship with org
        builder
            .HasOne(s => s.Organization)
            .WithMany(o => o.Users)
            .HasForeignKey(s => s.OrganizationId)
            .HasPrincipalKey(r => r.OrganizationId);
    }
}
