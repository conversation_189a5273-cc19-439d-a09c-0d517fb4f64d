@page "/dashboard"
@using Klee.Web.App.Components.Pages.VoyagePlanning
@layout OrganizationViewLayout

<div class="space-y-6">
    <!-- Main Action Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        <ActionCard
            Href="@VoyagePlanner.GetUri()"
            Title="Find a Captain"
            Description="Find a Remote Operator for your vessel"
            IconClass="fas fa-user"
            ButtonText="Start" />

        <ActionCard
            Href="/find-voyage"
            Title="Find a Voyage"
            Description="Browse open voyages looking for Remote Operators"
            IconClass="fas fa-ship"
            ButtonText="Start" />
    </div>

    <!-- Upcoming Voyages -->
    <div class="space-y-4">
        <h2 class="text-2xl font-bold text-teal-700 flex items-center gap-2">
            <i class="@($"fas fa-clock h-4 w-4 mr-2 {TailwindStyleStrings.Icon.Default}")"></i>
            Upcoming voyages
        </h2>

        <VoyageSection Voyage="@UpcomingVoyages[0]" />
        
        <VoyageSection Voyage="@UpcomingVoyages[0]" />
    </div>
</div>

@code {

    public static string GetUri() {
        return "/dashboard";
    }

    [Inject]
    private NavigationManager NavigationManager { get; set; }

    private List<VoyageItem> RecentVoyages { get; set; } = new();
    private List<VoyageItem> UpcomingVoyages { get; set; } = new();

    protected override void OnInitialized()
    {
        // TODO: Replace with actual data
        RecentVoyages = new List<VoyageItem>
        {
            new VoyageItem
            {
                Id = 1,
                VesselName = "Helsinki",
                Status = "completed",
                DepartureDate = DateTime.Parse("2024-03-01"),
                ArrivalDate = DateTime.Parse("2024-03-05"),
                Origin = "Rotterdam",
                Destination = "Antwerp",
                Cargo = "Container",
                Captain = "John Smith",
                RocStation = "ROC Amsterdam"
            },
            new VoyageItem
            {
                Id = 2,
                VesselName = "River Drone 3",
                Status = "active",
                DepartureDate = DateTime.Parse("2024-03-10"),
                ArrivalDate = DateTime.Parse("2024-03-15"),
                Origin = "Amsterdam",
                Destination = "Rotterdam",
                Cargo = "Bulk Cargo",
                Captain = "Sarah Johnson",
                RocStation = "ROC Rotterdam"
            }
        };

        UpcomingVoyages = new List<VoyageItem>
        {
            new VoyageItem
            {
                Id = 3,
                VesselName = "Rotterdam",
                Status = "upcoming",
                DepartureDate = DateTime.Parse("2024-04-01"),
                ArrivalDate = DateTime.Parse("2024-04-05"),
                Origin = "Rotterdam",
                Destination = "Antwerp",
                Cargo = "Container",
                Captain = "Michael Chen",
                RocStation = "ROC Amsterdam"
            }
        };
    }

    public class VoyageItem
    {
        public int Id { get; set; }
        public string VesselName { get; set; }
        public string Status { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public string Origin { get; set; }
        public string Destination { get; set; }
        public string Cargo { get; set; }
        public string Captain { get; set; }
        public string RocStation { get; set; }
    }
}