﻿using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Messages.Queries.UserManagement.Users;

namespace Klee.Web.App.Pages.UserManagement.Users.UserProfileUpdateGeneral;

    public class UserProfileUpdateGeneralDialogViewModel
        : ViewModelBase<UserProfileUpdateGeneralDialogViewModel>
    {
        #region PROPERTIES
        [Required]
        [Display(Name = "UserProfile Id")]
        public string UserProfileId { get; set; } = "";

        [Required]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = "";

        [Required]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = "";

        [Required]
        [Display(Name = "DisplayName Name")]
        public string DisplayName { get; set; } = "";

        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = "";
        #endregion

        #region CONSTRUCTORS
        public UserProfileUpdateGeneralDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.UserProfileId = "";
            this.FirstName = "";
            this.LastName = "";
            this.DisplayName = "";
            this.Email = "";
        }

        public async Task LoadUserProfileAsync(string userProfileId)
        {
            // Get UserProfile
            UserProfile userProfile = await this.SrpQueryProcessor.ExecuteAsync(new GetUserProfileQuery(userProfileId, this.SrpQueryContext)) ?? new UserProfile();

            // Set UserProfile
            this.UserProfileId = userProfile.UserId;
            this.FirstName = userProfile.FirstName;
            this.LastName = userProfile.LastName;
            this.DisplayName = userProfile.DisplayName;
            this.Email = userProfile.Email;
        }
        #endregion

        #region METHODS
        public async Task UpdateUserProfileAsync()
        {
            // Init
            UpdateUserProfileGeneralCommand updateUserProfileGeneralCommand = new UpdateUserProfileGeneralCommand(this.UserProfileId,
                                                                                                      this.SrpCommandContext)
                                                                              {
                                                                                FirstName = this.FirstName,
                                                                                LastName = this.LastName,
                                                                                DisplayName = this.DisplayName,
                                                                                Email = this.Email
                                                                              };

            // Create UserProfile
            await this.SrpCommandProcessor
                      .SendAsync(updateUserProfileGeneralCommand, false);
        }
        #endregion
    }