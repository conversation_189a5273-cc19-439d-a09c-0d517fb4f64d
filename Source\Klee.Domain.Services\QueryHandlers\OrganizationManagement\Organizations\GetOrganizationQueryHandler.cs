using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.OrganizationManagement.Organizations
{
    public sealed class GetOrganizationQueryHandler
        : QueryHandlerAsync<GetOrganizationQuery, Organization>
    {
        #region PROPERTIES
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationQueryHandler(IOrganizationSrpRepository organizationSrpRepository,
                                           IMemoryCache memoryCache)
        {
            this.OrganizationSrpRepository = organizationSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<Organization> ExecuteAsync(GetOrganizationQuery query,
                                                               CancellationToken cancellationToken = new CancellationToken())
        {
            // Init
            string organizationId = query.OrganizationId;

            // Get Organization from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetOrganizationCacheId(organizationId), out Organization cachedOrganization);

            // Init
            Organization organization = cachedOrganization;

            // Get Organization from DB (if needed)
            if (query.AllowCached == false || !isCached)
            {
                // Get Organization
                organization = await this.OrganizationSrpRepository.FindAsync(_ => _.OrganizationId == organizationId  &&
                                                                                   _.EntityPartitionKey == organizationId, query);

                // Cache Organization
                this.MemoryCache.Set(MemoryCacheIds.GetOrganizationCacheId(organizationId),
                                     organization, new MemoryCacheEntryOptions()
                                     {
                                         AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                         Size = 1
                                     });
            }

            return organization;
        }
        #endregion
    }
}