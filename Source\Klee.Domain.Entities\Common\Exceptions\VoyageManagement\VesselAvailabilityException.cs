using System;

namespace Klee.Domain.Entities.Common.Exceptions.VoyageManagement;

/// <summary>
/// Exception thrown when a vessel is not available for the requested time slot
/// </summary>
public class VesselAvailabilityException : Exception
{
    public string VesselId { get; }
    public DateTime VoyageStartDateTime { get; }
    public DateTime VoyageEndDateTime { get; }
    public Guid ConflictingVoyageId { get; }
    public DateTime ConflictingVoyageStartDateTime { get; }
    public DateTime ConflictingVoyageEndDateTime { get; }
    public string ConflictingVoyageBookingOrganization { get; }

    public VesselAvailabilityException(string vesselId, DateTime voyageStartDateTime, DateTime voyageEndDateTime,
        Guid conflictingVoyageId, DateTime conflictingVoyageStartDateTime, DateTime conflictingVoyageEndDateTime,
        string conflictingVoyageBookingOrganization)
        : base($"The selected vessel is not available for the requested time slot ({voyageStartDateTime:yyyy-MM-dd HH:mm} - {voyageEndDateTime:yyyy-MM-dd HH:mm}). It is already booked for another voyage from {conflictingVoyageStartDateTime:yyyy-MM-dd HH:mm} to {conflictingVoyageEndDateTime:yyyy-MM-dd HH:mm} by {conflictingVoyageBookingOrganization}.")
    {
        VesselId = vesselId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
        ConflictingVoyageId = conflictingVoyageId;
        ConflictingVoyageStartDateTime = conflictingVoyageStartDateTime;
        ConflictingVoyageEndDateTime = conflictingVoyageEndDateTime;
        ConflictingVoyageBookingOrganization = conflictingVoyageBookingOrganization;
    }

    public VesselAvailabilityException(string vesselId, DateTime voyageStartDateTime, DateTime voyageEndDateTime,
        Guid conflictingVoyageId, DateTime conflictingVoyageStartDateTime, DateTime conflictingVoyageEndDateTime,
        string conflictingVoyageBookingOrganization, string message)
        : base(message)
    {
        VesselId = vesselId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
        ConflictingVoyageId = conflictingVoyageId;
        ConflictingVoyageStartDateTime = conflictingVoyageStartDateTime;
        ConflictingVoyageEndDateTime = conflictingVoyageEndDateTime;
        ConflictingVoyageBookingOrganization = conflictingVoyageBookingOrganization;
    }

    public VesselAvailabilityException(string vesselId, DateTime voyageStartDateTime, DateTime voyageEndDateTime,
        Guid conflictingVoyageId, DateTime conflictingVoyageStartDateTime, DateTime conflictingVoyageEndDateTime,
        string conflictingVoyageBookingOrganization, string message, Exception innerException)
        : base(message, innerException)
    {
        VesselId = vesselId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
        ConflictingVoyageId = conflictingVoyageId;
        ConflictingVoyageStartDateTime = conflictingVoyageStartDateTime;
        ConflictingVoyageEndDateTime = conflictingVoyageEndDateTime;
        ConflictingVoyageBookingOrganization = conflictingVoyageBookingOrganization;
    }
}
