﻿using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails.Data;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateGeneral;
using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateQualifications;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorDetails;

public partial class OperatorDetailsViewBase : LayoutBodyDetailsViewBase<OperatorDetailsViewModel, UserSessionData>
{
    #region PROPERTIES
    [Parameter]
    public string OperatorIdEncoded { get; set; }
    public string OperatorId => Base64UrlEncoder.Decode(this.OperatorIdEncoded);

    protected string PageHeaderTitle { get; private set; } = "Operator :                                      ";

    // Render Support
    private bool AreTabContentItemGeneralChartsRendered { get; set; }

    // Tab Content Items
    protected RTabContentItem TabContentItemGeneral { get; set; }

    // Dialog Views
    protected OperatorUpdateGeneralDialogView OperatorUpdateGeneralDialogView { get; set; }
    protected OperatorUpdateQualificationsDialogView OperatorUpdateQualificationsDialogView { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Init
        this.ViewModel = new OperatorDetailsViewModel(this);

        //
        await base.OnInitializedAsync();

        // Set PageHeaderTitle
        this.PageHeaderTitle = this.GetPageHeaderTitle();
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            //
            await base.OnParametersSetAsync();

            // Load Operator
            if (await this.ViewModel.LoadOperatorAsync(this.OperatorId))
            {
                // Set PageHeaderTitle
                this.PageHeaderTitle = this.GetPageHeaderTitle();
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when setting parameters.");
            #endregion

            if (!this.IsDisposed)
            {
                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);
            }

            //throw;
        }
    }

    protected override Task OnAfterRenderFirstAsync()
    {
        // Set EventHandlers
        this.TabContentItemGeneral.ContentRendered += this.OnTabContentItemGeneral_ContentRendered;
        return base.OnAfterRenderFirstAsync();
    }

    protected override async Task OnUserSessionDataSaveAsync()
    {
        await base.OnUserSessionDataSaveAsync();
    }

    protected override async Task OnUserSessionDataRestoreAsync()
    {
        await base.OnUserSessionDataRestoreAsync();
    }

    protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
    {
        await base.OnMainLayoutBodyActiveTabChangedAsync(e);

        // Handle Tab Change
        switch (e.ActiveTabId)
        {
            case OperatorDetailsViewTabIds.General:
                break;

        }
    }

    protected override bool ShouldRender()
    {
        return base.ShouldRender();
    }
    #endregion

    #region METHODS - SUPPORT
    private string GetPageHeaderTitle()
    {
        return $"Operator : {this.OperatorId} ({this.ViewModel.OperatorFirstName} {this.ViewModel.OperatorLastName})";
    }
    #endregion

    #region EVENT HANDLERS
    protected async Task OnClickDelete()
    {
        // Init
        string operatorId = this.ViewModel?.OperatorId ?? "";

        try
        {
            if (await this.DialogBox.AskConfirmationForWarningAsync("Delete operator?",
                                                                    $"Delete operator with id '{operatorId}'?") == RDialogResult.Ok)
            {
                // Delete Operator
                await this.ViewModel!.DeleteOperatorAsync(operatorId);

                // Close View
                await this.CloseViewAsync();

                // Notify
                await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Operator with id '{operatorId}' is deleted.");
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when deleting operator with id '{operatorId}'", operatorId);
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }

    protected async Task OnClick_ShowSysEntityInfoDetailsView()
    {
        // Init
        string operatorId = this.ViewModel?.OperatorId ?? "";

        try
        {
            // Show EntityInfoDetails
            //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.Operator);
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when navigating to Operator entity view of operator '{operatorId}'", operatorId);
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }

    protected async Task OnClickEdit_OpenOperatorUpdateGeneralDialogView()
    {
        try
        {
            await this.OperatorUpdateGeneralDialogView.LoadOperatorAsync(this.OperatorId);
            await this.OperatorUpdateGeneralDialogView.OpenDialogViewAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.OperatorUpdateGeneralDialogView));
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }

    protected async Task OnClickEdit_OpenOperatorUpdateQualificationsDialogView()
    {
        try
        {
            await this.OperatorUpdateQualificationsDialogView.LoadOperatorAsync(this.OperatorId);
            await this.OperatorUpdateQualificationsDialogView.OpenDialogViewAsync();
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.OperatorUpdateQualificationsDialogView));
            #endregion

            // Notify
            await this.UserNotificationService.ShowErrorAsync(exception.Message);

            //throw;
        }
    }
    #endregion

    #region EVENT HANDLERS - Operator UPDATE GENERAL DIALOG VIEW
    protected void OnOperatorUpdateGeneralDialogView_Opened()
    {
    }

    protected async Task OnOperatorUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
    {
        switch (e.DialogResult)
        {
            case RDialogResult.Ok:
                // Init
                var data = e.DialogResultData as OperatorUpdateGeneralDialogViewModel;
                
                // Load Update
                await this.ViewModel.LoadOperatorAsync(this.OperatorId, forceLoad:true);
                break;

            default:
                break;
        }
    }
    #endregion

    #region EVENT HANDLERS - Operator UPDATE QUALIFICATIONS DIALOG VIEW
    protected void OnOperatorUpdateQualificationsDialogView_Opened()
    {
    }

    protected async Task OnOperatorUpdateQualificationsDialogView_Closed(RDialogViewClosedEventArgs e)
    {
        switch (e.DialogResult)
        {
            case RDialogResult.Ok:
                // Init
                var data = e.DialogResultData as OperatorUpdateGeneralDialogViewModel;
                
                // Load Update
                await this.ViewModel.LoadOperatorAsync(this.OperatorId, forceLoad:true);
                break;

            default:
                break;
        }
    }
    #endregion

    #region EVENT HANDLERS
    protected Task OnTabNavItemGeneral_IsActiveChanged(bool isActive)
    {
        if (isActive)
        {
        }

        return Task.CompletedTask;
    }

    protected async void OnTabContentItemGeneral_ContentRendered(object sender, EventArgs e)
    {
        if (this.MainLayoutBody.Tabs.ActiveTabId == OperatorDetailsViewTabIds.General &&
            this.AreTabContentItemGeneralChartsRendered == false)
        {
            // Set Indicator
            this.AreTabContentItemGeneralChartsRendered = true;

            // Update charts
            //await this.OperatorSummary01WidgetView.UpdateJavaScriptChartsAsync();
        }
    }
    #endregion
}