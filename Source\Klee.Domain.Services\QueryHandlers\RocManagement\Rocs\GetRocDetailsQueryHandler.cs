﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.RocManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Klee.Domain.Messages.Queries.StationManagement.Stations.Data;
using Microsoft.EntityFrameworkCore;
using Monet.Helpers;
using Renoir.Application.Domain.Helpers;

namespace Klee.Domain.Services.QueryHandlers.RocManagement.Rocs;

public sealed class GetRocDetailsQueryHandler
    : QueryHandlerAsync<GetRocDetailsQuery, RocDetailsItem>
{
    #region PROPERTIES
    private IRocSrpRepository RocSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public GetRocDetailsQueryHandler(IRocSrpRepository rocSrpRepository,
        IMemoryCache memoryCache)
    {
        this.RocSrpRepository = rocSrpRepository;
        this.MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS

    public override async Task<RocDetailsItem> ExecuteAsync(GetRocDetailsQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        // Init
        string rocId = query.RocId;

        // Get ROC from cache
        var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetRocCacheId(rocId), out Roc cachedRoc);

        // Init
        Roc roc = cachedRoc;

        // Get ROC from DB (if needed)
        if (query.AllowCached == false || query.IncludeOrganization || query.IncludeStations ||
            !isCached) {

            IQueryable<Roc>? rocs =  RocSrpRepository.Entities(query);
            // Get ROC 

            //Include relations if needed
            if (query.IncludeStations) {
                rocs =  rocs.Include(_ => _.Stations);
            }

            if (query.IncludeOrganization) {
                rocs = rocs.Include(_ => _.Organization);
            }

            roc = await rocs
                .FirstOrDefaultAsync(_ => _.RocId == rocId && _.EntityPartitionKey == rocId, cancellationToken);
            
            // Cache ROC
            this.MemoryCache.Set(MemoryCacheIds.GetRocCacheId(rocId),
                roc, new MemoryCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                    Size = 1
                });
        }

        //Create Roc details item
        RocDetailsItem rocDetails = new RocDetailsItem() {
            RocId = roc.RocId,
            RocName = roc.RocName,
            SoftwareEnvironmentDisplayName = roc.SoftwareEnvironmentId.GetDisplayName(),
            IsActive = roc.IsActive ?? false,
            CreatedBy = roc.CreatedBy,
            CreatedByName = roc.GetCreatedByName(),
            CreatedDateTimeRstAsString = roc.GetCreatedDateTimeRstAsString(),
            ModifiedBy = roc.ModifiedBy,
            ModifiedByName = roc.GetModifiedByName(),
            ModifiedDateTimeRstAsString = roc.GetModifiedDateTimeRstAsString()
        };

        rocDetails.Stations = roc.Stations
            .Select(_ => new StationListItem()
            {
                EntityId = _.EntityId,
                StationId = _.StationId,
                StationName = _.StationName,
                RocId = _.RocId,
                JoystickTypeDisplayName = _.JoystickTypeId.GetDisplayName(),
                IsActive = _.IsActive ?? false

            }).ToList();
        return rocDetails;
    }
    #endregion
}