﻿@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits RocUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="@ViewModelEditContext"
                 Title="Update Roc">
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Roc Id" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.RocId" ReadOnly="true"/>
                <ValidationMessage For="@(() => ViewModel.RocId)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-12">
            <RControlValueEdit Label="Roc Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.RocName"/>
                <ValidationMessage For="@(() => ViewModel.RocName)"/>
            </RControlValueEdit>
        </div>
        
        <div class="col-md-12">
            <RControlValueEdit Label="Address" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.Address"/>
                <ValidationMessage For="@(() => ViewModel.Address)"/>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}