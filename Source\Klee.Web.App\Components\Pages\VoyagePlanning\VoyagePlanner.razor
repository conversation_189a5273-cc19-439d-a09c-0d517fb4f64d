@page "/find-captain/plan-voyage"

@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data

@layout OrganizationViewLayout

@inherits VoyagePlannerViewBase

<div class="container py-3">
    <!-- Steps Component -->
    <div class="mb-8">
        <Steps Current="@ViewModel.GetCurrentStep()" Class="voyage-steps">
            <Step Title="Plan Voyage" Description="Select vessel and voyage details" />
            <Step Title="Select Captain" Description="Choose an available captain" />
            <Step Title="Confirm Voyage" Description="Review and confirm voyage details" />
        </Steps>
    </div>

    <!-- Main Content -->
    @switch (ViewModel.CurrentViewState) {
        case PlanVoyageViewState.Planning:
            <PlanVoyageComponent @ref="this.PlanVoyageComponentRef"
                                 OnClick_PlanVoyage="@HandlePlanVoyageSubmit"
                                 OnClick_Back="@NavigateToDashboard"
                                 InitialVesselId="@(ViewModel.HasStoredVoyageData() ? ViewModel.SelectedVesselId : "")"
                                 InitialStartDateTime="@(ViewModel.HasStoredVoyageData() ? ViewModel.VoyageStartDateTime : null)"
                                 InitialEndDateTime="@(ViewModel.HasStoredVoyageData() ? ViewModel.VoyageEndDateTime : null)"
                                 InitialQualifications="@(ViewModel.HasStoredVoyageData() ? ViewModel.RequiredQualifications : new List<QualificationTypeIds>())"
                                 InitialDescription="@(ViewModel.HasStoredVoyageData() ? ViewModel.Description : "")" />
            break;
        case PlanVoyageViewState.SelectingCaptain:
            <SelectCaptainComponent VoyageStartDateTime="@(ViewModel.VoyageStartDateTime)"
                                    VoyageEndDateTime="@(ViewModel.VoyageEndDateTime)"
                                    RequiredQualifications="@ViewModel.RequiredQualifications"
                                    OnClick_Back="@HandleBackToPlanning"
                                    OnSelectCaptain="@HandleSelectCaptain"
                                    OnCreateIncompleteVoyage="@HandleCreateIncompleteVoyage" />
            break;
        case PlanVoyageViewState.ConfirmingOrder:
            <VoyageOrderConfirmationComponent @ref="OrderConfirmationComponent"
                                     SelectedVesselId="@ViewModel.SelectedVesselId"
                                     VoyageStartDateTime="@ViewModel.VoyageStartDateTime"
                                     VoyageEndDateTime="@ViewModel.VoyageEndDateTime"
                                     RequiredQualifications="@ViewModel.RequiredQualifications"
                                     Description="@ViewModel.Description"
                                     SelectedCaptain="@ViewModel.SelectedCaptain"
                                     OnClick_Back="@HandleBackToSelectCaptain"
                                     OnVoyageConfirmed="@HandleVoyageConfirmed" />
            break;
    }
</div>

@code {
    public static string GetUri()
    {
        return "/find-captain/plan-voyage";
    }
}
