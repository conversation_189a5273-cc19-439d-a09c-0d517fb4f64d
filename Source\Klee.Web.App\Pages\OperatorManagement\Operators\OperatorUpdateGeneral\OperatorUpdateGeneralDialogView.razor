﻿@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits OperatorUpdateGeneralDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Update Operator">
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Operator Id" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.OperatorId" ReadOnly="true" />
                <ValidationMessage For="@(() => ViewModel.OperatorId)" />
            </RControlValueEdit>
        </div>

        <div class="col-md-6">
            <RControlValueEdit Label="Email">
                <DxTextBox @bind-Text="ViewModel.OperatorEmail" />
                <ValidationMessage For="@(() => ViewModel.OperatorEmail)" />
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="First Name">
                <DxTextBox @bind-Text="ViewModel.OperatorFirstName" />
                <ValidationMessage For="@(() => ViewModel.OperatorFirstName)" />
            </RControlValueEdit>
        </div>

        <div class="col-md-6">
            <RControlValueEdit Label="Last Name">
                <DxTextBox @bind-Text="ViewModel.OperatorLastName" />
                <ValidationMessage For="@(() => ViewModel.OperatorLastName)" />
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Hour Rate (Euros)">
                <DxSpinEdit @bind-Value="@ViewModel.HourlyRateInEuros"
                            DisplayFormat="€ #,##0.00"
                            Increment="0.5"
                            MinValue="0" />
                <ValidationMessage For="@(() => ViewModel.HourlyRateInEuros)" />
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}