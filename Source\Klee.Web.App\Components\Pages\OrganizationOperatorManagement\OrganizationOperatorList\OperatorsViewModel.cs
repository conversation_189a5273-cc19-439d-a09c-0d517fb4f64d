using EnumsNET;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Renoir.Srp.Portal.Web.Pages.Common;
using Klee.Domain.Services;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList;

public class OperatorsViewModel {

    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion


    #region PROPERTIES
    public List<OrganizationOperatorListItem> OrganizationOperators { get; private set; } = new ();
    #endregion

    #region CONSTRUCTORS
    public OperatorsViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion


    #region METHODS
    public async Task<bool> LoadOrganizationOperatorsAsync(bool forceLoad = false)
    {
       
        // Load Organization Operators
        if (forceLoad)
        {
            // Load Organization Operators
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            this.OrganizationOperators = (await this._srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationOperatorListQuery(queryContext))).ToList();
            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOrganizationOperatorAsync(string operatorId)
    {
        try
        {
            // Delete Organization Operator
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            await this._srpProcessors.CommandProcessor.SendAsync(new DeleteOperatorCommand(operatorId, commandContext));

            // Reload Organization Operators
            await this.LoadOrganizationOperatorsAsync(forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion
}
