﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.RocManagement.Rocs.Validators;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles.Helpers;
using Klee.Domain.Services.Repositories.RocManagement;

namespace Klee.Domain.Services.CommandHandlers.RocManagement.Rocs;

public sealed class CreateRocCommandHandler : RequestHandlerAsync<CreateRocCommand> {
    #region PROPERTIES

    private IRocSrpRepository RocSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public CreateRocCommandHandler(IRocSrpRepository rocSrpRepository,
        IMemoryCache memoryCache) {
        RocSrpRepository = rocSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateRocCommandValidator))]
    public override async Task<CreateRocCommand> HandleAsync(CreateRocCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        // Create
        Roc roc = new Roc() {
            RocName = command.RocName,
            Address = command.RocAddress,
            SoftwareEnvironmentId = command.SoftwareEnvironmentId,
            OrganizationId = command.OrganizationId,
        };

        // Save 
        await RocSrpRepository.AddAsync(roc, command);

        // Set Result
        command.Result.EntityId = roc.EntityId;

        // Clear Caches
        MemoryCache.RemoveVehicle(roc.RocId);
 

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}