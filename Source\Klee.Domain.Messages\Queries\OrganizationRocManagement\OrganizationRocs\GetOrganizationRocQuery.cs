﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;

public class GetOrganizationRocQuery
    : QueryBase<Roc>
{
    #region PROPERTIES
    public string RocId { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationRocQuery(string rocId,
        IQueryContext context)
        : base(context)
    {
        RocId = rocId;
    }
    #endregion
}