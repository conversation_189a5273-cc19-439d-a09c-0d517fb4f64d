﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators.Helpers;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators;

public sealed class DeleteOperatorCommandHandler
    : RequestHandlerAsync<DeleteOperatorCommand>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public DeleteOperatorCommandHandler(IOperatorSrpRepository operatorSrpRepository,
        IMemoryCache memoryCache)
    {
        OperatorSrpRepository = operatorSrpRepository;
        MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    public override async Task<DeleteOperatorCommand> HandleAsync(DeleteOperatorCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Find Operator
        Operator operatorObj = await OperatorSrpRepository.FindAsync(_ => _.OperatorId == command.OperatorId &&
                                                        _.EntityPartitionKey == command.OperatorId, command);

        if (operatorObj != null)
        {
            // Set IsActive to false (soft delete)
            operatorObj.IsActive = false;

            // Update
            await OperatorSrpRepository.UpdateAsync(operatorObj, command);

            // Clear Caches
            MemoryCache.RemoveOperator(command.OperatorId);
        }
        else
        {
            throw new EntityNotFoundException(
                $"Operator with operator id '{command.OperatorId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }
    #endregion
}