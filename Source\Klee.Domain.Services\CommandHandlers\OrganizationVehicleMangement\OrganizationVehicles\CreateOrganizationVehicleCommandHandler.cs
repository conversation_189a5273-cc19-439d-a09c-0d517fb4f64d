﻿using System;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles.Validators;
using Klee.Domain.Services.UserContextService;

namespace Klee.Domain.Services.CommandHandlers.OrganizationVehicleMangement.OrganizationVehicles;

public sealed class CreateOrganizationVehicleCommandHandler
    : RequestHandlerAsync<CreateOrganizationVehicleCommand>
{
    #region PROPERTIES
    private IVehicleSrpRepository VehicleSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
#endregion

#region CONSTRUCTORS
public CreateOrganizationVehicleCommandHandler(IVehicleSrpRepository vehicleSrpRepository, IUserContextHelperService userContextHelperService)
    {
        VehicleSrpRepository = vehicleSrpRepository;
        UserContextHelperService = userContextHelperService;
}
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateOrganizationVehicleCommandValidator))]
    public override async Task<CreateOrganizationVehicleCommand> HandleAsync(CreateOrganizationVehicleCommand command,
        CancellationToken cancellationToken = new ())
    {
        // Create Vehicle 
        // Get Organization ID from logged in user
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(command.Context.User);

        // Create
        Vehicle vehicle = new Vehicle()
        {
            VehicleTypeId = command.VehicleTypeId,
            VehicleName = command.VehicleName,
            ENI = command.ENI,
            HourlyRateInEuros = command.HourlyRateInEuros,
            Length = command.Length,
            Beam = command.Beam,
            VesselType = command.VesselType,
            OrganizationId = organizationId
        };

        // Save 
        await VehicleSrpRepository.AddAsync(vehicle, command);

        // Set Result
        command.Result.EntityId = vehicle.EntityId;

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}