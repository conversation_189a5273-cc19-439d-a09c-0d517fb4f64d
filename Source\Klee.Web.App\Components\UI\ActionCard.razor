@using AntDesign

<a href="@Href" class="block">
    <Card Class="h-full hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 hover:border-teal-700 bg-white rounded-lg">
        <div class="flex flex-col space-y-1.5 p-6 pb-2">
            <h3 class="font-semibold leading-none tracking-tight text-xl text-teal-700">@Title</h3>
            <p class="text-sm text-gray-600">@Description</p>
        </div>
        <div class="p-6 pt-4 pb-8 flex flex-col items-center justify-center">
            <div class="h-20 w-20 flex items-center justify-center mb-4">
                <i class="@IconClass text-3xl text-teal-700"></i>
            </div>
            <Button Type="@ButtonType.Primary" Class="bg-teal-700 hover:bg-teal-600 border-teal-700 hover:border-teal-600 text-white rounded-md">
                @ButtonText
            </Button>
        </div>
    </Card>
</a>

@code {
    [Parameter]
    public string Href { get; set; } = "#";

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public string Description { get; set; } = "";

    [Parameter]
    public string IconClass { get; set; } = "fas fa-circle";

    [Parameter]
    public string ButtonText { get; set; } = "Start";
}
