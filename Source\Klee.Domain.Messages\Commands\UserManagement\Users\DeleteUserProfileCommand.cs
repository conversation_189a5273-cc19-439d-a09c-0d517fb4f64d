﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.UserManagement.Users;

public class DeleteUserProfileCommand: CommandBase {

    #region RESULT CLASS
    public class CommandResult
    {
    }
    #endregion

    #region PROPERTIES
    public string UserId { get; }

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public DeleteUserProfileCommand(string userId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.UserId = userId;
    }
    #endregion
    
}