﻿using FluentValidation;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations.Validators;

public class UpdateOrganizationGeneralCommandValidator  : AbstractValidator<UpdateOrganizationGeneralCommand>{
    public UpdateOrganizationGeneralCommandValidator()
    {
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
        this.RuleFor(_ => _.Name).NotNull();
        this.RuleFor(_ => _.Name).NotEmpty();
    }
}