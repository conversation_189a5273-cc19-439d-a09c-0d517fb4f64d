﻿using EnumsNET;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Renoir.Srp.Portal.Web.Pages.Common;
using Klee.Domain.Services;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList;

public class VesselsViewModel {

    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion


    #region PROPERTIES
    public List<OrganizationVehicleListItem> OrganizationVehicles { get; private set; } = new ();
    #endregion

    #region CONSTRUCTORS
    public VesselsViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion


    #region METHODS
    public async Task<bool> LoadOrganizationVehiclesAsync(bool forceLoad = false)
    {
        // Load Organization Vehicles
        if (forceLoad)
        {
            // Load Organization Vehicles
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            this.OrganizationVehicles = (await this._srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationVehicleListQuery(queryContext))).ToList();
            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOrganizationVehicleAsync(string organizationVehicleId)
    {
        try
        {
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            // Delete Organization Vehicle
            await this._srpProcessors.CommandProcessor.SendAsync(new DeleteVehicleCommand(organizationVehicleId, commandContext));

            // Load Organization Vehicles
            await this.LoadOrganizationVehiclesAsync(forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion
}