﻿using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationVehicleManagement.OrganizationVehicles;

public class CreateOrganizationVehicleCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public VehicleTypeIds VehicleTypeId { get; }
    public string VehicleName { get; set; } = "";
    public string ENI { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public double Length { get; set; } = 0.0;
    public double Beam { get; set; } = 0.0;
    public VesselTypeIds VesselType { get; set; } = VesselTypeIds.None;
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateOrganizationVehicleCommand(
        VehicleTypeIds vehicleTypeId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.VehicleTypeId = vehicleTypeId;
    }
    #endregion
}