﻿using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Monet.Common;
using Monet.Helpers;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.General;

namespace Klee.Domain.Services
{
    public class SrpProcessors
        : DisposableObject, ISrpProcessors
    {
        #region PROPERTIES
        private IServiceScopeFactory ServiceScopeFactory { get; }
        private IServiceScope ServiceScope { get; }

        public IAmACommandProcessor CommandProcessor => this.ServiceScope.ServiceProvider.GetService<IAmACommandProcessor>();
        public IQueryProcessor QueryProcessor => this.ServiceScope.ServiceProvider.GetService<IQueryProcessor>();

        public IMessageContextProvider MessageContextProvider { get; }

        public Renoir.Application.Messages.Events.Common.IEventContext EventContext => this.MessageContextProvider.EventContext;
        public Renoir.Application.Messages.Commands.Common.ICommandContext CommandContext => this.MessageContextProvider.CommandContext;
        public Renoir.Application.Messages.Queries.Common.IQueryContext QueryContext => this.MessageContextProvider.QueryContext;
        #endregion

        #region CONSTRUCTORS
        public SrpProcessors(IServiceScopeFactory serviceScopeFactory,
                             IMessageContextProvider messageContextProvider)
        {
            this.ServiceScopeFactory = serviceScopeFactory;
            this.ServiceScope = serviceScopeFactory.CreateScope();
            this.MessageContextProvider = messageContextProvider;
        }
        #endregion

        #region METHODS - IDISPOSABLE
        protected override void Dispose()
        {
            this.ServiceScope.ForceDispose();
        }
        #endregion

        #region METHODS
        public Task<Renoir.Application.Messages.Events.Common.IEventContext> GetEventContextAsync()
        {
            return this.MessageContextProvider.GetEventContextAsync();
        }

        public Task<Renoir.Application.Messages.Commands.Common.ICommandContext> GetCommandContextAsync()
        {
            return this.MessageContextProvider.GetCommandContextAsync();
        }

        public Task<Renoir.Application.Messages.Queries.Common.IQueryContext> GetQueryContextAsync()
        {
            return this.MessageContextProvider.GetQueryContextAsync();
        }
        #endregion
    }
}
