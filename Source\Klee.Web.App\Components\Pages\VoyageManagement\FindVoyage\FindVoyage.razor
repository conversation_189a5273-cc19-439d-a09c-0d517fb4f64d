@layout OrganizationViewLayout
@using AntDesign
@using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data
@using Klee.Domain.Entities.VoyageManagement.Voyages.Data
@using Klee.Web.App.Components.UI
@using EnumsNET

@page "/find-voyage"

@inherits FindVoyageViewBase

<div class="container py-8">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">Find a Voyage</h1>

        <div class="flex items-center gap-3">
            <Button Type="@ButtonType.Default"
                    Class="@TailwindStyleStrings.Button.Secondary"
                    Icon="@IconType.Outline.Reload"
                    OnClick="@OnClickRefresh">
                Refresh
            </Button>
        </div>
    </div>

    @if (!ViewModel.OpenVoyages.Any())
    {
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="text-center py-12">
                <i class="fas fa-ship text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Open Voyages Found</h3>
                <p class="text-gray-500">No voyages are currently looking for operators.</p>
            </div>
        </Card>
    }
    else
    {
        <Card Class="@TailwindStyleStrings.Card.Container">
            <Table TItem="OpenVoyageListItem"
                       DataSource="@ViewModel.OpenVoyages"
                       Class="@TailwindStyleStrings.Table.Container"
                       ExpandIconColumnIndex="0"
                       RowExpandable="@(record => true)">
                    <ColumnDefinitions>
                        <PropertyColumn Property="@(v => v.DateRangeDisplay)" Title="Date & Time" Sortable Width="180">
                            <Template>
                                <div class="flex flex-col">
                                    <span class="font-medium text-gray-900">@context.DateRangeDisplay</span>
                                    <span class="text-xs text-gray-500">Duration: @context.DurationDisplay</span>
                                </div>
                            </Template>
                        </PropertyColumn>
                        <PropertyColumn Property="@(v => v.VesselDisplayName)" Title="Vessel" Sortable Filterable Width="220">
                            <Template>
                                <div class="flex items-center gap-2">
                                    <i class="@($"fas fa-ship h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                                    <span class="font-medium">@context.VesselDisplayName</span>
                                </div>
                            </Template>
                        </PropertyColumn>
                        <PropertyColumn Property="@(v => v.QualificationsDisplay)" Title="Required Qualifications" Filterable Width="250">
                            <Template>
                                @if (context.RequiredQualifications.Any())
                                {
                                    <div class="flex flex-wrap gap-1">
                                        @foreach (var qualification in context.RequiredQualifications.Take(2))
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                                @qualification.AsString(EnumFormat.DisplayName)
                                            </span>
                                        }
                                        @if (context.RequiredQualifications.Count > 2)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                                +@(context.RequiredQualifications.Count - 2) more
                                            </span>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <span class="text-sm text-gray-500 italic">No specific qualifications required</span>
                                }
                            </Template>
                        </PropertyColumn>
                        <ActionColumn Title="Actions" Fixed="ColumnFixPlacement.Right" Width="120">
                            <Space>
                                <SpaceItem>
                                    <Button Type="@ButtonType.Primary"
                                            Class="@TailwindStyleStrings.Button.Primary"
                                            Size="@ButtonSize.Small"
                                            OnClick="() => HandleSelectVoyage(context.VoyageId)">
                                        Assign Operator
                                    </Button>
                                </SpaceItem>
                            </Space>
                        </ActionColumn>
                    </ColumnDefinitions>
                    <ExpandTemplate Context="voyage">
                        <div class="px-8 py-6 bg-gray-50">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Voyage Details -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Voyage Details</h4>
                                    <div class="bg-white rounded-lg p-4 shadow-sm">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-1">Description</p>
                                                @if (!string.IsNullOrWhiteSpace(voyage.Data.Description))
                                                {
                                                    <p class="text-sm text-gray-900">@voyage.Data.Description</p>
                                                }
                                                else
                                                {
                                                    <p class="text-sm text-gray-500 italic">No description provided</p>
                                                }
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Start Date & Time</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.StartDateTime.ToString("MMM dd, yyyy HH:mm")</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">End Date & Time</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.EndDateTime.ToString("MMM dd, yyyy HH:mm")</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Duration</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.DurationDisplay</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Booking Organization</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.BookingOrganizationName</p>
                                            </div>
                                            @if (voyage.Data.RequiredQualifications.Any())
                                            {
                                                <div class="md:col-span-2">
                                                    <p class="text-sm font-medium text-teal-700 mb-2">Required Qualifications</p>
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach (var qualification in voyage.Data.RequiredQualifications)
                                                        {
                                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-teal-100 text-teal-800">
                                                                @qualification.AsString(EnumFormat.DisplayName)
                                                            </span>
                                                        }
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- Vessel Information -->
                                <div class="space-y-4">
                                    <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Vessel Information</h4>
                                    <div class="bg-white rounded-lg p-4 shadow-sm">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Vessel Name</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleName</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">ENI</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleENI</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Type</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleTypeDisplayName</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Hourly Rate</p>
                                                <p class="text-sm text-gray-900">€@voyage.Data.VehicleHourlyRateInEuros.ToString("N2")</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Length</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleLength.ToString("N1") m</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-teal-700 mb-1">Beam</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleBeam.ToString("N1") m</p>
                                            </div>
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-1">Owner Organization</p>
                                                <p class="text-sm text-gray-900">@voyage.Data.VehicleOrganizationName</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ExpandTemplate>
                </Table>
        </Card>
    }
</div>

@code {
    public static string GetUri()
    {
        return "/find-voyage";
    }
}
