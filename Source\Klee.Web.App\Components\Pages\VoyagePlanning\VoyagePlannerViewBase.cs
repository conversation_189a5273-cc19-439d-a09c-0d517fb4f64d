using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Components.Pages;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class VoyagePlannerViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected NavigationManager NavigationManager { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }

    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    #endregion

    #region PROPERTIES
    protected VoyagePlannerViewModel ViewModel { get; set; } = new();
    
    // User authorization
    public bool IsUserOrganizationAdmin { get; set; } = false;

    //Refs
    public PlanVoyageComponent PlanVoyageComponentRef { get; set; }
    public VoyageOrderConfirmationComponent OrderConfirmationComponent { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        // Check authorization
        IsUserOrganizationAdmin = await UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();

        if (!IsUserOrganizationAdmin)
        {
            // Redirect unauthorized users
            NavigationManager.NavigateTo(Dashboard.GetUri());
            return;
        }

        await base.OnInitializedAsync();
    }
    #endregion

    #region METHODS - NAVIGATION
    protected void NavigateToDashboard()
    {
        NavigationManager.NavigateTo(Dashboard.GetUri());
    }

    protected async Task HandlePlanVoyageSubmit((string, DateTime, DateTime, IEnumerable<QualificationTypeIds>, string) callbackData)
    {
        (string vesselId, DateTime startDateTime, DateTime endDateTime, IEnumerable<QualificationTypeIds> qualifications, string description) = callbackData;

        // Store voyage data for captain selection
        ViewModel.SetVoyageData(vesselId, startDateTime, endDateTime, qualifications, description);

        // Navigate to captain selection
        ViewModel.NavigateToSelectCaptain();
        StateHasChanged();
    }

    protected void HandleBackToPlanning()
    {
        ViewModel.NavigateToPlanning();
        StateHasChanged();
    }

    protected async Task HandleSelectCaptain(AvailableCaptainListItem selectedCaptain)
    {
        try
        {
            // Store selected captain data
            ViewModel.SetSelectedCaptain(selectedCaptain);

            // Navigate to order confirmation
            ViewModel.NavigateToOrderConfirmation();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to select captain. Please try again.",
                Duration = 4.5
            });
        }
    }

    protected void HandleBackToSelectCaptain()
    {
        ViewModel.NavigateToSelectCaptain();
        StateHasChanged();
    }

    protected async Task HandleVoyageConfirmed()
    {
        // Navigate back to dashboard after successful voyage creation
        NavigationManager.NavigateTo(Dashboard.GetUri());
    }

    protected async Task HandleCreateIncompleteVoyage()
    {
        try
        {
            // Get current user's organization
            string userOrgId = await UserAuthenticationService.GetCurrentUserOrganizationIdAsync();

            // Create voyage without operator
            var createVoyageCommand = new CreateVoyageCommand(userOrgId, await SrpProcessors.GetCommandContextAsync())
            {
                StartDateTime = ViewModel.VoyageStartDateTime,
                EndDateTime = ViewModel.VoyageEndDateTime,
                Description = ViewModel.Description,
                RequiredQualifications = ViewModel.RequiredQualifications.ToList(),
                VehicleId = ViewModel.SelectedVesselId,
                OperatorId = null // No operator assigned
            };

            // Execute voyage creation
            await SrpProcessors.CommandProcessor.SendAsync(createVoyageCommand);

            NotificationService.Success(new NotificationConfig()
            {
                Message = "Voyage Created",
                Description = "Your voyage has been created successfully. You can assign an operator later from the Find Voyage page.",
                Duration = 6.0
            });

            // Navigate back to dashboard
            NavigationManager.NavigateTo(Dashboard.GetUri());
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to create voyage. Please try again.",
                Duration = 4.5
            });
        }
    }

    #endregion
}
