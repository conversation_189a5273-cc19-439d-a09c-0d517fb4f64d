﻿using Klee.Domain.Entities.RocManagement.Rocs.Data;

namespace Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs.Data;

public class OrganizationRocListItem {
    #region PROPERTIES
    public long EntityId { get; set; }
    public string RocId { get; set; } = "";
    public string RocName { get; set; } = "";
    public string Address { get; set; } = "";
    public string Location { get; set; } = "";
    public string PostalCode { get; set; } = "";
    public CountryIds Country { get; set; } = CountryIds.None;
    public string CountryDisplayName { get; set; } = "";
    #endregion
}