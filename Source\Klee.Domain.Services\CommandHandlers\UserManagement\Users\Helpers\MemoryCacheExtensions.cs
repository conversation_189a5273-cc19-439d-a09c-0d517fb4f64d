﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.UserManagement.Users.Helpers;

public static class MemoryCacheExtensions
{
    public static void RemoveUserProfile(this IMemoryCache memoryCache,
        string userId)
    {
        memoryCache.Remove(MemoryCacheIds.GetUserProfileListCacheId);
        memoryCache.Remove(MemoryCacheIds.GetUserProfileCacheId(userId));
    }
}