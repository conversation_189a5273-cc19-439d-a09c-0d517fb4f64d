﻿using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;

public class UpdateOperatorGeneralCommandValidator : AbstractValidator<UpdateOperatorGeneralCommand> {
    public UpdateOperatorGeneralCommandValidator() {
        this.RuleFor(_ => _.OperatorId).NotNull();
        this.RuleFor(_ => _.OperatorId).NotEmpty();
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
    }

}