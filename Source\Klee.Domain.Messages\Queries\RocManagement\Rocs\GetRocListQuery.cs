﻿using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;

namespace Klee.Domain.Messages.Queries.RocManagement.Rocs;

public class GetRocListQuery : QueryBase<IReadOnlyList<RocListItem>> {

    #region PROPERTIES
    public bool AllowCached { get; set; } = false;

    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetRocListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
    
}