﻿@using DevExpress.Blazor
@using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data
@using Renoir.Web.Razor.Components.DialogsEditForm
@using Renoir.Web.Razor.Components.Labels
@inherits RocCreateDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Create ROC">
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Roc Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.RocName" />
                <ValidationMessage For="@(() => ViewModel.RocName)" />
            </RControlValueEdit>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Address" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.RocAddress" />
                <ValidationMessage For="@(() => ViewModel.RocAddress)" />
            </RControlValueEdit>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Environment" IsRequired="true">
                <DxComboBox Data="RocCreateDialogViewModel.SelectableSoftwareEnvironmentDisplayNames"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.SoftwareEnvironmentDisplayName" />
                <ValidationMessage For="@(() => ViewModel.SoftwareEnvironmentDisplayName)" />
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Organization Name" IsRequired="true">
                <DxComboBox Data="this.ViewModel.OrganizationNames"
                            TextFieldName="@nameof(OrganizationNameListItem.Name)"
                            ValueFieldName="@nameof(OrganizationNameListItem.OrganizationId)"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.SelectedOrganizationId" />
                <ValidationMessage For="@(() => ViewModel.SelectedOrganizationId)" />
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}