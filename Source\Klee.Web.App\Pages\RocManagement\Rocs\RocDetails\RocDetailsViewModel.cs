﻿using EnumsNET;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Data;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocDetails;

public class RocDetailsViewModel 
        : ViewModelBase<RocDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - ENTITY
        public Roc Roc { get; private set; }
        #endregion

        #region PROPERTIES - VIEW
        public string RocId { get; private set; } = "";

        public string RocName { get; private set; } = "";
        public string Address { get; private set; } = "";
        public string SoftwareEnvironmentDisplayName { get; private set; } = "";
        public bool IsActive { get; private set; }
        public string CreatedBy { get; private set; } = "";
        public string CreatedByName { get; private set; } = "";
        public string CreatedDateTimeRstAsString { get; private set; } = "";
        public string ModifiedBy { get; private set; } = "";
        public string ModifiedByName { get; private set; } = "";
        public string ModifiedDateTimeRstAsString { get; private set; } = "";
        #endregion

        #region CONSTRUCTORS
        public RocDetailsViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadRocAsync(string rocId,
                                                 bool forceLoad = false)
        {
            if(this.RocId != rocId ||
               forceLoad)
            {
                // Get ROC
                Roc roc = await this.SrpQueryProcessor.ExecuteAsync(new GetRocQuery(rocId, this.SrpQueryContext)) ?? new Roc();
                
                // Set ROC
                this.RocId = roc.RocId;
                this.RocName = roc.RocName;
                this.Address = roc.Address;
                this.SoftwareEnvironmentDisplayName = roc.SoftwareEnvironmentId.AsString(EnumFormat.DisplayName);
                this.IsActive = roc.IsActive ?? false;
                this.CreatedBy = roc.CreatedBy;
                this.CreatedByName = roc.GetCreatedByName();
                this.CreatedDateTimeRstAsString = roc.GetCreatedDateTimeRstAsString();
                this.ModifiedBy = roc.ModifiedBy;
                this.ModifiedByName = roc.GetModifiedByName();
                this.ModifiedDateTimeRstAsString = roc.GetModifiedDateTimeRstAsString();

                // Set Vehicle
                this.Roc = roc;

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteRocAsync(string rocId)
        {
            try
            {
                // Delete ROC
                await this.SrpCommandProcessor.SendAsync(new DeleteRocCommand(rocId, 
                                                                            this.SrpCommandContext));
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }