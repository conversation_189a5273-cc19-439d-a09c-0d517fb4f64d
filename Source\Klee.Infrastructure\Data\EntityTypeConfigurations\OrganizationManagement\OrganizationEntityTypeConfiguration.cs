using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Renoir.Application.EF.Data.Domains.Srp;

namespace Klee.Infrastructure.Data.EntityTypeConfigurations.OrganizationManagement
{
    public class OrganizationEntityTypeConfiguration : IEntityTypeConfiguration<Organization>
    {
        public void Configure(EntityTypeBuilder<Organization> builder)
        {
            builder.HasIndex(_ => _.OrganizationId)
                   .IsUnique();
            builder.Property(_ => _.OrganizationId)
                   .IsRequired();
            builder.Property(_ => _.Name)
                   .IsRequired();
            builder.HasQueryFilter(_ => _.EntityIsDeleted == false);
        }
    }
} 