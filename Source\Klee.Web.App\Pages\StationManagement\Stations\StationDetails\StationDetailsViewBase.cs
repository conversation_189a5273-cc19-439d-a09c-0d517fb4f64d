﻿
using Klee.Domain.Services.UserContextService;
using Klee.Web.App.Pages.StationManagement.Stations.StationDetails.Data;
using Klee.Web.App.Pages.StationManagement.Stations.StationUpdateGeneral;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;

namespace Klee.Web.App.Pages.StationManagement.Stations.StationDetails;

    public partial class StationDetailsViewBase : LayoutBodyDetailsViewBase<StationDetailsViewModel, UserSessionData>
    {
        #region DI
        [Inject]
        public IUserContextHelperService UserContextHelperService { get; set; }
        #endregion
        #region PROPERTIES
        [Parameter]
        public string StationIdEncoded { get; set; }
        public string StationId => Base64UrlEncoder.Decode(this.StationIdEncoded);

        protected string PageHeaderTitle { get; private set; } = "Station :                                      ";

        // Render Support
        private bool AreTabContentItemGeneralChartsRendered { get; set; }
        private bool AreTabContentItemYearOverviewChartsRendered { get; set; }

        // Tab Content Items
        protected RTabContentItem TabContentItemGeneral { get; set; }

        // Dialog Views
        protected StationUpdateGeneralDialogView StationUpdateGeneralDialogView { get; set; }

        //User authorization
        public bool IsUserOrganizationAdmin { get; set; } = false;
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new StationDetailsViewModel(this);

            //
            await base.OnInitializedAsync();

            // Set PageHeaderTitle
            this.PageHeaderTitle = this.GetPageHeaderTitle();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();

                // Load Station
                if (await this.ViewModel.LoadStationAsync(this.StationId))
                {
                    // Set PageHeaderTitle
                    this.PageHeaderTitle = this.GetPageHeaderTitle();

                    //Set user authorization
                    this.IsUserOrganizationAdmin =
                        await this.UserContextHelperService.IsUserOrganizationAdminForOrganizationAsync(AuthenticatedUser,
                            this.ViewModel.Station.Roc.OrganizationId);
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override Task OnAfterRenderFirstAsync()
        {
            // Set EventHandlers
            this.TabContentItemGeneral.ContentRendered += this.OnTabContentItemGeneral_ContentRendered;
       
            return base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
        {
            await base.OnMainLayoutBodyActiveTabChangedAsync(e);

            // Handle Tab Change
            switch (e.ActiveTabId)
            {
                case StationDetailsViewTabIds.General:
                    break;

            }
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS - SUPPORT
        private string GetPageHeaderTitle()
        {
            return $"Station : {this.StationId} ({this.ViewModel.StationName})";
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickDelete()
        {
            // Init
            string stationId = this.ViewModel?.StationId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Station?",
                                                                        $"Delete Station with id '{stationId}'?") == RDialogResult.Ok)
                {
                    // Delete Station
                    await this.ViewModel!.DeleteStationAsync(stationId);

                    // Close View
                    await this.CloseViewAsync();

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Station with id '{stationId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting Station with id '{StationId}'", stationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClick_ShowSysEntityInfoDetailsView()
        {
            // Init
            string stationId = this.ViewModel?.StationId ?? "";

            try
            {
                // Show EntityInfoDetails
                //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.Station);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to Station entity view of Station '{StationId}'", stationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenStationUpdateGeneralDialogView()
        {
            try
            {
                await this.StationUpdateGeneralDialogView.LoadStationAsync(this.StationId);
                await this.StationUpdateGeneralDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.StationUpdateGeneralDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - Station UPDATE GENERAL DIALOG VIEW
        protected void OnStationUpdateGeneralDialogView_Opened()
        {
        }

        protected async Task OnStationUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as StationUpdateGeneralDialogViewModel;
                    
                    // Load Update
                    await this.ViewModel.LoadStationAsync(this.StationId, forceLoad:true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnTabNavItemGeneral_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        protected async void OnTabContentItemGeneral_ContentRendered(object sender, EventArgs e)
        {
            if (this.MainLayoutBody.Tabs.ActiveTabId == StationDetailsViewTabIds.General &&
                this.AreTabContentItemGeneralChartsRendered == false)
            {
                // Set Indicator
                this.AreTabContentItemGeneralChartsRendered = true;

                // Update charts
                //await this.StationSummary01WidgetView.UpdateJavaScriptChartsAsync();
            }
        }

        #endregion
    }