using System;

namespace Klee.Domain.Entities.Common.Exceptions.VoyageManagement;

/// <summary>
/// Exception thrown when a voyage booking conflict occurs due to concurrency issues
/// </summary>
public class VoyageConcurrencyException : Exception
{
    public string? OperatorId { get; }
    public DateTime VoyageStartDateTime { get; }
    public DateTime VoyageEndDateTime { get; }

    public VoyageConcurrencyException(string? operatorId, DateTime voyageStartDateTime, DateTime voyageEndDateTime)
        : base($"Captain {operatorId ?? "Unknown"} is no longer available for the requested time slot ({voyageStartDateTime:yyyy-MM-dd HH:mm} - {voyageEndDateTime:yyyy-MM-dd HH:mm}). Another booking may have been made while you were confirming your voyage. Please return to captain selection to choose an alternative captain.")
    {
        OperatorId = operatorId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
    }

    public VoyageConcurrencyException(string? operatorId, DateTime voyageStartDateTime, DateTime voyageEndDateTime, string message)
        : base(message)
    {
        OperatorId = operatorId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
    }

    public VoyageConcurrencyException(string? operatorId, DateTime voyageStartDateTime, DateTime voyageEndDateTime, string message, Exception innerException)
        : base(message, innerException)
    {
        OperatorId = operatorId;
        VoyageStartDateTime = voyageStartDateTime;
        VoyageEndDateTime = voyageEndDateTime;
    }
}
