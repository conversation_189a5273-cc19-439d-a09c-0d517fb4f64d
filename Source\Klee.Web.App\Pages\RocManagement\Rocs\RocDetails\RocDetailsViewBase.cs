﻿using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Data;
using Klee.Web.App.Pages.RocManagement.Rocs.RocDetails.Stations;
using Klee.Web.App.Pages.RocManagement.Rocs.RocUpdateGeneral;
using Klee.Web.App.Pages.StationManagement.Stations.StationCreate;
using Klee.Web.App.Pages.StationManagement.Stations.StationList;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Tokens;
using Renoir.Srp.Portal.Web.Pages.Common.Views.DetailsViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.Tabs.Events;
using Renoir.Web.Razor.Components.Tabs;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;


namespace Klee.Web.App.Pages.RocManagement.Rocs.RocDetails;

public partial class RocDetailsViewBase : LayoutBodyDetailsViewBase<RocDetailsViewModel, UserSessionData>
    {
        #region PROPERTIES
        [Parameter]
        public string RocIdEncoded { get; set; }
        public string RocId => Base64UrlEncoder.Decode(this.RocIdEncoded);

        protected string PageHeaderTitle { get; private set; } = "ROC :                                      ";

        // Render Support
        private bool AreTabContentItemGeneralChartsRendered { get; set; }
        private bool AreTabContentItemStationsChartsRendered { get; set; }

        // Tab Content Items
        protected RTabContentItem TabContentItemGeneral { get; set; }
        protected RTabContentItem TabContentItemStations { get; set; }


        //Component refs
        public RocDetailsViewStationsComponent RocDetailsViewStationComponentRef { get; set; }

        // Dialog Views
        protected RocUpdateGeneralDialogView RocUpdateGeneralDialogView { get; set; }
        protected StationCreateDialogView StationCreateDialogView { get; set; }
        #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new RocDetailsViewModel(this);

            //
            await base.OnInitializedAsync();

            // Set PageHeaderTitle
            this.PageHeaderTitle = this.GetPageHeaderTitle();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();

                // Load Vehicle
                if (await this.ViewModel.LoadRocAsync(this.RocId))
                {
                    // Set PageHeaderTitle
                    this.PageHeaderTitle = this.GetPageHeaderTitle();
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override Task OnAfterRenderFirstAsync()
        {
            // Set EventHandlers
            this.TabContentItemGeneral.ContentRendered += this.OnTabContentItemGeneral_ContentRendered;
            this.TabContentItemStations.ContentRendered += this.OnTabContentItemStations_ContentRendered;

            return base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override async Task OnMainLayoutBodyActiveTabChangedAsync(RTabActiveTabChangedEventArgs e)
        {
            await base.OnMainLayoutBodyActiveTabChangedAsync(e);

            // Handle Tab Change
            switch (e.ActiveTabId)
            {
                case RocDetailsViewTabIds.General:
                    break;

                case RocDetailsViewTabIds.Stations:
                    await RocDetailsViewStationComponentRef.ViewModel.LoadRocStationsAsync(this.RocId, forceLoad: true);
                break;
            }
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS - SUPPORT
        private string GetPageHeaderTitle()
        {
            return $"Roc : {this.RocId} ({this.ViewModel.RocName})";
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickDelete()
        {
            // Init
            string rocId = this.ViewModel?.RocId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete ROC?",
                                                                        $"Delete ROC with id '{rocId}'?") == RDialogResult.Ok)
                {
                    // Delete ROC
                    await this.ViewModel!.DeleteRocAsync(rocId);

                    // Close View
                    await this.CloseViewAsync();

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"ROC with id '{rocId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting ROC with id '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);
                
                //throw;
            }
        }

        protected async Task OnClick_ShowSysEntityInfoDetailsView()
        {
            // Init
            string rocId = this.ViewModel?.RocId ?? "";

            try
            {
                // Show EntityInfoDetails
                //await this.ShowSysEntityInfoDetailsViewAsync(this.ViewModel.Roc);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to ROC entity view of ROC '{rocId}'", rocId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickEdit_OpenRocUpdateGeneralDialogView()
        {
            try
            {
                await this.RocUpdateGeneralDialogView.LoadRocAsync(this.RocId);
                await this.RocUpdateGeneralDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.RocUpdateGeneralDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnClickAddStation_OpenStationCreateDialogView() {
            try
            {
                await this.StationCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.StationCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion


        #region EVENT HANDLERS - ROC UPDATE GENERAL DIALOG VIEW
        protected void OnRocUpdateGeneralDialogView_Opened()
        {
        }

        protected async Task OnRocUpdateGeneralDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Init
                    var data = e.DialogResultData as RocUpdateGeneralDialogViewModel;
                    
                    // Load Update
                    await this.ViewModel.LoadRocAsync(this.RocId, forceLoad:true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS - STATION CREATE DIALOG VIEW
        protected void OnStationCreateDialogView_Opened()
        {
            //Automatically set ROC Id based on current ROC in create dialog view
            StationCreateDialogView.ViewModel.RocId = this.RocId;
        }

        protected async Task OnStationCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created station
                    //this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                    await this.RocDetailsViewStationComponentRef.ViewModel.LoadRocStationsAsync(this.RocId, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnTabNavItemGeneral_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {
            }

            return Task.CompletedTask;
        }

        protected async Task OnTabNavItemStations_IsActiveChanged(bool isActive)
        {
            if (isActive)
            {

            }
        }

        protected async void OnTabContentItemGeneral_ContentRendered(object sender, EventArgs e)
        {
            if (this.MainLayoutBody.Tabs.ActiveTabId == RocDetailsViewTabIds.General &&
                this.AreTabContentItemGeneralChartsRendered == false)
            {
                // Set Indicator
                this.AreTabContentItemGeneralChartsRendered = true;

                // Update charts
                //await this.VehicleSummary01WidgetView.UpdateJavaScriptChartsAsync();
            }
        }

        protected async void OnTabContentItemStations_ContentRendered(object sender, EventArgs e)
        {
            if (this.MainLayoutBody.Tabs.ActiveTabId == RocDetailsViewTabIds.Stations &&
                this.AreTabContentItemStationsChartsRendered == false)
            {
                // Set Indicator
                this.AreTabContentItemStationsChartsRendered = true;

                // Update charts
                //await this.VehicleSummary02WidgetView.UpdateDevExpressCharts();
                //await this.VehicleSummary02WidgetView.UpdateJavaScriptChartsAsync();
            }
        }
        #endregion
    }