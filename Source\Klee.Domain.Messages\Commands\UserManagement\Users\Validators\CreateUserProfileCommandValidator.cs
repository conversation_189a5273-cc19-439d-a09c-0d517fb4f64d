﻿using FluentValidation;

namespace Klee.Domain.Messages.Commands.UserManagement.Users.Validators;

public class CreateUserProfileCommandValidator : AbstractValidator<CreateUserProfileCommand> {
    public CreateUserProfileCommandValidator() {
        this.RuleFor(_ => _.UserId).NotNull();
        this.RuleFor(_ => _.UserId).NotEmpty();
        this.RuleFor(_ => _.UserId).EmailAddress();
        this.RuleFor(_ => _.Email).EmailAddress();
        this.RuleFor(_ => _.OrganizationId).NotNull();
        this.RuleFor(_ => _.OrganizationId).NotEmpty();
    }
}