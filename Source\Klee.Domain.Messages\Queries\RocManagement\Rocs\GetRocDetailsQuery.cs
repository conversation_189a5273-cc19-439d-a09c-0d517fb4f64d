﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.RocManagement.Rocs;

public class GetRocDetailsQuery: QueryBase<RocDetailsItem> {
    #region PROPERTIES
    public string RocId { get; }
    public bool AllowCached { get; set; } = false;
    public bool IncludeStations { get; set; } = true;
    public bool IncludeOrganization { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetRocDetailsQuery(string rocId,
        IQueryContext context)
        : base(context)
    {
        this.RocId = rocId;
    }
    #endregion
}