﻿@using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data
@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits VehicleCreateDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Create Vehicle">
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Vehicle Type" IsRequired="true">
                <DxComboBox Data="VehicleCreateDialogViewModel.SelectableVehicleTypeDisplayNames"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.VehicleTypeDisplayName"/>
                <ValidationMessage For="@(() => ViewModel.VehicleTypeDisplayName)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Vehicle Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.VehicleName"/>
                <ValidationMessage For="@(() => ViewModel.VehicleName)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="ENI">
                <DxTextBox @bind-Text="ViewModel.ENI"/>
                <ValidationMessage For="@(() => ViewModel.ENI)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Hour Rate (Euros)">
                <DxSpinEdit @bind-Value="@ViewModel.HourlyRateInEuros"
                            DisplayFormat="€ #,##0.00" 
                            Increment="0.5"
                            MinValue="0" />
                <ValidationMessage For="@(() => ViewModel.HourlyRateInEuros)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Environment" IsRequired="true">
                <DxComboBox Data="VehicleCreateDialogViewModel.SelectableSoftwareEnvironmentDisplayNames"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.SoftwareEnvironmentDisplayName"/>
                <ValidationMessage For="@(() => ViewModel.SoftwareEnvironmentDisplayName)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Organization Name" IsRequired="true">
                <DxComboBox Data="this.ViewModel.OrganizationNames"
                            TextFieldName="@nameof(OrganizationNameListItem.Name)"
                            ValueFieldName="@nameof(OrganizationNameListItem.OrganizationId)"
                            ListRenderMode="@ListRenderMode.Entire"
                            FilteringMode="@DataGridFilteringMode.None"
                            ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Auto"
                            @bind-Value="@ViewModel.SelectedOrganizationId" />
                <ValidationMessage For="@(() => ViewModel.SelectedOrganizationId)" />
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}