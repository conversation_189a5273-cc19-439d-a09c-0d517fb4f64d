﻿@using Klee.Web.App.Pages.OperatorManagement.Operators.OperatorList
@using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList
@using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList
@using Klee.Web.App.Pages.RocManagement.Rocs.RocList
@using Klee.Web.App.Pages.UserManagement.Users.UserProfileList
@using Renoir.Web.Razor.Components.MainNavigationMenuLeft
@using Renoir.Web.Razor.Shared.Layouts.Main
@using Microsoft.AspNetCore.Components.Authorization
@using Renoir.Srp.Portal.Web.Application.Security

@inherits MainNavMenuLeftBase

<RMainNavMenuLeft>
    <MainNavMenuContent>
        <!-- Main Nav Menu : Start -->
        <RNavMenu>
            @* AUTHORIZE ONLY INTERNAL USERS : START *@
            @* AUTHORIZE ONLY INTERNAL USERS : END *@


            <!-- APPLICATION -->
            @* AUTHORIZE ONLY APP ADMINS : START *@
            <!-- MANAGEMENT -->
            <AuthorizeView Policy="@PolicyNames.AdminsApp">
                <RNavItem IconCssClass="fas fa-home" Href="@Dashboard.GetUri()">User Dashboard</RNavItem>
                <RNavSubMenu>
                    <RNavSubMenuHeader IconCssClass="fas fa-table">Management</RNavSubMenuHeader>
                    <RNavSubMenuList>

                        <!-- ORGANIZATIONS -->
                        <RNavItem IconCssClass="fas fa-building" Href="@OrganizationListView.GetUri()">Organizations</RNavItem>

                        <!-- VEHICLES -->
                        <RNavItem IconCssClass="fas fa-vehicle" Href="@VehicleListView.GetUri()">Vehicles</RNavItem>

                        <!-- ROCS -->
                        <RNavItem IconCssClass="fas fa-gamepad" Href="@RocListView.GetUri()">Rocs</RNavItem>

                        <!-- OPERATORS -->
                        <RNavItem IconCssClass="fas fa-users" Href="@OperatorListView.GetUri()">Operators</RNavItem>

                    </RNavSubMenuList>
                </RNavSubMenu>


                <RNavSubMenu>
                    <RNavSubMenuHeader IconCssClass="fas fa-puzzle-piece">Application</RNavSubMenuHeader>
                    <RNavSubMenuList>
                        <RNavItem IconCssClass="fa fa-user" Href="@UserProfileListView.GetUri()">User Profiles</RNavItem>
                    </RNavSubMenuList>
                </RNavSubMenu>
            </AuthorizeView>
            @* AUTHORIZE ONLY APP ADMINS : END *@

            <!-- SYSTEM -->
            @* AUTHORIZE ONLY SYSTEM ADMINS : START *@
            <AuthorizeView Policy="@PolicyNames.AdminsSystem">
                <RNavSubMenu>
                    <RNavSubMenuHeader IconCssClass="fas fa-cogs">System</RNavSubMenuHeader>
                    <RNavSubMenuList>
                        <RNavItem Href="/SystemInfo">System Info (Dummy)</RNavItem>
                        <RNavSubMenu>
                            <RNavSubMenuHeader>Processing Services</RNavSubMenuHeader>
                            <RNavSubMenuList>
                                <RNavItem Href="/BackgroundJobServer">Background Job Servers (Dummy)</RNavItem>
                                <RNavItem Href="/GeneralService">General Services (Dummy)</RNavItem>
                            </RNavSubMenuList>
                        </RNavSubMenu>
                    </RNavSubMenuList>
                </RNavSubMenu>
            </AuthorizeView>
            @* AUTHORIZE ONLY SYSTEM ADMINS : END *@
        </RNavMenu>
        <!-- Main Nav Menu : End -->
    </MainNavMenuContent>
</RMainNavMenuLeft>
