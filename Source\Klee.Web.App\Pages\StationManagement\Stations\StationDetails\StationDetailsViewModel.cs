﻿using EnumsNET;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Klee.Domain.Messages.Queries.StationManagement.Stations;
using Klee.Web.App.Pages.StationManagement.Stations.StationDetails.Data;
using Renoir.Application.Domain.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.StationManagement.Stations.StationDetails;

    public class StationDetailsViewModel 
        : ViewModelBase<StationDetailsViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - ENTITY
        public Station Station { get; private set; }
        #endregion

        #region PROPERTIES - VIEW
        public string StationId { get; private set; } = "";
        public string StationName { get; private set; } = "";
        public string RocId { get; private set; } = "";
        public string StationJoystickTypeDisplayName { get; private set; } = "";
        public string SoftwareEnvironmentDisplayName { get; private set; } = "";
        public double HourlyRateInEuros { get; private set; } = 0.0;
        public bool IsActive { get; private set; }

        public string CreatedBy { get; private set; } = "";
        public string CreatedByName { get; private set; } = "";
        public string CreatedDateTimeRstAsString { get; private set; } = "";
        public string ModifiedBy { get; private set; } = "";
        public string ModifiedByName { get; private set; } = "";
        public string ModifiedDateTimeRstAsString { get; private set; } = "";
        #endregion

        #region CONSTRUCTORS
        public StationDetailsViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadStationAsync(string stationId,
                                                 bool forceLoad = false)
        {
            if(this.StationId != stationId ||
               forceLoad)
            {
                // Get Station
                Station station = await this.SrpQueryProcessor.ExecuteAsync(new GetStationQuery(stationId, this.SrpQueryContext)) ?? new Station();
                
                // Set Station
                this.StationJoystickTypeDisplayName = station.JoystickTypeId.AsString(EnumFormat.DisplayName);
                this.StationId = station.StationId;
                this.StationName = station.StationName;
                this.RocId = station.RocId;
                this.HourlyRateInEuros = station.HourlyRateInEuros;
                this.SoftwareEnvironmentDisplayName = station.SoftwareEnvironmentId.AsString(EnumFormat.DisplayName);
                this.IsActive = station.IsActive ?? false;
                this.CreatedBy = station.CreatedBy;
                this.CreatedByName = station.GetCreatedByName();
                this.CreatedDateTimeRstAsString = station.GetCreatedDateTimeRstAsString();
                this.ModifiedBy = station.ModifiedBy;
                this.ModifiedByName = station.GetModifiedByName();
                this.ModifiedDateTimeRstAsString = station.GetModifiedDateTimeRstAsString();

                // Set Station
                this.Station = station;

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteStationAsync(string stationId)
        {
            try
            {
                // Delete Station
                await this.SrpCommandProcessor.SendAsync(new DeleteStationCommand(stationId, 
                                                                            this.SrpCommandContext));
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }