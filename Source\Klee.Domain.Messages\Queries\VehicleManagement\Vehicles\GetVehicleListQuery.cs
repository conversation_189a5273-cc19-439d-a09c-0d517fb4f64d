﻿using System.Collections.Generic;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Renoir.Application.Messages.Queries.Common;
using IQueryContext = Renoir.Application.Messages.Queries.Common.IQueryContext;

namespace Klee.Domain.Messages.Queries.VehicleManagement.Vehicles
{
    public class GetVehicleListQuery
        : QueryBase<IReadOnlyList<VehicleListItem>>
    {
        #region PROPERTIES
        public bool AllowCached { get; set; } = false;

        /// <summary>
        /// == true     => only active,
        /// == false    => only inactive,
        /// == null     => no specific filtering
        /// </summary>
        public bool? IsActive { get; set; } = null;
        #endregion

        #region CONSTRUCTORS
        public GetVehicleListQuery(IQueryContext context)
            : base(context)
        {
        }
        #endregion
    }
}
