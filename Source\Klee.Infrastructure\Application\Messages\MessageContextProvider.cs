﻿using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Monet.Validations;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Events.Common;
using Renoir.Application.Messages.General;
using Renoir.Application.Messages.Queries.Common;
using Renoir.Application.Security;
using Renoir.Application.SoftwareEnvironments;
using Renoir.Application.UserSessionStores;

namespace Klee.Infrastructure.Application.Messages
{
    public class MessageContextProvider : IMessageContextProvider
    {
        #region PROPERTIES
        public AuthenticationStateProvider AuthenticationStateProvider { get; }
        public IAuthenticationState AuthenticationState { get; }

        //
        public ClaimsPrincipal User => this.AuthenticationState.GetUser();
        public ISoftwareEnvironmentInfo SoftwareEnvironmentInfo { get; }
        public IUserSessionStore UserSessionStore { get; }

        //
        public IEventContext EventContext => EventContextFactory.Create(this.User, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        public ICommandContext CommandContext => CommandContextFactory.Create(this.User, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        public IQueryContext QueryContext => QueryContextFactory.Create(this.User, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        #endregion

        #region CONSTRUCTOR
        public MessageContextProvider(AuthenticationStateProvider authenticationStateProvider,
                                      IAuthenticationState authenticationState,
                                      ISoftwareEnvironmentInfo softwareEnvironmentInfo,
                                      IUserSessionStore userSessionStore)
        {
            // Validate
            Throw.IfNull(nameof(authenticationStateProvider), authenticationStateProvider);
            Throw.IfNull(nameof(authenticationState), authenticationState);
            Throw.IfNull(nameof(softwareEnvironmentInfo), softwareEnvironmentInfo);
            Throw.IfNull(nameof(userSessionStore), userSessionStore);

            //
            this.AuthenticationStateProvider = authenticationStateProvider;
            this.AuthenticationState = authenticationState;
            this.SoftwareEnvironmentInfo = softwareEnvironmentInfo;
            this.UserSessionStore = userSessionStore;
        }
        #endregion

        #region
        public async Task<IEventContext> GetEventContextAsync()
        {
            // Get User
            var user = (await this.AuthenticationStateProvider.GetAuthenticationStateAsync()).User;

            return new EventContext(user, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        }

        public async Task<ICommandContext> GetCommandContextAsync()
        {
            // Get User
            var user = (await this.AuthenticationStateProvider.GetAuthenticationStateAsync()).User;

            return new CommandContext(user, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        }

        public async Task<IQueryContext> GetQueryContextAsync()
        {
            // Get User
            var user = (await this.AuthenticationStateProvider.GetAuthenticationStateAsync()).User;

            return new QueryContext(user, this.SoftwareEnvironmentInfo, this.UserSessionStore.UserTimeZoneId);
        }
        #endregion
    }
}