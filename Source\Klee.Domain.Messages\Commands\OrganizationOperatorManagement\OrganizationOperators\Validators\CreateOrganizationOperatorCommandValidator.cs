﻿using FluentValidation;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;

public class CreateOrganizationOperatorCommandValidator : AbstractValidator<CreateOrganizationOperatorCommand> {
    public CreateOrganizationOperatorCommandValidator() {
        this.RuleFor(_ => _.WeekDays).Must(this.WorkingDaysMustBeValid).WithMessage("At least one working day must be selected.");
    }

    private bool WorkingDaysMustBeValid(WeekDaysIds weekDays) {
        return weekDays != WeekDaysIds.None;
    }
}