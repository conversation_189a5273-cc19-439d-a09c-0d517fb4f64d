using System.Collections.Generic;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.VoyageManagement.Voyages;

public class GetVoyageManagementListQuery : QueryBase<IReadOnlyList<VoyageManagementListItem>>
{
    #region PROPERTIES
    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = true;
    #endregion

    #region CONSTRUCTORS
    public GetVoyageManagementListQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}
