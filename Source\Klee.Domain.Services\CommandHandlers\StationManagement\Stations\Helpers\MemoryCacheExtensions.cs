﻿using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Microsoft.Extensions.Caching.Memory;

namespace Klee.Domain.Services.CommandHandlers.StationManagement.Stations.Helpers;

public static class MemoryCacheExtensions {
    public static void RemoveStation(this IMemoryCache memoryCache,
        string stationId)
    {
        memoryCache.Remove(MemoryCacheIds.GetStationListCacheId);
        memoryCache.Remove(MemoryCacheIds.GetStationCacheId(stationId));
    }
}