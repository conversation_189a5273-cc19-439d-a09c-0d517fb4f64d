﻿using System;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Renoir.Application.Messages.Commands.Common;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.VehicleManagement.Vehicles
{
    public class CreateVehicleCommand : CommandBase
    {
        #region RESULT CLASS
        public class CommandResult
        {
            public long EntityId { get; set; }
        }
        #endregion

        #region PROPERTIES
        public SoftwareEnvironmentIds SoftwareEnvironmentId { get; }

        public VehicleTypeIds VehicleTypeId { get; }
        public string VehicleName { get; set; } = "";
        public string ENI { get; set; } = "";
        public double HourlyRateInEuros { get; set; } = 0.0;
        public string OrganizationId { get; }
        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public CreateVehicleCommand(SoftwareEnvironmentIds softwareEnvironmentId,
                                    VehicleTypeIds vehicleTypeId,
                                    string organizationId,
                                    ICommandContext commandContext)
            : base(commandContext)
        {
            this.SoftwareEnvironmentId = softwareEnvironmentId;
            this.VehicleTypeId = vehicleTypeId;
            this.OrganizationId = organizationId;
        }
        #endregion
    }
}
