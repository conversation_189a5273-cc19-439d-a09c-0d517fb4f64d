@using DevExpress.Blazor
@using Renoir.Web.Razor.Components.DialogsEditForm
@using Renoir.Web.Razor.Components.Labels
@inherits OrganizationCreateDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Create Organization">
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Name" IsRequired="true">
                <DxTextBox @bind-Text="ViewModel.Name"/>
                <ValidationMessage For="@(() => ViewModel.Name)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Code">
                <DxTextBox @bind-Text="ViewModel.Code"/>
                <ValidationMessage For="@(() => ViewModel.Code)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Contact Email">
                <DxTextBox @bind-Text="ViewModel.ContactEmail"/>
                <ValidationMessage For="@(() => ViewModel.ContactEmail)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <RControlValueEdit Label="Contact Phone">
                <DxTextBox @bind-Text="ViewModel.ContactPhone"/>
                <ValidationMessage For="@(() => ViewModel.ContactPhone)"/>
            </RControlValueEdit>
        </div>
        <div class="col-md-6">
            <RControlValueEdit Label="Address">
                <DxTextBox @bind-Text="ViewModel.Address"/>
                <ValidationMessage For="@(() => ViewModel.Address)"/>
            </RControlValueEdit>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Description">
                <DxMemo @bind-Text="ViewModel.Description"/>
                <ValidationMessage For="@(() => ViewModel.Description)"/>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
} 