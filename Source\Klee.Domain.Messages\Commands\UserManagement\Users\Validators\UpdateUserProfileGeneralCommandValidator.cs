﻿using FluentValidation;

namespace Klee.Domain.Messages.Commands.UserManagement.Users.Validators;

public class UpdateUserProfileGeneralCommandValidator : AbstractValidator<UpdateUserProfileGeneralCommand> {
    public UpdateUserProfileGeneralCommandValidator() {
        this.RuleFor(_ => _.UserId).NotNull();
        this.RuleFor(_ => _.UserId).NotEmpty();
        this.RuleFor(_ => _.UserId).EmailAddress();
    }
}