using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public enum PlanVoyageViewState
{
    Planning,
    SelectingCaptain,
    ConfirmingOrder
}

public class VoyagePlannerViewModel
{
    #region PROPERTIES
    // View state management
    public PlanVoyageViewState CurrentViewState { get; set; } = PlanVoyageViewState.Planning;
    
    // Form data from PlanVoyageComponent (for passing to SelectCaptainComponent)
    public string SelectedVesselId { get; set; } = "";
    public DateTime VoyageStartDateTime { get; set; } = DateTime.Now;
    public DateTime VoyageEndDateTime { get; set; } = DateTime.Now.AddHours(1);
    public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    public string Description { get; set; } = "";

    // Selected captain data (after captain selection)
    public string SelectedOperatorId { get; set; } = "";
    public AvailableCaptainListItem SelectedCaptain { get; set; } = null;
    #endregion

    #region METHODS - STATE MANAGEMENT
    public void SetVoyageData(string vesselId, DateTime startDateTime, DateTime endDateTime, IEnumerable<QualificationTypeIds> qualifications, string description)
    {
        SelectedVesselId = vesselId;
        VoyageStartDateTime = startDateTime;
        VoyageEndDateTime = endDateTime;
        RequiredQualifications = qualifications;
        Description = description;
    }

    public void NavigateToSelectCaptain()
    {
        CurrentViewState = PlanVoyageViewState.SelectingCaptain;
    }

    public void NavigateToPlanning()
    {
        CurrentViewState = PlanVoyageViewState.Planning;
    }

    public void NavigateToOrderConfirmation()
    {
        CurrentViewState = PlanVoyageViewState.ConfirmingOrder;
    }

    public void SetSelectedCaptain(AvailableCaptainListItem captain)
    {
        SelectedCaptain = captain;
        SelectedOperatorId = captain.OperatorId;
    }

    public bool HasStoredVoyageData()
    {
        return !string.IsNullOrEmpty(SelectedVesselId);
    }

    public (string VesselId, DateTime StartDateTime, DateTime EndDateTime, IEnumerable<QualificationTypeIds> Qualifications, string Description) GetStoredVoyageData()
    {
        return (SelectedVesselId, VoyageStartDateTime, VoyageEndDateTime, RequiredQualifications, Description);
    }

    public int GetCurrentStep()
    {
        return CurrentViewState switch
        {
            PlanVoyageViewState.Planning => 0,
            PlanVoyageViewState.SelectingCaptain => 1,
            PlanVoyageViewState.ConfirmingOrder => 2,
            _ => 0
        };
    }
    #endregion
}
