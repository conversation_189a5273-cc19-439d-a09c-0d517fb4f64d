﻿using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Services.Repositories.RocManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.RocManagement;

public class RocSrpRepository
    : EfDomainEntityRepository<AppSrpDbContext, Roc, long>, IRocSrpRepository
{
    public RocSrpRepository(AppSrpDbContext dbContext,
        IDbExceptionConverter dbExceptionConverter,
        ILogger<RocSrpRepository> logger)
        : base(dbContext, dbExceptionConverter, (ILogger)logger)
    {
    }

    protected override void Dispose()
    {
        base.Dispose();
    }
}