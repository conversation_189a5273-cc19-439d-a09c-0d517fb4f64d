Stack trace:
Frame         Function      Args
0007FFFFBF20  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAE20) msys-2.0.dll+0x2118E
0007FFFFBF20  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFFBF20  0002100469F2 (00021028DF99, 0007FFFFBDD8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF20  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF20  00021006A545 (0007FFFFBF30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBF30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF890C40000 ntdll.dll
7FF8907A0000 KERNEL32.DLL
7FF88E630000 KERNELBASE.dll
7FF88F7E0000 USER32.dll
7FF88DDD0000 win32u.dll
7FF890A70000 GDI32.dll
7FF88DEC0000 gdi32full.dll
7FF88E580000 msvcp_win.dll
7FF88E090000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF88FAD0000 advapi32.dll
7FF88ED90000 msvcrt.dll
7FF88FD20000 sechost.dll
7FF8901F0000 RPCRT4.dll
7FF88D2D0000 CRYPTBASE.DLL
7FF88E4E0000 bcryptPrimitives.dll
7FF88EE60000 IMM32.DLL
