﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.OperatorManagement;

public class OperatorSrpRepository
    : EfDomainEntityRepository<AppSrpDbContext, Operator, long>, IOperatorSrpRepository
{
    public OperatorSrpRepository(AppSrpDbContext dbContext,
        IDbExceptionConverter dbExceptionConverter,
        ILogger<OperatorSrpRepository> logger)
        : base(dbContext, dbExceptionConverter, (ILogger)logger)
    {
    }

    protected override void Dispose()
    {
        base.Dispose();
    }
}