﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Services.CommandHandlers.StationManagement.Stations.Helpers;
using Klee.Domain.Services.Repositories.StationManagement;
using Klee.Domain.Messages.Commands.StationManagement.Stations.Validators;
using Klee.Domain.Entities.Entities.StationManagement.Stations;
using Klee.Domain.Entities.StationManagement.Stations;

namespace Klee.Domain.Services.CommandHandlers.StationManagement.Stations;

public sealed class CreateStationCommandHandler
        : RequestHandlerAsync<CreateStationCommand>
    {
        #region PROPERTIES
        private IStationSrpRepository StationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateStationCommandHandler(IStationSrpRepository stationSrpRepository,
            IMemoryCache memoryCache)
        {
            StationSrpRepository = stationSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateStationCommandValidator))]
        public override async Task<CreateStationCommand> HandleAsync(CreateStationCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
            // Create Station (if it doesn't exist)
            if (!await StationSrpRepository.ExistsAsync(_ => _.StationId == command.StationId &&
                                                             _.EntityPartitionKey == command.StationId, command))
            {
                // Create
                Station station = new Station()
                {
                    StationId = command.StationId,
                    StationName = command.StationName,
                    RocId = command.RocId,
                    JoystickTypeId = command.JoystickTypeId,
                    HourlyRateInEuros = command.HourlyRateInEuros,
                    SoftwareEnvironmentId = command.SoftwareEnvironmentId,
                };

                // Save 
                await StationSrpRepository.AddAsync(station, command);

                // Set Result
                command.Result.EntityId = station.EntityId;

                // Clear Caches
                MemoryCache.RemoveStation(command.StationId);
            }
            else
            {
                throw new EntityAlreadyExistsException(
                    $"Station with station id '{command.StationId}' already exists.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }

        #endregion
    }