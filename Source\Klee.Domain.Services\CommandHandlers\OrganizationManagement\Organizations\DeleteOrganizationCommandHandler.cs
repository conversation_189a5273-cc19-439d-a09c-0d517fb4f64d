using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Commands.OrganizationManagement.Organizations;
using Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations.Helpers;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.OrganizationManagement.Organizations
{
    public sealed class DeleteOrganizationCommandHandler
        : RequestHandlerAsync<DeleteOrganizationCommand>
    {
        #region PROPERTIES
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public DeleteOrganizationCommandHandler(IOrganizationSrpRepository organizationSrpRepository,
                                                IMemoryCache memoryCache)
        {
            OrganizationSrpRepository = organizationSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<DeleteOrganizationCommand> HandleAsync(DeleteOrganizationCommand command,
                                                                          CancellationToken cancellationToken = new CancellationToken())
        {
            // Find Organization
            Organization organization = await OrganizationSrpRepository.FindAsync(_ => _.OrganizationId == command.OrganizationId &&
                                                                                       _.EntityPartitionKey == command.OrganizationId, command);

            if (organization != null)
            {
                // Set IsActive to false (soft delete)
                organization.IsActive = false;

                // Update
                await OrganizationSrpRepository.UpdateAsync(organization, command);

                // Clear Caches
                MemoryCache.RemoveOrganization(command.OrganizationId);
            }
            else
            {
                throw new EntityNotFoundException(
                    $"Organization with organization id '{command.OrganizationId}' not found.");
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
} 