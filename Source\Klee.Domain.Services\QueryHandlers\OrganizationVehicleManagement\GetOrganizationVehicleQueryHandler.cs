﻿using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Threading.Tasks;
using System.Threading;
using System;
using Klee.Domain.Services.UserContextService;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;

namespace Klee.Domain.Services.QueryHandlers.OrganizationVehicleManagement;

public sealed class GetOrganizationVehicleQueryHandler
    : QueryHandlerAsync<GetOrganizationVehicleQuery, Vehicle>
{
    #region PROPERTIES
    private IVehicleSrpRepository VehicleSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationVehicleQueryHandler(IVehicleSrpRepository vehicleSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.VehicleSrpRepository = vehicleSrpRepository;
        this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<Vehicle> ExecuteAsync(GetOrganizationVehicleQuery query,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Init
        string vehicleId = query.VehicleId;

        // Get Vehicle
        Vehicle vehicle = await this.VehicleSrpRepository.FindAsync(_ => _.VehicleId == vehicleId &&
                                                                         _.EntityPartitionKey == vehicleId, query);

        //Check if organization of logged in user is the same as vehicle organization
   
        string userOrganizationId = await this.UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);
        if (userOrganizationId != vehicle.OrganizationId)
        {
            throw new UnauthorizedAccessException("You do not have access to this vehicle.");
        }
        
        return vehicle;
    }
    #endregion
}