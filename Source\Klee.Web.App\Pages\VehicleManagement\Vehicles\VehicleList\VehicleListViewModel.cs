﻿using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList.Data;
using EnumsNET;
using ReactiveUI;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Pages.Common;

namespace Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList
{
    public class VehicleListViewModel
        : ViewModelBase<VehicleListViewModel>, IRViewModelWithUserSessionData<UserSessionData>
    {
        #region PROPERTIES - STATIC
        // Selectable
        public static IList<VehiclesMainFilterTypeIds> SelectableVehiclesMainFilterTypeIds { get; }
            = Enums.GetMembers<VehiclesMainFilterTypeIds>()
                   .Where(_ => _.Value != VehiclesMainFilterTypeIds.None)
                   .Select(_ => _.Value).ToList();
        #endregion

        #region FIELDS
        private VehiclesMainFilterTypeIds _vehiclesMainFilterTypeId = VehiclesMainFilterTypeIds.None;
        #endregion

        #region PROPERTIES
        public VehiclesMainFilterTypeIds VehiclesMainFilterTypeId
        {
            get => this._vehiclesMainFilterTypeId;
            set => this.RaiseAndSetIfChanged(ref this._vehiclesMainFilterTypeId, value);
        }

        // Actual Loaded
        public VehiclesMainFilterTypeIds LoadedVehiclesMainFilterTypeId { get; private set; }

        //
        public IReadOnlyList<VehicleListItem> Vehicles { get; private set; } = new List<VehicleListItem>();
        #endregion

        #region CONSTRUCTORS
        public VehicleListViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS - OVERRIDES
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
        }
        #endregion

        #region METHODS - USER SESSION DATA
        public void SetUserSessionData(UserSessionData userSessionData)
        {
            this.VehiclesMainFilterTypeId = userSessionData.VehiclesMainFilterTypeId;
        }

        public UserSessionData GetUserSessionData()
        {
            return new UserSessionData()
                   {
                       VehiclesMainFilterTypeId = this.LoadedVehiclesMainFilterTypeId
                   };
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadVehiclesAsync(bool forceLoad = false)
        {
            // Adjust Properties
            this.VehiclesMainFilterTypeId = this.VehiclesMainFilterTypeId != VehiclesMainFilterTypeIds.None ? this.VehiclesMainFilterTypeId : VehiclesMainFilterTypeIds.All;

            // Load Vehicles
            if (this.LoadedVehiclesMainFilterTypeId != this.VehiclesMainFilterTypeId ||
                forceLoad)
            {
                // Load Vehicles
                switch (this.VehiclesMainFilterTypeId)
                {
                    case VehiclesMainFilterTypeIds.Active:
                        // Load Vehicles
                        this.Vehicles = await this.SrpQueryProcessor.ExecuteAsync(new GetVehicleListQuery(this.SrpQueryContext)
                                                                               {
                                                                                   IsActive = true
                                                                               });
                        break;
                    case VehiclesMainFilterTypeIds.All:
                    default:
                        // Load Vehicles
                        this.Vehicles = await this.SrpQueryProcessor.ExecuteAsync(new GetVehicleListQuery(this.SrpQueryContext));
                        break;
                }

                // Set
                this.LoadedVehiclesMainFilterTypeId = this.VehiclesMainFilterTypeId;

                // Notify
                await this.InvokeStateHasChangedOnHostAsync();

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task DeleteVehicleAsync(string vehicleId)
        {
            try
            {
                // Delete Vehicle
                //await this.SrpCommandProcessor.SendAsync(new DeleteVehicleCommand(vehicleId,
                //                                                               this.SrpCommandContext));

                // Load Vehicles
                await this.LoadVehiclesAsync(forceLoad: true);
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }
}