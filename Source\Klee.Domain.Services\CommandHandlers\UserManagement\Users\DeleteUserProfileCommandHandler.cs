﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Messages.Commands.UserManagement.Users;
using Klee.Domain.Services.CommandHandlers.UserManagement.Users.Helpers;
using Klee.Domain.Services.Repositories.UserManagement.Users;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.UserManagement.Users;

public sealed class DeleteUserProfileCommandHandler
    : RequestHandlerAsync<DeleteUserProfileCommand>
{
    #region PROPERTIES
    private IUserProfileSrpRepository UserProfileSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }
    #endregion

    #region CONSTRUCTORS
    public DeleteUserProfileCommandHandler(IUserProfileSrpRepository userProfileSrpRepository,
        IMemoryCache memoryCache)
    {
        UserProfileSrpRepository = userProfileSrpRepository;
        MemoryCache = memoryCache;
    }
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    public override async Task<DeleteUserProfileCommand> HandleAsync(DeleteUserProfileCommand command,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Find UserProfile
        UserProfile userProfile = await UserProfileSrpRepository.FindAsync(_ => _.UserId == command.UserId &&
                                                                    _.EntityPartitionKey == command.UserId, command);

        if (userProfile != null)
        {
            // Set IsActive to false (soft delete)
            userProfile.IsActive = false;

            // Update
            await UserProfileSrpRepository.UpdateAsync(userProfile, command);

            // Clear Caches
            MemoryCache.RemoveUserProfile(command.UserId);
        }
        else
        {
            throw new EntityNotFoundException(
                $"UserProfile with UserProfile id '{command.UserId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }
    #endregion
}