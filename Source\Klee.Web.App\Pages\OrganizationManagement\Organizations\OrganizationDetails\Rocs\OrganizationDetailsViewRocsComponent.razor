﻿@using Klee.Domain.Messages.Queries.RocManagement.Rocs.Data
@using Klee.Web.App.Pages.RocManagement.Rocs.RocList
@using Klee.Web.App.Pages.RocManagement.Rocs.RocList.Data
@inherits OrganizationDetailsViewRocsComponentBase

<section class="card">
    <header class="card-header">
        <h2 class="card-title">Overview</h2>
        <div class="card-actions">
            <RCardActionShowFilter @ref="@RCardActionShowFilterRow" />
            <RCardActionShowGridColumnSelection @ref="@RCardActionShowGridColumnSelection" />
            <RCardActionToggle />
        </div>
    </header>
    <div class="card-body">
        <DxToolbar>
            <Items>
                <DxToolbarItem>
                    <Template>
                        <div class="control-value pe-2" style="width: 100px">
                            <DxComboBox Data="@RocListViewModel.SelectableRocsMainFilterTypeIds"
                                        ListRenderMode="@ListRenderMode.Entire"
                                        FilteringMode="@DataGridFilteringMode.None"
                                        ClearButtonDisplayMode="@DataEditorClearButtonDisplayMode.Never"
                                        Value="@ViewModel.RocsMainFilterTypeId"
                                        ValueChanged="@((RocsMainFilterTypeIds rocsMainFilterTypeId) => OnValueChanged_RocsMainFilterTypeId(rocsMainFilterTypeId))"
                                        SizeMode="SizeMode.Small" />
                        </div>
                    </Template>
                </DxToolbarItem>
            </Items>
        </DxToolbar>

        <DxGrid Data="@this.ViewModel.Rocs"
                @ref="@DxGrid"
                ShowFilterRow="@ListViewProperties.DxGridShowFilterRow"
                SelectionMode="GridSelectionMode.Multiple"
                FocusedRowEnabled="@ListViewProperties.DxGridFocusedRowEnabled"
                ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                PageSize="15"
                PageSizeSelectorVisible="true"
                PageSizeSelectorAllRowsItemVisible="true"
                PageSizeSelectorItems=@(new int[] { 5, 10, 15, 20, 50 })
                PagerNavigationMode="PagerNavigationMode.InputBox"
                PageIndex="@ListViewProperties.DxGridPageIndex"
                PageIndexChanged="@OnDxGrid_PageIndexChanged"
                LayoutAutoLoading="@OnDxGrid_LayoutAutoLoading"
                LayoutAutoSaving="@OnDxGrid_LayoutAutoSaving"
                FocusedRowChanged="OnDxGrid_FocusedRowChanged"
                SizeMode="SizeMode.Small">
            <Columns>
                <DxGridSelectionColumn Width="40px"
                                       VisibleIndex="1"
                                       Visible="@ListViewProperties.DxGridShowGridColumnSelection" />
                <RDxGridCommandsDataColumn OnClickViewDetails="OnDxGrid_ClickViewDetails"
                                           OnClickDelete="OnDxGrid_ClickDelete"
                                           CanShowButtonDelete=true
                                           ButtonType="RDxGridCommandsDataColumnButtonTypes.Text"
                                           VisibleIndex="2" />
                <DxGridDataColumn FieldName="@nameof(RocListItem.RocId)" Caption="Roc Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="3" />
                <DxGridDataColumn FieldName="@nameof(RocListItem.RocName)" Caption="Roc Name" Width="200px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="4" />
                <DxGridDataColumn FieldName="@nameof(RocListItem.OrganizationId)" Caption="Organization Id" Width="100px" TextAlignment="@GridTextAlignment.Left" VisibleIndex="5" />
                @if (this.ViewModel.RocsMainFilterTypeId != RocsMainFilterTypeIds.Active)
                {
                    <DxGridDataColumn FieldName="@nameof(RocListItem.IsActive)"
                                      Width="60px" VisibleIndex="6">
                        <CellDisplayTemplate>
                            <DxCheckBox CssClass="d-inline-block" Enabled="false" Checked="(bool)context.Value" />
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                }
                <RDxGridEmptyDataColumn />
            </Columns>
        </DxGrid>

    </div>
</section>

@code {
}
