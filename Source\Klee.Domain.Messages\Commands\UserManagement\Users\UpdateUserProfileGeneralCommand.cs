﻿using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.UserManagement.Users;

public class UpdateUserProfileGeneralCommand: CommandBase {
    #region RESULT CLASS
    public class CommandResult
    {
        public long EntityId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string UserId { get; }

    public string FirstName { get; set; } = "";
    public string LastName { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string Email{ get; set; } = "";

    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public UpdateUserProfileGeneralCommand(string userId,
        ICommandContext commandContext)
        : base(commandContext)
    {
        this.UserId = userId;
    }
    #endregion
}