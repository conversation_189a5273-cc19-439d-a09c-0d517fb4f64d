using AntDesign;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class SelectCaptainViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public DateTime VoyageStartDateTime { get; set; }
    [Parameter] public DateTime VoyageEndDateTime { get; set; }
    [Parameter] public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    [Parameter] public EventCallback OnClick_Back { get; set; }
    [Parameter] public EventCallback<AvailableCaptainListItem> OnSelectCaptain { get; set; }
    [Parameter] public EventCallback OnCreateIncompleteVoyage { get; set; }
    #endregion

    #region PROPERTIES
    protected SelectCaptainViewModel ViewModel { get; set; }
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new SelectCaptainViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        ViewModel.SetVoyageCriteria(VoyageStartDateTime, VoyageEndDateTime, RequiredQualifications);
        await LoadCaptainsAsync();
        
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA LOADING
    protected async Task LoadCaptainsAsync()
    {
        try
        {
            await ViewModel.LoadAvailableCaptainsAsync();
            StateHasChanged();
        }
        catch (Exception ex)
        { 
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load available captains. Please try again.",
                Duration = 4.5
            });
        }
    }
    #endregion

    #region METHODS - NAVIGATION
    protected async Task HandleOnClick_Back()
    {
        await OnClick_Back.InvokeAsync();
    }

    protected async Task HandleSelectCaptain(AvailableCaptainListItem captain)
    {
        await OnSelectCaptain.InvokeAsync(captain);
    }

    protected async Task HandleCreateIncompleteVoyage()
    {
        await OnCreateIncompleteVoyage.InvokeAsync();
    }
    #endregion

    #region METHODS - WORKING DAYS DISPLAY
    protected List<string> GetWorkingDaysDisplay(dynamic workingDaysFlags)
    {
        var days = new List<string>();
        var flags = (int)workingDaysFlags;
        
        if ((flags & 1) != 0) days.Add("Mon");
        if ((flags & 2) != 0) days.Add("Tue");
        if ((flags & 4) != 0) days.Add("Wed");
        if ((flags & 8) != 0) days.Add("Thu");
        if ((flags & 16) != 0) days.Add("Fri");
        if ((flags & 32) != 0) days.Add("Sat");
        if ((flags & 64) != 0) days.Add("Sun");
        
        return days.Any() ? days : new List<string> { "Not specified" };
    }
    #endregion
}
