using AntDesign;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class VoyageOrderConfirmationViewBase : ComponentBase
{
    #region SERVICES
    [Inject]
    protected ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    protected INotificationService NotificationService { get; set; }

    [Inject]
    protected IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PARAMETERS
    [Parameter] public string SelectedVesselId { get; set; } = "";
    [Parameter] public DateTime VoyageStartDateTime { get; set; }
    [Parameter] public DateTime VoyageEndDateTime { get; set; }
    [Parameter] public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    [Parameter] public string Description { get; set; } = "";
    [Parameter] public AvailableCaptainListItem? SelectedCaptain { get; set; } = null;
    [Parameter] public EventCallback OnClick_Back { get; set; }
    [Parameter] public EventCallback OnVoyageConfirmed { get; set; }
    #endregion

    #region PROPERTIES
    public VoyageOrderConfirmationViewModel ViewModel { get; set; }
    protected bool IsLoading { get; set; } = true;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
        ViewModel = new VoyageOrderConfirmationViewModel(SrpProcessors);
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (SelectedCaptain != null)
        {
            ViewModel.SetVoyageData(SelectedVesselId, VoyageStartDateTime, VoyageEndDateTime, RequiredQualifications, Description, SelectedCaptain);
            await LoadDataAsync();
        }
        
        await base.OnParametersSetAsync();
    }
    #endregion

    #region METHODS - DATA LOADING
    protected async Task LoadDataAsync()
    {
        IsLoading = true;
        
        try
        {
            await ViewModel.LoadVesselInfoAsync();
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to load voyage data. Please try again.",
                Duration = 4.5
            });
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    #endregion

    #region METHODS - NAVIGATION
    protected async Task HandleOnClick_Back()
    {
        await OnClick_Back.InvokeAsync();
    }

    protected async Task HandleConfirmVoyage()
    {
        try
        {
            // Get current user's organization
            string userOrgId = await UserAuthenticationService.GetCurrentUserOrganizationIdAsync();
            
            // Create voyage and invoice
            bool success = await ViewModel.CreateVoyageAsync(userOrgId);
            
            if (success)
            {
                NotificationService.Success(new NotificationConfig()
                {
                    Message = "Voyage Confirmed",
                    Description = $"Your voyage with Captain {SelectedCaptain?.FullName} has been successfully booked!",
                    Duration = 4.5
                });

                await OnVoyageConfirmed.InvokeAsync();
            }
            else
            {
                // Check if it was a concurrency conflict
                if (ViewModel.HasConcurrencyConflict)
                {
                    NotificationService.Error(new NotificationConfig()
                    {
                        Message = "Captain No Longer Available",
                        Description = ViewModel.ConcurrencyErrorMessage,
                        Duration = 6.0
                    });
                }
                else
                {
                    NotificationService.Error(new NotificationConfig()
                    {
                        Message = "Booking Failed",
                        Description = "Failed to confirm your voyage. Please try again.",
                        Duration = 4.5
                    });
                }

                ViewModel.ClearConcurrencyConflict();
                await OnClick_Back.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "An unexpected error occurred while confirming your voyage. Please try again.",
                Duration = 4.5
            });
        }
    }
    #endregion
}
