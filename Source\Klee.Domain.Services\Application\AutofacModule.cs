﻿using System.Linq;
using Autofac;
using Paramore.Brighter;
using Paramore.Darker;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.Application
{
    public class AutofacModule : Module
    {
        protected override void Load(ContainerBuilder containerBuilder)
        {
            // Register "Brighter" CommandHandlers
            {
                // General
                foreach (var type in this.ThisAssembly.GetTypes()
                                         .Where(_ => _.IsAbstract == false)
                                         .Where(_ => (_.IsGenericType == false) ||
                                                     (_.IsGenericType == true && _.GetGenericTypeDefinition() != typeof(RequestValidationCommandHandler<>)))
                                         .Where(_ => (_.IsGenericType == false) ||
                                                     (_.IsGenericType == true && _.GetGenericTypeDefinition() != typeof(RequestContextCommandHandler<>)))
                                         .Where(_ => _.GetInterfaces().Contains(typeof(IHandleRequestsAsync)) ||
                                                     _.GetInterfaces().Contains(typeof(IHandleRequests))))
                {
                    containerBuilder.RegisterType(type).AsSelf().AsImplementedInterfaces();
                }

                // RequestValidation 
                containerBuilder.RegisterGeneric(typeof(RequestValidationCommandHandler<>)).AsSelf();

                // RequestContext 
                containerBuilder.RegisterGeneric(typeof(RequestContextCommandHandler<>)).AsSelf();
            }

            // Register "Darker" QueryHandlers
            {
                foreach (var type in this.ThisAssembly.GetTypes()
                                         .Where(_ => _.IsAbstract == false)
                                         .Where(_ => _.GetInterfaces().Contains(typeof(IQueryHandler))))
                {
                    containerBuilder.RegisterType(type).AsSelf().AsImplementedInterfaces();
                }
            }
        }
    }
}
