﻿using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.OrganizationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.OrganizationManagement.Organizations;

    public sealed class GetOrganizationNameListQueryHandler
        : QueryHandlerAsync<GetOrganizationNameListQuery, IReadOnlyList<OrganizationNameListItem>>
    {
        #region PROPERTIES
        private IOrganizationSrpRepository OrganizationSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationNameListQueryHandler(IOrganizationSrpRepository organizationSrpRepository,
                                                IMemoryCache memoryCache)
        {
            this.OrganizationSrpRepository = organizationSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<OrganizationNameListItem>> ExecuteAsync(GetOrganizationNameListQuery query,
                                                                                     CancellationToken cancellationToken = new CancellationToken())
        {
            // Get Organizations from cache
            var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetOrganizationNameListCacheId, out List<OrganizationNameListItem> cachedOrganizations);

            // Init
            List<OrganizationNameListItem> organizations = cachedOrganizations ?? new List<OrganizationNameListItem>();

            // Get Organizations from DB (if needed)
            if (query.AllowCached == false || !isCached)
            {
                // Get Organizations
                organizations = await this.OrganizationSrpRepository.Entities(query)
                                  .OrderBy(_ => _.OrganizationId)
                                  .Select(_ => new OrganizationNameListItem
                                  {
                                      EntityId = _.EntityId,
                                      OrganizationId = _.OrganizationId,
                                      Name = _.Name,
                                  })
                                  .ToListAsync(cancellationToken: cancellationToken);

                // Cache Organizations
                this.MemoryCache.Set(MemoryCacheIds.GetOrganizationNameListCacheId,
                                     organizations, new MemoryCacheEntryOptions()
                                     {
                                         AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                                         Size = 1
                                     });
            }

            return organizations;
        }
        #endregion
    }