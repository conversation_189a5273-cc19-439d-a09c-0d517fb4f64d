﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators.Helpers;

namespace Klee.Domain.Services.CommandHandlers.OperatorManagement.Operators;

    public sealed class CreateOperatorCommandHandler
        : RequestHandlerAsync<CreateOperatorCommand>
    {
        #region PROPERTIES
        private IOperatorSrpRepository OperatorSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public CreateOperatorCommandHandler(IOperatorSrpRepository operatorSrpRepository,
            IMemoryCache memoryCache)
        {
            OperatorSrpRepository = operatorSrpRepository;
            MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateOperatorCommandValidator))]
        public override async Task<CreateOperatorCommand> HandleAsync(CreateOperatorCommand command,
            CancellationToken cancellationToken = new CancellationToken())
        {
          
            // Create
            Operator operatorObj = new Operator()
            {
                FirstName = command.OperatorFirstName,
                LastName = command.OperatorLastName,
                OperatorEmail = command.OperatorEmail,
                HourlyRateInEuros = command.HourlyRateInEuros,
                SoftwareEnvironmentId = command.SoftwareEnvironmentId,
                OrganizationId = command.OrganizationId,
                Qualifications = command.Qualifications
            };

            // Save 
            await OperatorSrpRepository.AddAsync(operatorObj, command);

            // Set Result
            command.Result.EntityId = operatorObj.EntityId;

            // Clear Caches
            MemoryCache.RemoveOperator(operatorObj.OperatorId);

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }

        #endregion
    }