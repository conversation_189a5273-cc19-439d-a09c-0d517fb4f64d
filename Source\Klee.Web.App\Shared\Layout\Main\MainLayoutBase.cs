﻿using System.Security.Claims;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Identity.Web;
using Renoir.Application.Security.Helpers;
using Renoir.Srp.Portal.Web.Pages.Common;
using Renoir.Web.Razor.Components.DetectPreRendering;
using Renoir.Web.Razor.Services.GoogleAnalytics;

namespace Klee.Web.App.Shared.Layout.Main
{
    public class MainLayoutBase : LayoutViewBase
    {
        #region FIELDS
        protected ErrorBoundary? _errorBoundary;
        #endregion

        #region PROPERTIES
        [CascadingParameter(Name = nameof(IsPreRendering))]
        protected bool IsPreRendering { get; set; }

        [Inject]
        private IPreRenderFlag PreRenderFlag { get; set; }

        [Inject]
        private IGoogleAnalyticService GoogleAnalytics { get; set; }

        private bool IsUserSessionStartedNotified { get; set; }
        private bool IsGoogleAnalyticsInitialized { get; set; }
        #endregion

        #region CONSTRUCTORS
        public MainLayoutBase()
        {
        }
        #endregion

        #region NETHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Initialize Authenticated User (if needed)
            await this.InitializeAuthenticatedUserIfStillNeededAsync();

            // Prerendering
            if (this.PreRenderFlag.IsPreRendering)
            {
                // Notify
                //await this.NotifyUserSessionStartedIfStillNeededAsync();
            }

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            // Prerendering
            if (this.PreRenderFlag.IsPreRendering)
            {
                // Notify
                //await this.NotifyUserSessionStartedIfStillNeededAsync();
            }

            //
            this._errorBoundary?.Recover();
            
            //
            await base.OnParametersSetAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            // Notify
            //await this.NotifyUserSessionStartedIfStillNeededAsync();

            // Initialize Google Analytics
            await this.InitializeGoogleAnalyticsAsync();

            //
            await base.OnAfterRenderAsync(firstRender);
        }
        #endregion

        #region METHODS
        private async Task InitializeGoogleAnalyticsAsync()
        {
            if (!this.IsGoogleAnalyticsInitialized)
            {
                try
                {
                    // Init
                    ClaimsPrincipal user = (await this.AuthenticationStateTask).User;

                    if (user != null)
                    {
                        var userId = user.GetUserIdAsHashMD5();
                        var globalConfigData = new Dictionary<string, object>();
                        globalConfigData["user_id"] = userId;
                        var globalEventData = new Dictionary<string, object>();
                        globalEventData["user_id"] = userId;

                        //
                        await this.GoogleAnalytics.ConfigureGlobalConfigData(globalConfigData);
                        await this.GoogleAnalytics.ConfigureGlobalEventData(globalEventData);

                        //
                        this.IsGoogleAnalyticsInitialized = true;
                    }
                }
                catch (Exception exception)
                {
                    #region Logging
                    this.Logger.LogError(exception, "Error on initializing Google Analytics.");
                    #endregion

                    //throw;
                }
            }
        }

        private async Task NotifyUserSessionStartedIfStillNeededAsync()
        {
            // Init
            ClaimsPrincipal user = (await this.AuthenticationStateTask).User;

            // Notify User Session Started (if possible & still needed)
            if (!this.IsUserSessionStartedNotified &&
                user.IsAuthenticated())
            {
                try
                {
                    // Notify User Session Started
                    //await this.ServiceProvider.GetService<IUserSessionService>().NotifyUserSessionStartedAsync(user);

                    //
                    this.IsUserSessionStartedNotified = true;
                }
                catch (Exception exception)
                {
                    #region Logging
                    this.Logger.LogError(exception, "Error on notify user session started.");
                    #endregion

                    //throw;
                }
            }
        }
        #endregion
    }
}
