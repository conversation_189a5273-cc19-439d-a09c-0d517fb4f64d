﻿namespace Klee.Domain.Messages.Queries.UserManagement.Users.Data;

public class UserProfileListItem {
    
    #region PROPERTIES
    public long EntityId { get; set; }

    public string UserId { get; set; } = "";
    public string FirstName { get; set; } = "";

    public string LastName { get; set; } = "";

    public string DisplayName { get; set; } = "";
    public string Email { get; set; } = "";
    public string OrganizationId { get; set; } = "";

    public bool IsActive { get; set; }
    #endregion
}