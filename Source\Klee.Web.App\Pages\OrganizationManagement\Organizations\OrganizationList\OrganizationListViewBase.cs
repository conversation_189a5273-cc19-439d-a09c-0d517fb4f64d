using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationCreate;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationDetails;
using Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList.Data;
using Renoir.Srp.Portal.Web.Pages.Common.Views.ListViews;
using Renoir.Web.Razor.Components.Dialogs.Data;
using Renoir.Web.Razor.Components.DxGrids.Events;
using Renoir.Web.Razor.Pages.Dialogs.Events;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;
using DevExpress.Blazor;

namespace Klee.Web.App.Pages.OrganizationManagement.Organizations.OrganizationList
{
    public partial class OrganizationListViewBase 
        : LayoutBodyListDxGridViewBase<OrganizationListViewModel, OrganizationListItem, UserSessionData>
    {
        #region FIELDS
        #endregion

        #region PROPERTIES
        // Dialog Views
        protected OrganizationCreateDialogView OrganizationCreateDialogView { get; set; }
        #endregion

        #region CONSTRUCTORS
        protected OrganizationListViewBase()
        {
        }
        #endregion

        #region IDISPOSABLE
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
        }
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new OrganizationListViewModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Load Organizations
                if (await this.ViewModel.LoadOrganizationsAsync())
                {
            
                }

                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                if (!this.IsDisposed)
                {
                    // Notify
                    await this.UserNotificationService.ShowErrorAsync(exception.Message);
                }

                //throw;
            }
        }

        protected override async Task OnAfterRenderFirstAsync()
        {
            await base.OnAfterRenderFirstAsync();
        }

        protected override async Task OnUserSessionDataSaveAsync()
        {
            await base.OnUserSessionDataSaveAsync();
        }

        protected override async Task OnUserSessionDataRestoreAsync()
        {
            await base.OnUserSessionDataRestoreAsync();
        }

        protected override bool ShouldRender()
        {
            return base.ShouldRender();
        }
        #endregion

        #region METHODS
        #endregion

        #region EVENT HANDLERS - DX DATA GRID
        
        protected async Task OnDxGrid_ClickViewDetails(RDxGridRowViewDetailsMouseEventArgs e)
        {
            // Init
            OrganizationListItem organizationListItem = e.GetDataItem() as OrganizationListItem ?? new OrganizationListItem();
            string organizationId = organizationListItem.OrganizationId ?? "";

            try
            {
                await this.NavigateToAsync(OrganizationDetailsView.GetUri(organizationId), e.MustOpenInBlankBrowser);
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to organization details of organization '{organizationId}'", organizationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        protected async Task OnDxGrid_ClickDelete(RDxGridRowDeleteMouseEventArgs e)
        {
            // Init
            OrganizationListItem organizationListItem = e.GetDataItem() as OrganizationListItem ?? new OrganizationListItem();
            string organizationId = organizationListItem.OrganizationId ?? "";

            try
            {
                if (await this.DialogBox.AskConfirmationForWarningAsync("Delete Organization?",
                                                                        $"Delete organization with id '{organizationId}'?") == RDialogResult.Ok)
                {
                    // Delete Organization
                    await this.ViewModel.DeleteOrganizationAsync(organizationId);

                    // Notify
                    await this.UserNotificationService.ShowSuccessAsync("Deleted", $"Organization with id '{organizationId}' is deleted.");
                }
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when deleting organization with id '{organizationId}'", organizationId);
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected async Task OnClickNew()
        {
            try
            {
                await this.OrganizationCreateDialogView.OpenDialogViewAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                //this.Logger.LogError(exception, "Exception when opening dialog '{dialogViewName}'", nameof(this.OrganizationCreateDialogView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }

        public async void OnValueChanged_OrganizationsMainFilterTypeId(OrganizationsMainFilterTypeIds organizationsMainFilterTypeId)
        {
            try
            {
                // Set
                this.ViewModel.OrganizationsMainFilterTypeId = organizationsMainFilterTypeId;

                // Load
                // Todo: Could load be based on property change of OrganizationsMainFilterType?
                await this.ViewModel.LoadOrganizationsAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when selecting filter type in '{viewName}'", nameof(OrganizationListView));
                #endregion

                // Notify
                await this.UserNotificationService.ShowErrorAsync(exception.Message);

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS - ORGANIZATION CREATE DIALOG VIEW
        protected void OnOrganizationCreateDialogView_Opened()
        {
        }

        protected async Task OnOrganizationCreateDialogView_Closed(RDialogViewClosedEventArgs e)
        {
            switch (e.DialogResult)
            {
                case RDialogResult.Ok:
                    // Refresh the page to show the newly created organization
                    this.NavigationManager.NavigateTo(this.NavigationManager.Uri, forceLoad: true);
                    break;

                default:
                    break;
            }
        }
        #endregion
    }
} 