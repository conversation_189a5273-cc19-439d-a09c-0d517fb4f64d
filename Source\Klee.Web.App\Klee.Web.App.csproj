﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Shared\Styles\App.css" />
  </ItemGroup>

	<ItemGroup>
    <PackageReference Include="AntDesign" Version="1.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="6.0.36" />
    <PackageReference Include="DevExpress.Blazor" Version="24.1.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.14" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.0" />
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.4.0" />
    <PackageReference Include="Renoir.Srp.Portal.Web.Core" Version="1.1.25078.1-beta" />
    <PackageReference Include="Renoir.Web.Razor" Version="1.1.25078.1-beta" />
    <PackageReference Include="Renoir.Web.Razor.Extra" Version="1.1.25078.1-beta" />
  </ItemGroup>

	<ItemGroup>
	  <Folder Include="Pages\UserManagement\Users\UserProfileDetails\Data\" />
	  <Folder Include="wwwroot\Styles\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Klee.Domain.Entities\Klee.Domain.Entities.csproj" />
	  <ProjectReference Include="..\Klee.Domain.Messages\Klee.Domain.Messages.csproj" />
	  <ProjectReference Include="..\Klee.Domain.Services\Klee.Domain.Services.csproj" />
	  <ProjectReference Include="..\Klee.Infrastructure\Klee.Infrastructure.csproj" />
	  <ProjectReference Include="..\Klee.Web.App.Client\Klee.Web.App.Client.csproj" />
	</ItemGroup>

</Project>
