﻿using Autofac;
using Klee.Infrastructure.Repositories.OrganizationManagement;
using Klee.Infrastructure.Repositories.RocManagement;
using Klee.Infrastructure.Repositories.VehicleManagement;
using Klee.Infrastructure.Repositories.VoyageManagement;
using Klee.Infrastructure.Repositories.InvoiceManagement;
using Module = Autofac.Module;

namespace Klee.Infrastructure.Application
{
    public class AutofacModule : Module
    {
        protected override void Load(ContainerBuilder containerBuilder)
        {
            // Register Entity Specific Repositories
            {
                // Register from Renoir.Srp.Infrastructure.dll
                foreach (var type in typeof(VehicleSrpRepository)
                                     .Assembly.GetTypes()
                                     .Where(_ => _.IsAbstract == false)
                                     .Where(_ => _.GetInterfaces().Contains(typeof(Renoir.Application.Domain.IDomainEntityRepositoryBasic))))
                {
                    containerBuilder.RegisterType(type).AsImplementedInterfaces();
                }

                foreach (var type in typeof(OrganizationSrpRepository)
                             .Assembly.GetTypes()
                             .Where(_ => _.IsAbstract == false)
                             .Where(_ => _.GetInterfaces().Contains(typeof(Renoir.Application.Domain.IDomainEntityRepositoryBasic))))
                {
                    containerBuilder.RegisterType(type).AsImplementedInterfaces();
                }

                foreach (var type in typeof(RocSrpRepository)
                             .Assembly.GetTypes()
                             .Where(_ => _.IsAbstract == false)
                             .Where(_ => _.GetInterfaces().Contains(typeof(Renoir.Application.Domain.IDomainEntityRepositoryBasic))))
                {
                    containerBuilder.RegisterType(type).AsImplementedInterfaces();
                }
            }

            // Register Background Jobs
            {
                //// Register from Renoir.Srp.Infrastructure.dll (e.g Report JobExecutor Jobs)
                //foreach (var type in typeof(Renoir.Srp.Infrastructure.ReportJobs.BackgroundJobs.JobRunners.Types.JobsByPeriods.ReportByDayJobs.ReportByDayJobExecutorJob)
                //                     .Assembly.GetTypes()
                //                     .Where(_ => _.IsAbstract == false)
                //                     .Where(_ => _.GetInterfaces().Contains(typeof(Renoir.Application.Jobs.BackgroundJobs.JobRunners.IJobBase))))
                //{
                //    containerBuilder.RegisterType(type).AsImplementedInterfaces();
                //}

                //// Register from Renoir.Srp.BackgroundJobs.Jobs.dll (e.g. General jobs)
                //foreach (var type in typeof(Renoir.Srp.BackgroundJobs.Jobs.SystemsTesting.BackgroundJobs.Test0001PrintConsoleTextJobs.Test0001PrintConsoleTextJob)
                //                     .Assembly.GetTypes()
                //                     .Where(_ => _.IsAbstract == false)
                //                     .Where(_ => _.GetInterfaces().Contains(typeof(Renoir.Application.Jobs.BackgroundJobs.JobRunners.IJobBase))))
                //{
                //    containerBuilder.RegisterType(type).AsImplementedInterfaces();
                //}
            }
        }
    }
}