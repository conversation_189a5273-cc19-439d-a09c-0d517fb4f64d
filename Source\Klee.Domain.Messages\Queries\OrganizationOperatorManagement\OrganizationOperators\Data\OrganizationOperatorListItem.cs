﻿using System;
using System.Collections.Generic;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;

namespace Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators.Data;

public class OrganizationOperatorListItem {
    #region PROPERTIES
    public long EntityId { get; set; }
    public string OperatorId { get; set; } = "";
    public string OperatorFirstName { get; set; } = "";
    public string OperatorLastName { get; set; } = "";
    public string OperatorDisplayName { get; set; } = "";
    public string Email { get; set; } = "";
    public double HourlyRateInEuros { get; set; } = 0.0;
    public List<QualificationTypeIds> Qualifications { get; set; } = new();
    public int YearsOfExperience { get; set; } = 0;
    public int YearsOfRemoteExperience { get; set; } = 0;
    public WeekDaysIds WeekDays { get; set; } = WeekDaysIds.None;
    public DateTime RegularStartTime { get; set; } = DateTime.Today.AddHours(9);
    public DateTime RegularEndTime { get; set; } = DateTime.Today.AddHours(17);
    public string Biography { get; set; } = "";
    #endregion
}