﻿using System;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Renoir.Application.Messages.Queries.Common;
using System.Collections.Generic;

namespace Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;

public class GetAvailableOrganizationVehicleListForJobProposalQuery
    : QueryBase<IReadOnlyList<OrganizationVehicleListItem>>
{
    #region PROPERTIES
    public DateTime JobOfferStartTime { get; }
    public DateTime JobOfferEndTime { get; }
    public bool AllowCached { get; set; } = false;
    #endregion

    #region CONSTRUCTORS
    public GetAvailableOrganizationVehicleListForJobProposalQuery(DateTime jobOfferStartTime, 
        DateTime jobOfferEndTime, IQueryContext context)
        : base(context)
    {
        this.JobOfferStartTime = jobOfferStartTime;
        this.JobOfferEndTime = jobOfferEndTime;
    }
    #endregion
}