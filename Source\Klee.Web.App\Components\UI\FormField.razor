@using Microsoft.AspNetCore.Components

<div class="space-y-2">
    <label class="block text-sm font-semibold text-gray-700"
           style="font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif;"
           for="@Id">
        @Label
        @if (IsRequired)
        {
            <span class="text-red-600 ml-1">*</span>
        }
    </label>
    <div class="w-full">
        @ChildContent
    </div>
</div>

@code {
    [Parameter]
    public string Label { get; set; } = "";

    [Parameter]
    public string Id { get; set; } = "";

    [Parameter]
    public bool IsRequired { get; set; } = false;

    [Parameter]
    public RenderFragment ChildContent { get; set; }
}