using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Klee.Domain.Services.UserContextService;
using Klee.Domain.Entities.UserManagement.Users;
using Renoir.Application.Security;
using AuthenticationState = Microsoft.AspNetCore.Components.Authorization.AuthenticationState;

namespace Klee.Web.App.Services.Authentication;

public class UserAuthenticationService : IUserAuthenticationService
{
    private readonly AuthenticationStateProvider _authenticationStateProvider;
    private readonly IUserContextHelperService _userContextHelperService;
    private readonly ILogger<UserAuthenticationService> _logger;

    public UserAuthenticationService(
        AuthenticationStateProvider authenticationStateProvider,
        IUserContextHelperService userContextHelperService,
        ILogger<UserAuthenticationService> logger)
    {
        _authenticationStateProvider = authenticationStateProvider;
        _userContextHelperService = userContextHelperService;
        _logger = logger;
    }

    /// <summary>
    /// Gets the current authenticated user using multiple fallback approaches for maximum reliability
    /// </summary>
    /// <returns>ClaimsPrincipal of the current authenticated user</returns>
    public async Task<ClaimsPrincipal> GetCurrentUserAsync()
    {
        try
        {
            AuthenticationState? authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal? user = authState?.User;

            if (user?.Identity?.IsAuthenticated == true)
            {
               return user;
            }

            _logger.LogWarning("AuthenticationStateProvider returned unauthenticated user");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user from AuthenticationStateProvider");
        }


        return new ClaimsPrincipal(new ClaimsIdentity());
    }

    /// <summary>
    /// Gets the current user's profile from the database
    /// </summary>
    /// <returns>UserProfile of the current user, or null if not found</returns>
    public async Task<UserProfile?> GetCurrentUserProfileAsync()
    {
        try
        {
            ClaimsPrincipal currentUser = await GetCurrentUserAsync();
            return await _userContextHelperService.GetUserProfileByClaimsAsync(currentUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user profile");
            return null;
        }
    }

    /// <summary>
    /// Gets the current user's organization ID
    /// </summary>
    /// <returns>Organization ID of the current user, or empty string if not found</returns>
    public async Task<string> GetCurrentUserOrganizationIdAsync()
    {
        try
        {
            ClaimsPrincipal currentUser = await GetCurrentUserAsync();
            return await _userContextHelperService.GetUserOrganizationIdByClaimsAsync(currentUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user organization ID");
            return string.Empty;
        }
    }

    /// <summary>
    /// Checks if the current user is an organization admin
    /// </summary>
    /// <returns>True if the current user is an organization admin, false otherwise</returns>
    public async Task<bool> IsCurrentUserOrganizationAdminAsync()
    {
        try
        {
            ClaimsPrincipal currentUser = await GetCurrentUserAsync();
            return _userContextHelperService.IsUserOrganizationAdmin(currentUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if current user is organization admin");
            return false;
        }
    }

    /// <summary>
    /// Checks if the current user is an admin for a specific organization
    /// </summary>
    /// <param name="orgId">Organization ID to check</param>
    /// <returns>True if the current user is an admin for the specified organization, false otherwise</returns>
    public async Task<bool> IsCurrentUserOrganizationAdminForOrganizationAsync(string orgId)
    {
        try
        {
            ClaimsPrincipal currentUser = await GetCurrentUserAsync();
            return await _userContextHelperService.IsUserOrganizationAdminForOrganizationAsync(currentUser, orgId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if current user is organization admin for organization {OrgId}", orgId);
            return false;
        }
    }

    /// <summary>
    /// Checks if a specific user is an organization admin (utility method)
    /// </summary>
    /// <param name="userClaimsPrincipal">User's ClaimsPrincipal</param>
    /// <returns>True if the user is an organization admin, false otherwise</returns>
    private bool IsUserOrganizationAdmin(ClaimsPrincipal userClaimsPrincipal)
    {
        return _userContextHelperService.IsUserOrganizationAdmin(userClaimsPrincipal);
    }
}
