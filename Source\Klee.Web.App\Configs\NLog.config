﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="error"
      internalLogFile="c:\temp\internal-nlog.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- log file uses some ASP.NET core renderers -->
    <target xsi:type="AsyncWrapper" 
            name="logfileAsync" 
            queueLimit="100000" 
            overflowAction="Grow" 
            batchSize="250">
      <target xsi:type="File"
              name="logfile"
              layout="${longdate} | ${processid} | ${event-properties:item=EventId_Id} | ${uppercase:${level}} | ${logger} | ${message} ${exception:format=tostring} | url: ${aspnet-request-url} | action: ${aspnet-mvc-action} | ${aspnet-user-identity} |END|"
              fileName="${basedir}\Logs\Web.Api.NLog.log"
              archiveFileName="${basedir}\Logs\Archives\Web.Api.NLog.archived.{########}.zip"
              archiveNumbering="Date"
              archiveEvery="Day"
              archiveDateFormat="yyyyMMdd"
              enableArchiveFileCompression="true"
              maxArchiveFiles="30"
              keepFileOpen="true"
              encoding="utf-8" 
              concurrentWrites="true" />
    </target>
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!--Skip non-critical Microsoft logs and so log only own logs-->
    <!--<logger name="Microsoft.*" maxLevel="Info" final="true" />-->
    <!-- BlackHole without writeTo -->
    <logger name="*" minlevel="Debug" writeTo="logfileAsync" />
  </rules>
</nlog>