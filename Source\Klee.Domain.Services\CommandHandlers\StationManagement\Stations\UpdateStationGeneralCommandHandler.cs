﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.StationManagement.Stations;
using Klee.Domain.Messages.Commands.StationManagement.Stations.Validators;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Klee.Domain.Services.Repositories.StationManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;
using Klee.Domain.Services.CommandHandlers.StationManagement.Stations.Helpers;

namespace Klee.Domain.Services.CommandHandlers.StationManagement.Stations;

public sealed class UpdateStationGeneralCommandHandler
    : RequestHandlerAsync<UpdateStationGeneralCommand> {
    #region PROPERTIES

    private IStationSrpRepository StationSrpRepository { get; }
    private IMemoryCache MemoryCache { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateStationGeneralCommandHandler(IStationSrpRepository stationSrpRepository,
        IMemoryCache memoryCache) {
        StationSrpRepository = stationSrpRepository;
        MemoryCache = memoryCache;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateStationGeneralCommandValidator))]
    public override async Task<UpdateStationGeneralCommand> HandleAsync(UpdateStationGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {
        //Get Station (if it exists) 
        if (await StationSrpRepository.ExistsAsync(_ => _.StationId == command.StationId &&
                                                        _.EntityPartitionKey == command.StationId,
                command)) {
            Station station = await StationSrpRepository.FindAsync(_ => _.StationId == command.StationId &&
                                                                        _.EntityPartitionKey == command.StationId,
                command);

            station.StationName = command.StationName;
            station.RocId = command.RocId;
            station.JoystickTypeId = command.JoystickTypeId;
            station.HourlyRateInEuros = command.HourlyRateInEuros;
            // Update
            await StationSrpRepository.UpdateAsync(station, command);

            // Set Result
            command.Result.EntityId = station.EntityId;

            // Clear Caches
            MemoryCache.RemoveStation(command.StationId);
        }
        else {
            throw new EntityNotFoundException(
                $"Station with Station id '{command.StationId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}