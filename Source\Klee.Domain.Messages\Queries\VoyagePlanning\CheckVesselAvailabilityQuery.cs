using System;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.VoyagePlanning;

public class CheckVesselAvailabilityQuery
    : QueryBase<VesselAvailabilityResult>
{
    #region PROPERTIES
    public string VesselId { get; set; } = "";
    public DateTime VoyageStartDateTime { get; set; }
    public DateTime VoyageEndDateTime { get; set; }
    #endregion

    #region CONSTRUCTORS
    public CheckVesselAvailabilityQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}
