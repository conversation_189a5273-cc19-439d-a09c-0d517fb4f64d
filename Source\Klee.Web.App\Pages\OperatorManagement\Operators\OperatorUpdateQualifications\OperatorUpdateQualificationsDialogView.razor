﻿@using Renoir.Web.Razor.Components.DialogsEditForm

@inherits OperatorUpdateQualificationsDialogViewBase

<RDialogEditForm @ref="@DialogExtended"
                 OnDialogResetting="OnDialogResetting"
                 OnDialogOpening="OnDialogOpening"
                 OnDialogOpened="OnDialogOpened"
                 OnDialogClosed="OnDialogClosed"
                 OnValidSubmit="OnValidSubmit"
                 OnInvalidSubmit="OnInvalidSubmit"
                 EditContext="ViewModelEditContext"
                 Title="Update Qualifications">
    <div class="row">
        <div class="col-md-12">
            <RControlValueEdit Label="Qualifications" IsRequired="false">
                <DxListBox Data="@OperatorUpdateQualificationsDialogViewModel.SelectableOperatorQualificationsWithDisplayNames"
                           SelectionMode="ListBoxSelectionMode.Multiple"
                           ShowCheckboxes="true"
                           TextFieldName="Value"
                           ValueFieldName="Key"
                           style="max-height: 130px; overflow-y: auto;"
                           CssClass="w-auto mt-1 me-1 flex-grow-1"
                           @bind-Values="@this.ViewModel.QualificationsSelected">
                </DxListBox>
            </RControlValueEdit>
        </div>
    </div>
</RDialogEditForm>

@code {
}