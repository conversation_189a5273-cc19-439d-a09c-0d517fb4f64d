﻿using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.UserManagement.Users;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Klee.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.Repositories.Common;

namespace Klee.Infrastructure.Repositories.UserManagement;

public class UserProfileSrpRepository
    : EfDomainEntityRepository<AppSrpDbContext, UserProfile, long>, IUserProfileSrpRepository
{
    public UserProfileSrpRepository(AppSrpDbContext dbContext,
        IDbExceptionConverter dbExceptionConverter,
        ILogger<UserProfileSrpRepository> logger)
        : base(dbContext, dbExceptionConverter, (ILogger)logger)
    {
    }

    protected override void Dispose()
    {
        base.Dispose();
    }
}