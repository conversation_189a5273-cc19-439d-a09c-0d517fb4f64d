﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Monet.Helpers;

namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorUpdateQualifications;

public class OperatorUpdateQualificationsDialogViewModel
    : ViewModelBase<OperatorUpdateQualificationsDialogViewModel>
{
    #region PROPERTIES - STATIC
    // Selectable
    public static IList<KeyValuePair<QualificationTypeIds, string>> SelectableOperatorQualificationsWithDisplayNames { get; }
        = Enums.GetMembers<QualificationTypeIds>()
            .Where(_ => _.Value != QualificationTypeIds.None && _.Value != QualificationTypeIds.Undefined)
            .Select(_ => new KeyValuePair<QualificationTypeIds,string>(
                _.Value,
                _.Value.GetDisplayName()))
            .ToList();
    #endregion

    #region PROPERTIES
    private string OperatorId { get; set; } = "";
    public IEnumerable<QualificationTypeIds> QualificationsSelected { get; set; } = new List<QualificationTypeIds>();
    #endregion

    #region CONSTRUCTORS
    public OperatorUpdateQualificationsDialogViewModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS
    public void Clear()
    {
        this.QualificationsSelected = new List<QualificationTypeIds>();
    }

    public async Task LoadOperatorAsync(string operatorId)
    {
        // Get Operator
        Operator operatorObj = await this.SrpQueryProcessor.ExecuteAsync(new GetOperatorQuery(operatorId, this.SrpQueryContext)) ?? new Operator();

        // Set Operator
        this.OperatorId = operatorObj.OperatorId;
        this.QualificationsSelected = operatorObj.Qualifications;
    }
    #endregion

    #region METHODS
    public async Task UpdateOperatorAsync()
    {
        // Init
        UpdateOperatorQualificationsCommand updateOperatorQualificationsCommand = new UpdateOperatorQualificationsCommand(this.OperatorId,
                                                                                                  this.SrpCommandContext)
                                                                                    {
                                                                                        Qualifications = this.QualificationsSelected.ToList()
                                                                                    };

        // Create Operator
        await this.SrpCommandProcessor
                  .SendAsync(updateOperatorQualificationsCommand, false);
    }
    #endregion
}