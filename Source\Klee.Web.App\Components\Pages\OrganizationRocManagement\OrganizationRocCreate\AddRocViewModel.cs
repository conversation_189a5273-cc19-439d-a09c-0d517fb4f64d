using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.RocManagement.Rocs.Data;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Messages.Commands.OrganizationRocManagement.OrganizationRocs.Validators;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Validations.Helpers;

namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocCreate;

public class AddRocViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES - FORM

    public string RocName { get; set; } = "";

    public string Address { get; set; } = "";

    public string Location { get; set; } = "";

    public string PostalCode { get; set; } = "";

    [Required]
    public CountryIds Country { get; set; } = CountryIds.None;
    #endregion

    #region CONSTRUCTORS
    public AddRocViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public void Clear()
    {
        this.RocName = "";
        this.Address = "";
        this.Location = "";
        this.PostalCode = "";
        this.Country = CountryIds.None;
    }

    public async Task CreateRocAsync()
    {
        try
        {
            // Create the command
            CreateOrganizationRocCommand createCommand = await NewCreateOrganizationRocCommandAsync();

            // Execute the command
            await this._srpProcessors.CommandProcessor.SendAsync(createCommand);
        }
        catch
        {
            throw;
        }
    }

    public async Task<CreateOrganizationRocCommand> NewCreateOrganizationRocCommandAsync()
    {
        ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            
        return new CreateOrganizationRocCommand(commandContext)
        {
            RocName = this.RocName,
            Address = this.Address,
            Location = this.Location,
            PostalCode = this.PostalCode,
            Country = this.Country,
        };
    }

    #endregion

    #region METHODS - VALIDATE
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        CreateOrganizationRocCommandValidator createOrganizationRocCommandValidator = new CreateOrganizationRocCommandValidator();
        (await createOrganizationRocCommandValidator.ValidateAsync(await this.NewCreateOrganizationRocCommandAsync()))
            .AddTo(validationResults);

        return validationResults;
    }
    #endregion
}
