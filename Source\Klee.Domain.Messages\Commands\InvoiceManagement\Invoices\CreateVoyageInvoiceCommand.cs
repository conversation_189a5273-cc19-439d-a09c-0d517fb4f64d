using System;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;

public class CreateVoyageInvoiceCommand : CommandBase
{
    #region RESULT CLASS
    public class CommandResult
    {
        public Guid VoyageInvoiceId { get; set; }
    }
    #endregion

    #region PROPERTIES
    public string BookingOrganizationId { get; }
    public string OperatorOrganizationId { get; set; } = "";
    public Guid VoyageId { get; set; } = Guid.Empty;
    public double TotalAmountInEuros { get; set; } = 0.0;
    
    // Result
    public CommandResult Result { get; } = new CommandResult();
    #endregion

    #region CONSTRUCTORS
    public CreateVoyageInvoiceCommand(string bookingOrganizationId, ICommandContext commandContext)
        : base(commandContext)
    {
        this.BookingOrganizationId = bookingOrganizationId;
    }
    #endregion
}
