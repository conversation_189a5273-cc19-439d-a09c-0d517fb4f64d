@page "/my-assets/vessels/edit/{VehicleIdEncoded}"

@layout OrganizationViewLayout
@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Entities.VehicleManagement.Vehicles.Data
@using Microsoft.IdentityModel.Tokens

@inherits EditVesselViewBase

<div class="container py-3">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-teal-700">Edit Vessel</h1>
        <Button Type="@ButtonType.Default"
                Class="@TailwindStyleStrings.Button.Outline"
                OnClick="NavigateToVesselList">
            Cancel
        </Button>
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="p-6">
            @if (IsLoading)
            {
                <div class="flex justify-center items-center py-12">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin text-2xl text-teal-700 mb-4"></i>
                        <p class="text-gray-600">Loading vessel data...</p>
                    </div>
                </div>
            }
            else if (ViewModel != null)
            {
                <EditForm Model="@ViewModel" OnValidSubmit="HandleSubmit" OnInvalidSubmit="HandleSubmit">
                    <DataAnnotationsValidator />
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Vessel Identification Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-ship mr-2"></i>Vessel Identification
                                </h3>

                                <div class="space-y-6">
                                    <FormField Label="Vessel Name" Id="vehicleName">
                                        <Input @bind-Value="ViewModel.VehicleName"
                                               Placeholder="Enter vessel name"
                                               Class="@TailwindStyleStrings.Form.Input"
                                               Id="vehicleName" />
                                        <ValidationMessage For="@(() => ViewModel.VehicleName)"/>
                                    </FormField>

                                    <FormField Label="ENI" Id="eni">
                                        <Input @bind-Value="ViewModel.ENI"
                                               Placeholder="Enter ENI number (optional)"
                                               Class="@TailwindStyleStrings.Form.Input"
                                               Id="eni" />
                                        <ValidationMessage For="@(() => ViewModel.ENI)"/>
                                    </FormField>
                                </div>
                            </div>

                            <!-- Vessel Technical Information Column -->
                            <div class="space-y-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                                <h3 class="text-lg font-semibold text-teal-700 border-b border-teal-200 pb-3 mb-4">
                                    <i class="fas fa-cogs mr-2"></i>Vessel Technical Information
                                </h3>

                                <div class="space-y-6">
                                    <FormField Label="Length (m)" Id="length">
                                        <AntDesign.InputNumber TValue="double"
                                                               @bind-Value="@ViewModel.Length"
                                                               Placeholder="0.00"
                                                               Step="0.1"
                                                               Min="0"
                                                               Precision="2"
                                                               Class="@TailwindStyleStrings.Form.InputNumber"
                                                               Id="length" />
                                        <ValidationMessage For="@(() => ViewModel.Length)"/>
                                    </FormField>

                                    <FormField Label="Beam (m)" Id="beam">
                                        <AntDesign.InputNumber TValue="double"
                                                               @bind-Value="@ViewModel.Beam"
                                                               Placeholder="0.00"
                                                               Step="0.1"
                                                               Min="0"
                                                               Precision="2"
                                                               Class="@TailwindStyleStrings.Form.InputNumber"
                                                               Id="beam" />
                                        <ValidationMessage For="@(() => ViewModel.Beam)"/>
                                    </FormField>

                                    <FormField Label="Vessel Type" Id="vesselType" IsRequired="true">
                                        <EnumSelect TEnum="VesselTypeIds"
                                                    @bind-Value="@ViewModel.VesselType"
                                                    Placeholder="Select vessel type"
                                                    Class="@TailwindStyleStrings.Form.Select"
                                                    AllowClear="false"
                                                    Id="vesselType">
                                        </EnumSelect>
                                        <ValidationMessage For="@(() => ViewModel.VesselType)"/>
                                    </FormField>

                                </div>
                            </div>
                        </div>

                        @if (ValidationErrors.Any())
                        {
                            <div class="rounded-md bg-red-50 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-circle h-5 w-5 text-red-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <ul class="list-disc pl-5 space-y-1">
                                                @foreach (var error in ValidationErrors)
                                                {
                                                    <li>@error</li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="flex justify-end gap-4 pt-4">
                            <Button Type="@ButtonType.Default"
                                    Class="@TailwindStyleStrings.Button.Outline"
                                    OnClick="NavigateToVesselList">
                                Cancel
                            </Button>
                            <Button Type="@ButtonType.Primary"
                                    Class="@TailwindStyleStrings.Button.Primary"
                                    HtmlType="submit"
                                    Loading="@this.IsSubmitting">
                                <i class="fas fa-save h-4 w-4 mr-2"></i>
                                Save Changes
                            </Button>
                        </div>
                    </div>
                </EditForm>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-500 mb-4"></i>
                    <p class="text-red-600">Failed to load vessel data. Please try again.</p>
                    <Button Type="@ButtonType.Primary"
                            Class="@($"{TailwindStyleStrings.Button.Primary} mt-4")"
                            OnClick="NavigateToVesselList">
                        Back to Vessels
                    </Button>
                </div>
            }
        </div>
    </Card>
</div>

@code {
    
    #region METHODS - STATIC
    public static string GetUri(string vehicleId)
    {
        return $"/my-assets/vessels/edit/{Base64UrlEncoder.Encode(vehicleId)}";
    }
    #endregion
}
