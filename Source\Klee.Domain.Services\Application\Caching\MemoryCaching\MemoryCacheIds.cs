﻿namespace Klee.Domain.Services.Application.Caching.MemoryCaching
{
    public static class MemoryCacheIds
    {
        #region CONSTANTS

        public const string GetVehicleListCacheId = nameof(GetVehicleListCacheId);
        public const string GetOrganizationListCacheId = nameof(GetOrganizationListCacheId);
        public const string GetOrganizationNameListCacheId = nameof(GetOrganizationNameListCacheId);
        public const string GetRocListCacheId = nameof(GetRocListCacheId);
        public const string GetStationListCacheId = nameof(GetStationListCacheId);
        public const string GetUserProfileListCacheId = nameof(GetUserProfileListCacheId);
        public const string GetOperatorListCacheId = nameof(GetOperatorListCacheId);
        public const string GetJobOfferListCacheId = nameof(GetJobOfferListCacheId);
        public const string GetJobProposalListCacheId = nameof(GetJobProposalListCacheId);
        public const string GetAcceptedJobListCacheId = nameof(GetAcceptedJobListCacheId);

        #endregion

        #region METHODS - STATIC
        public static string GetVehicleCacheId(string vehicleId)
        {
            return nameof(GetVehicleCacheId) + "-" + vehicleId;
        }

        public static string GetOrganizationCacheId(string organizationId)
        {
            return nameof(GetOrganizationCacheId) + "-" + organizationId;
        }

        
        public static string GetRocCacheId(string rocId)
        {
            return nameof(GetRocCacheId) + "-" + rocId;
        }

        public static string GetStationCacheId(string stationId)
        {
            return nameof(GetStationCacheId) + "-" + stationId;
        }

        public static string GetUserProfileCacheId(string userId)
        {
            return nameof(GetUserProfileCacheId) + "-" + userId;
        }

        public static string GetOperatorCacheId(string operatorId)
        {
            return nameof(GetOperatorCacheId) + "-" + operatorId;
        }

        public static string GetJobOfferCacheId(string jobOfferId)
        {
            return nameof(GetJobOfferCacheId) + "-" + jobOfferId;
        }

        public static string GetJobProposalCacheId(string jobProposalId)
        {
            return nameof(GetJobProposalCacheId) + "-" + jobProposalId;
        }

        public static string GetAcceptedJobCacheId(string acceptedJobId)
        {
            return nameof(GetAcceptedJobCacheId) + "-" + acceptedJobId;
        }

        #endregion
    }
}
