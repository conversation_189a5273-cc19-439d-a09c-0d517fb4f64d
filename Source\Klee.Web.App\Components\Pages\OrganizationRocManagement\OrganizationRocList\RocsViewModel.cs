using System.Collections.Generic;
using System.Threading.Tasks;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs;
using Klee.Domain.Messages.Queries.OrganizationRocManagement.OrganizationRocs.Data;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.OrganizationRocManagement.OrganizationRocList;

public class RocsViewModel
{
    #region FIELDS
    private ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    public IReadOnlyList<OrganizationRocListItem> Rocs { get; private set; } = new List<OrganizationRocListItem>();
    #endregion

    #region CONSTRUCTORS
    public RocsViewModel(ISrpProcessors srpProcessors)
    {
        this._srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task<bool> LoadOrganizationRocsAsync(bool forceLoad = false)
    {
        // Load Organization Rocs
        if (forceLoad)
        {
            IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
            this.Rocs = (await this._srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationRocListQuery(queryContext))).ToList();
            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task DeleteOrganizationRocAsync(string rocId)
    {
        try
        {
            ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            // Delete Organization ROC
            await this._srpProcessors.CommandProcessor.SendAsync(new DeleteRocCommand(rocId, commandContext));

            // Load Organization ROCS
            await this.LoadOrganizationRocsAsync(forceLoad: true);
        }
        catch
        {
            throw;
        }
    }
    #endregion

}
