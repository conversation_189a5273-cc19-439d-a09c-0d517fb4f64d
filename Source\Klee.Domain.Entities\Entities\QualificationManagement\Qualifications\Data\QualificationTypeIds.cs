﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Klee.Domain.Entities.QualificationManagement.Qualifications.Data;

public enum QualificationTypeIds {
    Undefined = -1, 
    None = 0,
    
    //Navigation
    [Display(Name = "Radar Patent")]
    RadarPatent = 1,
    [Display(Name = "Navigation License")]
    NavigationLicense = 2,
    [Display(Name = "Rhine Patent")]
    RhinePatent = 3,

    //Cargo
    [Display(Name = "ADN Basic Certificate")]
    AdnBasicCert = 4,
    [Display(Name = "ADN Chemistry Certificate")]
    AdnChemistryCert = 5,
    [Display(Name = "ADN Gas Certificate")]
    AdnGasCert = 6,

    //Communication
    [Display(Name = "Marine VHF Radio Certificate")]
    MarineVhfRadioCert = 7,
}