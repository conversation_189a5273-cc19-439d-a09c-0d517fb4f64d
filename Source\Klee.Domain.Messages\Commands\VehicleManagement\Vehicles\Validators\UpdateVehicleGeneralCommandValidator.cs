﻿using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using FluentValidation;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Messages.Commands.VehicleManagement.Vehicles.Validators
{
    public class UpdateVehicleGeneralCommandValidator : AbstractValidator<UpdateVehicleGeneralCommand>
    {
        public UpdateVehicleGeneralCommandValidator() { 
            this.RuleFor(_ => _.VehicleId).NotNull();
            this.RuleFor(_ => _.VehicleId).NotEmpty();
            this.RuleFor(_ => _.OrganizationId).NotNull();
            this.RuleFor(_ => _.OrganizationId).NotEmpty();
        }

    }
}