﻿using FluentValidation;
using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;

namespace Klee.Domain.Messages.Commands.StationManagement.Stations.Validators;

public class UpdateStationGeneralCommandValidator : AbstractValidator<UpdateStationGeneralCommand>
{
    public UpdateStationGeneralCommandValidator() { 
        this.RuleFor(_ => _.StationId).NotNull();
        this.RuleFor(_ => _.StationId).NotEmpty();
        this.RuleFor(_ => _.StationId).Length(7, 7).WithMessage("Station Id must be 7 characters.");
        this.RuleFor(_ => _.StationId).Must(this.StationIdMustStartWithCharacters).WithMessage("Station Id must start with characters: 'V', 'Dev', 'Dmo' or 'Tst'");
        this.RuleFor(_ => _.StationId).Must(this.StationIdMustEndWithNumbers).WithMessage("Station Id  must end with numbers.");
        this.RuleFor(_ => _.JoystickTypeId).Must(this.JoystickTypeIdMustBeValid).WithMessage("Joystick type is not valid");
        this.RuleFor(_ => _.RocId).NotNull();
        this.RuleFor(_ => _.RocId).NotEmpty();
    }

    private bool JoystickTypeIdMustBeValid(JoystickTypeIds joystickTypeId)
    {
        return joystickTypeId != JoystickTypeIds.Undefined && joystickTypeId != JoystickTypeIds.None;
    }

    private bool StationIdMustStartWithCharacters(string stationId)
    {
        return stationId.StartsWith("S") ||
               stationId.StartsWith("Sta") ||
               stationId.StartsWith("Dmo") ||
               stationId.StartsWith("Tst");
    }

    private bool StationIdMustEndWithNumbers(string stationId)
    {
        string stationIdEnd =
            stationId.Replace("Sta", "")
                .Replace("S", "")
                .Replace("Dmo", "")
                .Replace("Tst", "");

        return int.TryParse(stationIdEnd, out int result);
    }
}