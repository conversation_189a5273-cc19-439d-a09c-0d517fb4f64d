﻿using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList;
using Renoir.Srp.Portal.Web.Pages;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Pages
{
    public class IndexViewBase : LayoutBodyViewBase<IndexViewModel>
    {
        #region PROPERTIES
        #endregion

        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new IndexViewModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnClick_ViewVehicles()
        {
            // Init
            string userId = this.AuthenticatedUserId ?? "";

            try
            {
                // View UserVehicles
                this.NavigationManager.NavigateTo(VehicleListView.GetUri());
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to weather.", userId);
                #endregion

                //throw;
            }

            return Task.CompletedTask;
        }
        #endregion
    }
}