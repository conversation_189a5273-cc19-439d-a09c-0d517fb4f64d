
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.InvoiceManagement.Invoices;

public class InvoicesViewModel
{
    #region SERVICES
    private ISrpProcessors SrpProcessors { get; }
    private IUserAuthenticationService UserAuthenticationService { get; }
    #endregion

    #region PROPERTIES
    public List<InvoiceListItem> Invoices { get; private set; } = new();
    public InvoiceStatistics Statistics { get; private set; } = new();
    public bool IsLoading { get; private set; } = true;
    public bool IsUpdatingStatus { get; private set; } = false;
    public string CurrentUserOrganizationId { get; private set; } = "";
    #endregion

    #region CONSTRUCTORS
    public InvoicesViewModel(ISrpProcessors srpProcessors, IUserAuthenticationService userAuthenticationService)
    {
        SrpProcessors = srpProcessors;
        UserAuthenticationService = userAuthenticationService;
    }
    #endregion

    #region METHODS - INITIALIZATION
    public async Task InitializeAsync()
    {
        try
        {
            IsLoading = true;

            // Get current user's organization
            CurrentUserOrganizationId = await UserAuthenticationService.GetCurrentUserOrganizationIdAsync();

            if (!string.IsNullOrEmpty(CurrentUserOrganizationId))
            {
                // Load invoices and statistics
                await LoadInvoicesAsync();
                await LoadStatisticsAsync();
            }

        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadInvoicesAsync()
    {
        IQueryContext context = await SrpProcessors.GetQueryContextAsync();
        GetOrganizationInvoicesQuery query = new GetOrganizationInvoicesQuery(CurrentUserOrganizationId, context);
        Invoices = (await SrpProcessors.QueryProcessor.ExecuteAsync(query)).ToList();
    }

    private async Task LoadStatisticsAsync()
    {
        IQueryContext context = await SrpProcessors.GetQueryContextAsync();
        GetOrganizationInvoiceStatisticsQuery query = new GetOrganizationInvoiceStatisticsQuery(CurrentUserOrganizationId,context);
        Statistics = await SrpProcessors.QueryProcessor.ExecuteAsync(query);
    }
    #endregion

    #region METHODS - ACTIONS
    public async Task MarkInvoiceAsPaidAsync(Guid invoiceId)
    {
        try
        {
            IsUpdatingStatus = true;
            ICommandContext context = await SrpProcessors.GetCommandContextAsync();
            
            UpdateVoyageInvoiceStatusCommand command = new UpdateVoyageInvoiceStatusCommand(invoiceId, context)
            {
                Status = VoyageInvoiceStatus.Paid,
                PaymentDate = DateTime.UtcNow
            };

            await SrpProcessors.CommandProcessor.SendAsync(command);

            // Reload invoices to reflect the change
            await LoadInvoicesAsync();
            await LoadStatisticsAsync();
        }
        finally
        {
            IsUpdatingStatus = false;
        }
    }

    public string GetInvoiceDirection(InvoiceListItem invoice)
    {
        return invoice.GetInvoiceDirection(CurrentUserOrganizationId);
    }

    public string GetDirectionBadgeClass(InvoiceListItem invoice)
    {
        return invoice.IsIncoming(CurrentUserOrganizationId) 
            ? "bg-green-100 text-green-800 border-green-200" 
            : "bg-blue-100 text-blue-800 border-blue-200";
    }

    public string GetStatusBadgeClass(VoyageInvoiceStatus status)
    {
        return status switch
        {
            VoyageInvoiceStatus.Paid => "bg-green-100 text-green-800 border-green-200",
            VoyageInvoiceStatus.Open => "bg-amber-100 text-amber-800 border-amber-200",
            _ => "bg-gray-100 text-gray-800 border-gray-200"
        };
    }

    public string FormatDuration(TimeSpan duration)
    {
        if (duration.TotalDays >= 1)
        {
            return $"{(int)duration.TotalDays}d {duration.Hours}h {duration.Minutes}m";
        }
        return $"{duration.Hours}h {duration.Minutes}m";
    }

    public string FormatCurrency(double amount)
    {
        return $"€{amount:N2}";
    }
    #endregion
}
