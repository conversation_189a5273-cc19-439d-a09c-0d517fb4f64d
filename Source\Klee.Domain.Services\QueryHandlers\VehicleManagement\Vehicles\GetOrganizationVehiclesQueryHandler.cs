﻿using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Queries.VehicleManagement.Vehicles;
using Klee.Domain.Services.Repositories.VehicleManagement;
using Paramore.Darker;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.EntityFrameworkCore;
using Monet.Helpers;

namespace Klee.Domain.Services.QueryHandlers.VehicleManagement.Vehicles;

public class GetOrganizationVehiclesQueryHandler : QueryHandlerAsync<GetOrganizationVehiclesQuery, IReadOnlyList<VehicleListItem>> {
    #region PROPERTIES

    private IVehicleSrpRepository VehicleSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public GetOrganizationVehiclesQueryHandler(IVehicleSrpRepository vehicleSrpRepository) {
        this.VehicleSrpRepository = vehicleSrpRepository;
    }

    #endregion

    #region METHODS

    public override async Task<IReadOnlyList<VehicleListItem>> ExecuteAsync(GetOrganizationVehiclesQuery query,
        CancellationToken cancellationToken = new CancellationToken()) {
        string organizationId = query.OrganizationId;

        // Init
        List<VehicleListItem> vehicleListItems =
            // Get VehicleListItems from DB
            await this.VehicleSrpRepository.Entities(query)
                .Where(_ => _.OrganizationId == organizationId)
                .OrderBy(_ => _.VehicleId)
                .Select(_ => new VehicleListItem() {
                    EntityId = _.EntityId,
                    VehicleId = _.VehicleId,
                    VehicleTypeDisplayName = _.VehicleTypeId.GetDisplayName(),
                    VehicleName = _.VehicleName,
                    HourlyRateInEuros = _.HourlyRateInEuros,
                    ENI = _.ENI,
                    OrganizationId = _.OrganizationId,
                    IsActive = _.IsActive ?? false
                })
                .ToListAsync(cancellationToken: cancellationToken);

        // Filter "IsActive" (if needed)
        if (query.IsActive != null) {
            vehicleListItems = vehicleListItems.Where(_ => _.IsActive == query.IsActive).ToList();
        }

        return vehicleListItems;
    }
    #endregion
}