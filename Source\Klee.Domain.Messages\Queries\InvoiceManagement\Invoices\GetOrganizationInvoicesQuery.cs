using System.Collections.Generic;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.InvoiceManagement.Invoices;

public class GetOrganizationInvoicesQuery
    : QueryBase<IReadOnlyList<InvoiceListItem>>
{
    #region PROPERTIES
    public string OrganizationId { get; }
    
    /// <summary>
    /// == true     => only active,
    /// == false    => only inactive,
    /// == null     => no specific filtering
    /// </summary>
    public bool? IsActive { get; set; } = null;
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationInvoicesQuery(string organizationId, IQueryContext context)
        : base(context)
    {
        this.OrganizationId = organizationId;
    }
    #endregion
}
