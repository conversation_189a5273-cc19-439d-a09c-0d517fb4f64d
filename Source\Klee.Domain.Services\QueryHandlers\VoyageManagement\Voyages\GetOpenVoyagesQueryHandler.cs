using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.VehicleManagement.Vehicles.Data;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages.Data;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Klee.Domain.Services.UserContextService;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VoyageManagement.Voyages;

public class GetOpenVoyagesQueryHandler : QueryHandlerAsync<GetOpenVoyagesQuery, IReadOnlyList<OpenVoyageListItem>>
{
    #region PROPERTIES
    private IVoyageSrpRepository VoyageSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOpenVoyagesQueryHandler(IVoyageSrpRepository voyageSrpRepository,
                                      IUserContextHelperService userContextHelperService)
    {
        VoyageSrpRepository = voyageSrpRepository;
        UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<IReadOnlyList<OpenVoyageListItem>> ExecuteAsync(GetOpenVoyagesQuery query,
                                                                               CancellationToken cancellationToken = new CancellationToken())
    {
        // Get current user's organization
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        // Get open voyages (voyages without operators) with all related data
        List<OpenVoyageListItem> openVoyageListItems = await VoyageSrpRepository.Entities(query)
            .Include(v => v.Vehicle)
                .ThenInclude(ve => ve.Organization)
            .Include(v => v.BookingOrganization)
            .Where(v => 
                // Only active voyages if specified
                (query.IsActive == null || v.IsActive == query.IsActive) &&
                // Only voyage start date in the future
                (v.StartDateTime >= DateTime.Today) && 
                // Only voyages that need operators (no operator assigned)
                (v.OperatorId == null || v.OperatorId == "") &&
                // Only voyages from current organization (outgoing voyages)
                v.BookingOrganizationId == organizationId)
            .OrderByDescending(v => v.StartDateTime)
            .Select(v => new OpenVoyageListItem
            {
                VoyageId = v.VoyageId,
                StartDateTime = v.StartDateTime.ToLocalTime(),
                EndDateTime = v.EndDateTime.ToLocalTime(),
                Description = v.Description,
                RequiredQualifications = v.RequiredQualifications,
                
                // Vehicle information
                VehicleId = v.VehicleId,
                VehicleName = v.Vehicle.VehicleName,
                VehicleENI = v.Vehicle.ENI,
                VehicleType = v.Vehicle.VehicleTypeId,
                VehicleTypeDisplayName = v.Vehicle.VehicleTypeId.ToString(),
                VehicleLength = v.Vehicle.Length,
                VehicleBeam = v.Vehicle.Beam,
                VehicleHourlyRateInEuros = v.Vehicle.HourlyRateInEuros,
                VehicleOrganizationId = v.Vehicle.OrganizationId,
                VehicleOrganizationName = v.Vehicle.Organization.Name,
                
                // Booking organization information
                BookingOrganizationId = v.BookingOrganizationId,
                BookingOrganizationName = v.BookingOrganization.Name
            })
            .ToListAsync(cancellationToken);

        return openVoyageListItems;
    }
    #endregion
}
