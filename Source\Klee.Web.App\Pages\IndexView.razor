﻿@page "/"
@page "/Index"
@inherits IndexViewBase

<RMainLayoutBody PageHeaderTitle="Dashboard"
                 CanShowButtonClose=false
                 ParentView="this">
    <BreadcrumbsListItems>

    </BreadcrumbsListItems>
    <Content>
        <div class="row center">
            <div class="col-md-12 mb-4">
                <h1 class="p-4 font-bold text-blue-600">Hello, world!</h1>
                <h2 class="text-dark mb-1 font-weight-light mt-5 pt-2">Welcome to your <strong>Seafar Portal</strong></h2>
                <p class="lead">for <span class="alternative-font text-5">automating inland Shipping</span></p>

                <RA Class="btn btn-3d btn-xl mt-4 mb-5"
                    Style="background-color: #383f48; border-color: #383f48 #383f48 #22262b; color: #fff;"
                    OnClick="OnClick_ViewVehicles">
                    VIEW VEHICLES <i class="ms-4 fas fa-long-arrow-alt-right"></i>
                </RA>
            </div>
        </div>
    </Content>
</RMainLayoutBody>

@code {

    protected override Task OnAfterRenderAsync(bool firstRender)
    {
        return base.OnAfterRenderAsync(firstRender);
    }
}