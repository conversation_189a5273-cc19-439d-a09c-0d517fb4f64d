﻿using EnumsNET;
using Klee.Domain.Messages.Commands.StationManagement.Stations.Validators;
using Klee.Domain.Messages.Commands.StationManagement.Stations;
using Renoir.SoftwareEnvironments;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Monet.Helpers;
using Renoir.Application.Validations.Helpers;
using Klee.Domain.Entities.Entities.StationManagement.Stations.Data;

namespace Klee.Web.App.Pages.StationManagement.Stations.StationCreate;

public class StationCreateDialogViewModel
        : ViewModelBase<StationCreateDialogViewModel>, IValidatableObject
    {
        #region FIELDS
        private string _softwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
        private SoftwareEnvironmentIds _softwareEnvironmentId = SoftwareEnvironmentIds.Prod;

        private string _stationJoystickTypeDisplayName = JoystickTypeIds.Default.GetDisplayName();
        private JoystickTypeIds _stationJoystickTypeId = JoystickTypeIds.Default;

        #endregion

        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableSoftwareEnvironmentDisplayNames { get; } = Enums.GetMembers<SoftwareEnvironmentIds>()
                                                                                              .Where(_ => _.Value != SoftwareEnvironmentIds.None)
                                                                                              .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                              .OrderBy(_ => _).ToList();
        public static IList<string> SelectableJoystickTypeDisplayNames { get; } = Enums.GetMembers<JoystickTypeIds>()
                                                                                      .Where(_ => _.Value != JoystickTypeIds.None)
                                                                                      .Where(_ => _.Value != JoystickTypeIds.Undefined)
                                                                                      .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                      .OrderBy(_ => _).ToList();
        #endregion

        #region PROPERTIES
        [Required]
        [Display(Name = "Software Environment")]
        public string SoftwareEnvironmentDisplayName
        {
            get => this._softwareEnvironmentDisplayName;
            set => this.SetSoftwareEnvironmentDisplayName(value);
        }

        [Required]
        [Display(Name = "Station joystick type")]
        public string StationJoystickTypeDisplayName
        {
            get => this._stationJoystickTypeDisplayName;
            set => this.SetStationJoystickTypeDisplayName(value);
        }

        [Required]
        [Display(Name = "Station Id")]
        public string StationId { get; set; } = "";

        [Required]
        [Display(Name = "Station Name")]
        public string StationName { get; set; } = "";

        [Display(Name = "Hourly rate (Euro)")]
        public double HourlyRateInEuros { get; set; } = 0.0;

        [Required]
        [Display(Name = "ROC ID")]
        public string RocId { get; set; } = "";
        public string OrganizationId { get; set; } = "";
        #endregion

        #region PROPERTIES - CONVERTED
        public SoftwareEnvironmentIds SoftwareEnvironmentId => this._softwareEnvironmentId;
        public JoystickTypeIds StationJoystickTypeId => this._stationJoystickTypeId;
        #endregion

        #region CONSTRUCTORS
        public StationCreateDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public void Clear()
        {
            this.SoftwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
            this.StationJoystickTypeDisplayName = JoystickTypeIds.Default.GetDisplayName();
            this.StationId = "";
            this.StationName = "";
            this.RocId = "";
        }

        private void SetSoftwareEnvironmentDisplayName(string softwareEnvironmentDisplayName)
        {
            // Init
            bool isSoftwareEnvironmentChanged = softwareEnvironmentDisplayName != this.SoftwareEnvironmentDisplayName;

            //
            if (Enums.TryParse(softwareEnvironmentDisplayName, true, out SoftwareEnvironmentIds enumValue, EnumFormat.DisplayName))
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = enumValue;
            }
            else
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = SoftwareEnvironmentIds.None;
            }

            // Handle changed software environment type
            if (isSoftwareEnvironmentChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }

        private void SetStationJoystickTypeDisplayName(string stationJoystickTypeDisplayName)
        {
            // Init
            bool isStationTypeChanged = stationJoystickTypeDisplayName != this.StationJoystickTypeDisplayName;

            //
            if (Enums.TryParse(stationJoystickTypeDisplayName, true, out JoystickTypeIds enumValue, EnumFormat.DisplayName))
            {
                this._stationJoystickTypeDisplayName = stationJoystickTypeDisplayName;
                this._stationJoystickTypeId = enumValue;
            }
            else
            {
                this._stationJoystickTypeDisplayName = stationJoystickTypeDisplayName;
                this._stationJoystickTypeId = JoystickTypeIds.None;
            }

            // Handle changed Station type
            if (isStationTypeChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }
        #endregion

        #region METHODS - VALIDATE
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateStationCommandValidator createStationCommandValidator = new CreateStationCommandValidator();
            createStationCommandValidator.Validate(this.NewCreateStationCommand())
                                         .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        private CreateStationCommand NewCreateStationCommand()
        {
            return new CreateStationCommand(this.SoftwareEnvironmentId,
                                            this.StationId,
                                            this.RocId,
                                            this.StationJoystickTypeId,
                                            this.SrpCommandContext)
                   {
                       StationName = this.StationName,
                       HourlyRateInEuros = this.HourlyRateInEuros
                   };
        }

        public async Task CreateStationAsync()
        {
            // Init
            CreateStationCommand createStationCommand = this.NewCreateStationCommand();

            // Create UserProfile
            await this.SrpCommandProcessor
                      .SendAsync(createStationCommand, false);
        }
        #endregion
    }