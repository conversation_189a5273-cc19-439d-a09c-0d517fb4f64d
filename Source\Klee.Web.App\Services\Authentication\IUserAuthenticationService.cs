using System.Security.Claims;
using Klee.Domain.Entities.UserManagement.Users;

namespace Klee.Web.App.Services.Authentication;

public interface IUserAuthenticationService
{
    /// <summary>
    /// Gets the current authenticated user using multiple fallback approaches for maximum reliability
    /// </summary>
    /// <returns>ClaimsPrincipal of the current authenticated user</returns>
    Task<ClaimsPrincipal> GetCurrentUserAsync();

    /// <summary>
    /// Gets the current user's profile from the database
    /// </summary>
    /// <returns>UserProfile of the current user, or null if not found</returns>
    Task<UserProfile?> GetCurrentUserProfileAsync();

    /// <summary>
    /// Gets the current user's organization ID
    /// </summary>
    /// <returns>Organization ID of the current user, or empty string if not found</returns>
    Task<string> GetCurrentUserOrganizationIdAsync();

    /// <summary>
    /// Checks if the current user is an organization admin
    /// </summary>
    /// <returns>True if the current user is an organization admin, false otherwise</returns>
    Task<bool> IsCurrentUserOrganizationAdminAsync();

    /// <summary>
    /// Checks if the current user is an admin for a specific organization
    /// </summary>
    /// <param name="orgId">Organization ID to check</param>
    /// <returns>True if the current user is an admin for the specified organization, false otherwise</returns>
    Task<bool> IsCurrentUserOrganizationAdminForOrganizationAsync(string orgId);
}
