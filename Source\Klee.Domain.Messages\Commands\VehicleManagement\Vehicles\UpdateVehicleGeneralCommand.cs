﻿using System;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.VehicleManagement.Vehicles
{
    public class UpdateVehicleGeneralCommand : CommandBase
    {
        #region RESULT CLASS
        public class CommandResult
        {
            public long EntityId { get; set; }
        }
        #endregion

        #region PROPERTIES
        public string VehicleId { get; }

        public string VehicleName { get; set; } = "";
        public string ENI { get; set; } = "";
        public double HourlyRateInEuros { get; set; } = 0.0;
        public string OrganizationId { get; }
        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public UpdateVehicleGeneralCommand(string vehicleId,
                                           string organizationId,
                                           ICommandContext commandContext)
            : base(commandContext)
        {
            this.VehicleId = vehicleId;
            this.OrganizationId = organizationId;
        }
        #endregion
    }
}
