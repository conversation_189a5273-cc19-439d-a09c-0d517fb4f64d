﻿using Klee.Domain.Services;
using Klee.Domain.Services.CommandHandlers.VehicleManagement.Vehicles;
using Klee.Domain.Services.QueryHandlers.VehicleManagement.Vehicles;
using Klee.Infrastructure.Application.Messages;
using Klee.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Paramore.Brighter.Extensions.DependencyInjection;
using Paramore.Darker.AspNetCore;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.EF.Data.ExceptionsConverters;
using Renoir.Application.EF.Data.ExceptionsConverters.Cosmos;
using Renoir.Application.Messages.General;
using Renoir.Application.Security;
using Renoir.Application.SoftwareEnvironments;
using Renoir.Application.UserSessionStores;

namespace Klee.Infrastructure.Helpers
{
    public static class IServiceCollectionExtensionsForSrp
    {
        public static void AddSeafarServicesOfSrp(this IServiceCollection services,
                                                  IConfiguration configuration)
        {
            // Register Cosmos database
            {
                services
                    .AddDbContext<AppSrpDbContext>(options =>
                    {
                        //options.UseSqlite($"DataSource = Klee.Web.App.db")
                        //    .EnableSensitiveDataLogging()
                        //    .EnableDetailedErrors();
                        options.UseNpgsql(
                            "Host=localhost;Port=5432;Database=KleeDb;Username=klee_admin;Password=test123;");

                        //options.UseInMemoryDatabase(dbName));
                    }, ServiceLifetime.Scoped);
                //services.AddEntityFrameworkCosmos()
                //        .AddDbContext<AppSrpDbContext>(options =>
                //        {
                //            // Init
                //            var seafarCosmosDbAccountEndpoint = configuration[ConfigurationKeys2.Seafar_CosmosDb_AccountEndpoint];
                //            var seafarCosmosDbAccountKey = configuration[ConfigurationKeys2.Seafar_CosmosDb_AccountKey];
                //            var seafarCosmosDbDatabaseId = configuration[ConfigurationKeys2.Seafar_CosmosDb_DatabaseId];

                //            // REMARK: Use NoTracking
                //            //          - queries are fast (although maybe neglectable)
                //            //          - owned entities that didn't exist before and need to be added are automatically attached
                //            //            See also: https://stackoverflow.com/questions/********/nested-owned-type-not-saved-when-updating-in-the-database
                //            options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
                //            //
                //            options.UseCosmos(seafarCosmosDbAccountEndpoint,
                //                              seafarCosmosDbAccountKey,
                //                              seafarCosmosDbDatabaseId,
                //                              _ => { _.ExecutionStrategy(d => new CosmosExecutionStrategy(d)); });
                //            //options.UseLoggerFactory();
                //        }, ServiceLifetime.Transient);
                services.AddTransient<IAppSrpDbContext>(provider =>
                {
                    return provider.GetService<AppSrpDbContext>();
                });
                services.AddSingleton<IDbExceptionConverter, CosmosExceptionConverter>();
            }

            // Register Caching
            {
                services.AddMemoryCache(options =>
                {
                    options.SizeLimit = 1024;
                    options.ExpirationScanFrequency = TimeSpan.FromMinutes(1);
                });
            }

            // Register HttpClient
            {
                services.AddSingleton<HttpClient>(new HttpClient());
            }

            // Register Store (InMemory/Azure/...)
            {
                // UserSession (In Memory)
                services.AddScoped<IUserSessionStore, UserSessionInMemoryStore>();
            }

            // Register Brighter
            {
                services.AddBrighter(options =>
                        {
                            options.CommandProcessorLifetime = ServiceLifetime.Transient;
                            options.HandlerLifetime = ServiceLifetime.Transient;
                        })
                        .AsyncHandlersFromAssemblies(typeof(CreateVehicleCommandHandler).Assembly);
            }

            // Register Darker
            {
                services.AddDarker(options =>
                        {
                            options.QueryProcessorLifetime = ServiceLifetime.Transient;
                            options.HandlerLifetime = ServiceLifetime.Transient;
                        })
                        .AddHandlersFromAssemblies(typeof(GetVehicleListQueryHandler).Assembly);
            }

            // Register Software Environment
            {
                SoftwareEnvironmentInfo softwareEnvironmentInfo = new SoftwareEnvironmentInfo(configuration);
                services.AddSingleton<ISoftwareEnvironmentInfo>(softwareEnvironmentInfo);
                services.AddSingleton<ISoftwareEnvironmentInfoExtended>(softwareEnvironmentInfo);
            }

            // Register Azure Storage
            {
                //services.Configure<AzureBlobStorageOptions>(options =>
                //{
                //    options.ConnectionString = configuration[ConfigurationKeys2.Seafar_AzureStorage_ConnectionString];
                //});
                //services.AddScoped<IAzureBlobStorageFileUploader, AzureBlobStorageFileUploader>();
                //services.AddScoped<IBlobStorageFileDownloader, AzureBlobStorageFileDownloader>();
            }

            // Register Third Party SrpProcessing Services
            {
                //services.AddScoped<IBackgroundJobsSrpProcessingService, HangfireBackgroundJobsSrpProcessingService>();
            }

            // Register Extra Services
            {
                services.AddScoped<IMessageContextProvider, MessageContextProvider>();
                services.AddScoped<IAuthenticationState, AuthenticationState>();
                services.AddScoped<ISrpProcessors, SrpProcessors>();
                //services.AddScoped<IReportTypeJobDefinitionQueryService, ReportTypeJobDefinitionQueryService>();
            }
        }
    }
}
