using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.RocManagement.Rocs;
using Klee.Domain.Entities.UserManagement.Users;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;

namespace Klee.Domain.Entities.OrganizationManagement.Organizations
{
    public class Organization : DomainEntityAggregateRootBase<long>
    {
        #region PROPERTIES - IDENTIFICATION
        /// <summary>
        /// The unique identifier for the organization
        /// </summary>
        [Required]
        public string OrganizationId { get; internal set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// The display name of the organization
        /// </summary>
        [Required]
        public string Name { get; internal set; } = "";

        /// <summary>
        /// A short code or abbreviation for the organization
        /// </summary>
        [MaxLength(10)]
        public string Code { get; internal set; } = "";

        /// <summary>
        /// A description of the organization's purpose and scope
        /// </summary>
        public string Description { get; internal set; } = "";
        #endregion

        #region PROPERTIES - CONTACT
        /// <summary>
        /// The primary contact email for the organization
        /// </summary>
        [EmailAddress]
        public string ContactEmail { get; internal set; } = "";

        /// <summary>
        /// The primary contact phone number for the organization
        /// </summary>
        [Phone]
        public string ContactPhone { get; internal set; } = "";

        /// <summary>
        /// The physical address of the organization
        /// </summary>
        public string Address { get; internal set; } = "";
        #endregion

        #region PROPERTIES - SYSTEM
        /// <summary>
        /// The software environment on which the Organization is registered
        /// </summary>
        public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

        /// <summary>
        /// Is active when the organization is still in active use
        /// </summary>
        public bool? IsActive { get; internal set; } = true;
        #endregion

        #region PROPERTIES - RELATIONS
        /// <summary>
        /// Collection of vehicles belonging to this organization
        /// </summary>
        public List<Vehicle> Vehicles { get; internal set; } = new ();

        /// <summary>
        /// Collection of operators belonging to this organization
        /// </summary>
        public List<Operator> Operators { get; internal set; } = new ();

        /// <summary>
        /// Collection of ROCs belonging to this organization
        /// </summary>
        public List<Roc> Rocs { get; internal set; } = new ();

        /// <summary>
        /// Collection of users belonging to this organization
        /// </summary>
        public List<UserProfile> Users { get; internal set; } = new ();
        #endregion

        //TODO Link an organization to a user from Azure AD 

        #region CONSTRUCTORS
        public Organization()
        {
        }

        public Organization(string organizationId, string name)
        {
            this.OrganizationId = organizationId;
            this.Name = name;
        }
        #endregion

        #region METHODS - ENTITY
        public override string CreateEntityPartitionKey()
        {
            return this.OrganizationId;
        }

        public override string GetEntityId2()
        {
            return this.OrganizationId;
        }

        public override string GetEntityTypeName()
        {
            return "Organization";
        }
        #endregion
    }
} 