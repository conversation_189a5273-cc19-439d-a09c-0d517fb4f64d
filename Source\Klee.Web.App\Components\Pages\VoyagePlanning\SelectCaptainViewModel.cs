using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class SelectCaptainViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    // Captain data
    public List<AvailableCaptainListItem> AvailableCaptains { get; set; } = new();
    public bool IsLoadingCaptains { get; set; } = false;

    // Voyage criteria (passed from planning component)
    public DateTime VoyageStartDateTime { get; set; }
    public DateTime VoyageEndDateTime { get; set; }
    public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    #endregion

    #region CONSTRUCTORS
    public SelectCaptainViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task LoadAvailableCaptainsAsync()
    {
        IsLoadingCaptains = true;

        var query = new GetAvailableCaptainsForVoyageQuery(await _srpProcessors.GetQueryContextAsync())
        {
            VoyageStartDateTime = VoyageStartDateTime,
            VoyageEndDateTime = VoyageEndDateTime,
            RequiredQualifications = this.RequiredQualifications.ToList()
        };

        AvailableCaptains = (await _srpProcessors.QueryProcessor.ExecuteAsync(query)).ToList();
        
        IsLoadingCaptains = false;
    }

    public void SetVoyageCriteria(DateTime startDateTime, DateTime endDateTime, IEnumerable<QualificationTypeIds> qualifications)
    {
        VoyageStartDateTime = startDateTime;
        VoyageEndDateTime = endDateTime;
        RequiredQualifications = qualifications;
    }
    #endregion
}
