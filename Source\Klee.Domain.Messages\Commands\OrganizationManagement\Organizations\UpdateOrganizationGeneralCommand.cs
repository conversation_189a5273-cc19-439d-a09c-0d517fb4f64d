using System;
using Renoir.Application.Messages.Commands.Common;

namespace Klee.Domain.Messages.Commands.OrganizationManagement.Organizations
{
    public class UpdateOrganizationGeneralCommand : CommandBase
    {

        #region RESULT CLASS
        public class CommandResult
        {
            public long EntityId { get; set; }
        }
        #endregion

        #region PROPERTIES
        public string OrganizationId { get; }
        public string Name { get; }
        public string Code { get; set; } = "";
        public string Description { get; set; } = "";

        // Result
        public CommandResult Result { get; } = new CommandResult();
        #endregion

        #region CONSTRUCTORS
        public UpdateOrganizationGeneralCommand(string organizationId,
                                                string name,
                                                ICommandContext commandContext)
            : base(commandContext)
        {
            this.OrganizationId = organizationId;
            this.Name = name;
        }
        #endregion
    }
}