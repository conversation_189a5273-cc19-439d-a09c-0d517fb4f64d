using System;
using System.Collections.Generic;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Domain.Messages.Queries.VoyagePlanning;

public class GetAvailableCaptainsForVoyageQuery
    : QueryBase<IReadOnlyList<AvailableCaptainListItem>>
{
    #region PROPERTIES
    public DateTime VoyageStartDateTime { get; set; }
    public DateTime VoyageEndDateTime { get; set; }
    public List<QualificationTypeIds> RequiredQualifications { get; set; } = new ();
    #endregion

    #region CONSTRUCTORS
    public GetAvailableCaptainsForVoyageQuery(IQueryContext context)
        : base(context)
    {
    }
    #endregion
}
