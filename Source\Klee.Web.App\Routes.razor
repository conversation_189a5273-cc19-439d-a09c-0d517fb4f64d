﻿@using Klee.Web.App.Shared.Layout.Main
@using Renoir.Web.Razor.Components.DetectPreRendering
@using Microsoft.AspNetCore.Components.Authorization
@using Renoir.Web.Razor.Components.GoogleAnalytics

<PreRenderCascade>
    <CascadingAuthenticationState>
        <Router AppAssembly="typeof(Program).Assembly">
            <Found Context="routeData">
                <!-- Google Analytics -->
                <GoogleAnalyticsTracker />

                <!---->
                <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(MainLayout)" />
            </Found>
            <NotFound>
                <LayoutView Layout="@typeof(MainLayout)">
                    <RMainLayoutBody PageHeaderTitle="Empty Page">
                        <Content>
                            <h3>Sorry, there's nothing at this address.</h3>
                        </Content>
                    </RMainLayoutBody>
                </LayoutView>
            </NotFound>
        </Router>
    </CascadingAuthenticationState>
</PreRenderCascade>